<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <import class="ch.qos.logback.classic.encoder.PatternLayoutEncoder" />
    <import class="ch.qos.logback.core.ConsoleAppender" />
    <springProperty scope="context" name="app_name" source="info.application.name"/>
    <springProperty scope="context" name="env" source="spring.profiles.active"/>

    <springProfile name="!local">
        <appender name="ASYNC_STDOUT" class="ch.qos.logback.classic.AsyncAppender">
            <appender-ref ref="STDOUT" />
            <queueSize>512</queueSize>
            <discardingThreshold>0</discardingThreshold>
            <includeCallerData>false</includeCallerData>
            <neverBlock>true</neverBlock>
        </appender>

        <appender name="STDOUT" class="ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <pattern>
                        <pattern>
                            {
                            "timeStamp": "%d{yyyy-MM-dd HH:mm:ss.SSS}",
                            "logLevel": "%p",
                            "logger": "%logger",
                            "traceId": "%mdc{traceId}",
                            "message": "#tryJson{%message}",
                            "spanId": "%mdc{spanId}",
                            "thread": "%t",
                            "exception": "%ex"
                            }
                        </pattern>
                    </pattern>
                </providers>
            </encoder>
        </appender>


    </springProfile>
    <springProfile name="local">
        <appender name="STDOUT" class="ConsoleAppender">
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>${CONSOLE_LOG_THRESHOLD}</level>
            </filter>
            <encoder>
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
                <charset>${CONSOLE_LOG_CHARSET}</charset>
            </encoder>
        </appender>

        <appender name="ASYNC_STDOUT" class="ch.qos.logback.classic.AsyncAppender">
            <appender-ref ref="STDOUT" />
            <queueSize>512</queueSize>
            <discardingThreshold>0</discardingThreshold>
            <includeCallerData>false</includeCallerData>
            <neverBlock>true</neverBlock>
        </appender>
    </springProfile>

    <root level="INFO">
        <appender-ref ref="ASYNC_STDOUT" />
    </root>
    
    <Logger name="reqResLogger" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_STDOUT" />
    </Logger>
    
    <Logger name="application" level="DEBUG" additivity="false">
        <appender-ref ref="ASYNC_STDOUT" />
    </Logger>
</configuration>