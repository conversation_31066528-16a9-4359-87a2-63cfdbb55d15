common: &default_settings
  high_security: false
  enable_auto_app_naming: false
  enable_auto_transaction_naming: true
  log_level: warning
  audit_mode: false
  log_file_count: 1
  log_limit_in_kbytes: 0
  log_daily: false
  log_file_name: newrelic_agent.log
  application_logging:
    enabled: true
    forwarding:
      enabled: false
    metrics:
      enabled: true
  max_stack_trace_lines: 30
  attributes:
    enabled: true
    include:
      - database.*
      - db.*
      - sql.*
      - jdbc.*
      - hibernate.*
      - connection_pool.*
      - request.parameters.*
      - response.headers.*
      - kafka.*
      - redis.*
      - aws.*
      - feign.*
  transaction_tracer:
    enabled: true
    transaction_threshold: apdex_f
    record_sql: obfuscated
    log_sql: true
    stack_trace_threshold: 0.5
    explain_enabled: true
    explain_threshold: 0.5
    top_n: 20
    database_name_reporting:
      enabled: true
    slow_sql:
      enabled: true
      threshold: 250
      explain_plans:
        enabled: true
        threshold: 250
    transaction_segments:
      enabled: true
      threshold: 100
    segment_attributes:
      enabled: true
  error_collector:
    enabled: true
    ignore_errors:
      - akka.actor.ActorKilledException
      - org.springframework.dao.EmptyResultDataAccessException
      - org.springframework.dao.DataIntegrityViolationException
      - org.hibernate.exception.ConstraintViolationException
      - org.springframework.web.client.HttpClientErrorException
      - org.springframework.web.client.ResourceAccessException
      - org.springframework.web.client.HttpServerErrorException
    ignore_status_codes: [404, 400]
    capture_events: true
    error_grouping:
      enabled: true
      max_groups: 100
  transaction_events:
    enabled: true
    max_samples_stored: 2000
    attributes:
      include:
        - database.*
        - db.*
        - sql.*
        - jdbc.*
        - hibernate.*
        - kafka.*
        - redis.*
        - aws.*
        - feign.*
  distributed_tracing:
    enabled: true
    exclude_newrelic_header: false
    span_events_max_samples_stored: 10000
    span_events_max_samples_per_minute: 1000
  span_events:
    enabled: true
    max_samples_stored: 2000
    attributes:
      enabled: true
      include:
        - database.*
        - db.*
        - sql.*
        - jdbc.*
        - hibernate.*
        - kafka.*
        - redis.*
        - aws.*
        - feign.*
  cross_application_tracer:
    enabled: true
  thread_profiler:
    enabled: true
    max_profile_duration: 300
    max_profile_samples: 1000
  slow_sql:
    enabled: true
  browser_monitoring:
    auto_instrument: true
    page_view_timing:
      enabled: true
    ajax:
      enabled: true
  class_transformer:
    com.newrelic.instrumentation.servlet-user:
      enabled: true
    com.newrelic.instrumentation.spring-aop-2:
      enabled: true
    com.newrelic.instrumentation.jdbc-resultset:
      enabled: true
    com.lumi.rental.core.fleet.repository.*: true
    com.lumi.rental.core.fleet.service.*: true
    com.lumi.rental.core.fleet.controller.*: true
    com.lumi.rental.core.fleet.api.*: true
    classloader_excludes:
      groovy.lang.GroovyClassLoader$InnerLoader,
      org.codehaus.groovy.runtime.callsite.CallSiteClassLoader,
      com.collaxa.cube.engine.deployment.BPELClassLoader,
      org.springframework.data.convert.ClassGeneratingEntityInstantiator$ObjectInstantiatorClassGenerator,
      org.mvel2.optimizers.impl.asm.ASMAccessorOptimizer$ContextClassLoader,
      gw.internal.gosu.compiler.SingleServingGosuClassLoader,
  jfr:
    enabled: true
    audit_logging: true
    recording:
      enabled: true
      duration: 60
      max_age: 3600
      max_size: 100
  labels: