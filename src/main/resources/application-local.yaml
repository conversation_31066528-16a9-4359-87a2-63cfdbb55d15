opensearch:
  password: ${ES_PASSWORD:m*0Sqj^IGp&emkO0RPO$1aS39}
  username: ${ES_USERNAME:admin}
  uris: ${OPENSEARCH_HOST:opensearch-nonprod.lumirental.io:443}

aws:
  url.expiry: ${AWS_URL_EXPIRY:5000}
  key.pair.id: ${AWS_KEY_PAIR:K1BZ6AKLM48WUB}
  pem.file.path: ${AWS_PEM_KEY_PATH:K1BZ6AKLM48WUB.pem}

rest.call.auth.service:
  username: ${AUTHENTICATION_SERVICE_USER_USERNAME:lumi-fleet-service}
  password: ${AUTHENTICATION_SERVICE_USER_PASSWORD:Password@123}

#spring.datasource:
#  password:
#  username: root
#  url: jdbc:mysql://${MYSQL_HOST:localhost}:3306/lumi-core-fleet-service?createDatabaseIfNotExist=true


#spring.datasource:
#  username: mohd.danish
#  url: ***********************************************************************************************************************************************************************************************************************************
#  password: 4ohDUSAlYqp8VAa3d9CB9LF6
#

spring.datasource:
  username: nitin.maurya
  url: *******************************************************************************************************************************************************************************************************************************
  password: 52zYuBHvct49dq2Pm9yN