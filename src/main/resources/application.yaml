management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics,env,loggers,threaddump,heapdump
  endpoint:
    health:
      show-details: always
    metrics:
      access: read_only
    prometheus:
      access: read_only
  metrics:
    enable:
      kafka: true
      jvm: true
      jdbc: true
      hikaricp: true
      redis: true
      thread: true

# AI API configuration
ai:
  api:
    keys: ${AI_API_KEYS:}
    keys.default.limit: ${AI_API_KEYS_DEFAULT_LIMIT:60000}

spring:
  jpa:
    show-sql: 'false'
    generate-ddl: 'false'
    properties:
      hibernate:
        session.events.log.LOG_QUERIES_SLOWER_THAN_MS: '30'
        jdbc.time_zone: UTC
        format_sql: true
        jdbc.timeout: 5000
    hibernate:
      naming-strategy: org.hibernate.cfg.ImprovedNamingStrategy
      ddl-auto: none
  kafka:
    consumer:
      group-id: ${spring.application.name}
      auto-offset-reset: earliest
    bootstrap-servers: ${KAFKA_HOST:localhost:9092}
  datasource:
    testWhileIdle: 'true'
    password: ${SPRING_DATASOURCE_PASSWORD:}
    url: ${SPRING_DATASOURCE_URL:}
    validationQuery: SELECT 1
    username: ${SPRING_DATASOURCE_USERNAME:}
    hikari:
      idle-timeout: ${DB_IDLE_TIME:10000}
      maximum-pool-size: ${DB_POOL:20}
      auto-commit: 'true'
      connection-timeout: ${DB_TIMEOUT:20000}
      max-lifetime: ${DB_MAX_LIFE:30000}
      minimum-idle: ${DB_MIN_IDLE:10}
      register-mbeans: true
  cloud.stream.kafka.binder:
    minPartitionCount: ${KAFKA_PARTITION:1}
    replicationFactor: ${KAFKA_REPLICATION:1}
  application.name: fleet-service
  profiles.active: ${ACTIVE_PROFILE:local}
  data:
    redis:
      port: ${SPRING_REDIS_PORT:6379}
      password: ${SPRING_REDIS_PASSWORD:}
      ssl:
        enabled: ${SPRING_REDIS_SSL:false}
      host: ${SPRING_REDIS_HOST:lumi-dev-redis.hvikmd.0001.mes1.cache.amazonaws.com}
      database: 7
  liquibase:
    contexts: dev,qa,uat,prod
    enabled: 'true'
    change-log: classpath:/db/migration/changelog-master.xml
  threads.virtual.enabled: 'false'
  default.kafka.bootstrap-servers: ${DEFAULT_KAFKA_HOST:localhost:9092}
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      max-file-size: 20MB
      max-request-size: 215MB

aws:
  s3:
    vehicle.document.bucket: ${S3_FLEET_DOCUMENTS_BUCKET:dev-fleet-documents}
    sap.vehicle.document.folder: fleet-documents
    region: ${S3_REGION:me-south-1}
  cdn.content.url: ${AWS_CDN_CONTENT_URL:https://cdn-dev.lumirental.com}
  key.pair.id: ${AWS_KEY_PAIR:}
  pem.file.path: ${AWS_PEM_KEY_PATH:}
  url.expiry: ${AWS_URL_EXPIRY:0}
kafka:
  topic:
    vehicle:
      maintenance.data: SyncVehicleMaintenanceData
      financial.data: SyncVehicleFinancialData
      tracking.data: vehiclelivetrackingdata
    sync:
      vehicle:
        make.data: SyncMake
        model.data: SyncModel
        metadata: SyncVehicleMetaData
        document: SyncVehicleDocument
        tracking.data: SyncTrackingSaferoad
        operational.data: SyncVehicleOperationalData
        reservation.data: SyncVehicleReservationData
  dlt.listen.auto.start: ${DLT_AUTO_START:true}
  listen:
    single.concurrency: ${KAFKA_LISTENER_SINGLE_CONCURRENCY:1}
    concurrency: ${KAFKA_LISTENER_CONCURRENCY:1}
    auto.start: 'true'
info.app:
  version: ${BUILD_REVISION:1.0.0}
  description: An central fleet service for managing vehicles

logging:
  up-stream:
    header: x-domain, x-envoy-original-path, x-b3-traceid, client-id, allowed-branch-ids
    url: /v1/vehicle/tracking, /v1/vehicle
  down-stream:
    header: client-id
    url: /v1/vehicle/track/details
  level:
    org: WARN
    com.lumi: INFO
    root: WARN

opensearch:
  username: ${ES_USERNAME:admin}
  password: ${ES_PASSWORD:admin}
  uris: ${OPENSEARCH_HOST}
api:
  ai.service.base.url: ${AI_SERVICE:https://api-dev.lumirental.com/yaqeen-core-service}
  content.service.base.url: ${CONTENT_SERVICE:https://api-dev.lumirental.com/content-service}
  core.carpro.service.base.url: ${CORE_CAR_PRO_SERVICE:https://api-dev.lumirental.com/core-carpro-service}
  carpro.app:
    name: ${CAR_PRO_APP_NAME:RentProWeb}
    program.name: ${CAR_PRO_APP_PROGRAM_NAME:PreviewImage}
    base.url: ${CAR_PRO_APP_BASE_URL:http://carpro.staging.lumirental.com/}
    service.base.url: ${CAR_PRO_SERVICE:https://api-dev.lumirental.com/carpro-service}
  authentication:
    service.base.url: ${AUTHENTICATION_SERVICE_BASEURL:https://api-dev.lumirental.com/auth-service}
  agreement:
    service.base.url: ${CORE_AGREEMENT_SERVICE:https://api-dev.lumirental.com/core-agreement-service}
  branch:
    service.base.url: ${BRANCH_SERVICE:https://api-dev.lumirental.com/branch-service}
  user:
    service.base.url: ${USER_SERVICE:https://api-dev.lumirental.com/core-user-service}
server:
  forward-headers-strategy: framework
  port: ${SERVICE_PORT:${SERVER_PORT:8099}}
  compression:
    mime-types: text/html, text/xml, text/plain, text/css, text/javascript, application/javascript, application/json
    enabled: 'true'

rest.call.auth.service:
  password: ${AUTHENTICATION_SERVICE_USER_PASSWORD:Password@123}
  username: ${AUTHENTICATION_SERVICE_USER_USERNAME:lumi-fleet-service}

file:
  max.upload.bytes: ${MAX_UPLOAD_BYTES:10000000}
  storage:
    service: ${FILE_STORAGE_SERVICE:AWS}
    local.location: /home/<USER>/Desktop/

config:
  authorization:
    policy-enforcer:
      enable: false
  multitenant:
    tenants:
      lumi:
        keycloak-config:
          config-path: ${LUMI_KEYCLOAK_CONFIG_PATH:src/main/resources/keycloak-lumi.json}
          manager-username: ${LUMI_KEYCLOAK_MANAGER_USERNAME:ucs}
          manager-password: ${LUMI_KEYCLOAK_MANAGER_PASSWORD:123456}
          policy-enforcer-path: ${LUMI_KEYCLOAK_POLICY_ENFORCER_CONFIG_PATH:src/main/resources/policy-enforcer-lumi.json}
        tenant-identifier: LUMI
        token-converter-properties:
          resource-id: account
          principal-attribute: preferred_username
        jwk-set-uri: ${config.multitenant.tenants.lumi.issuer-uri}/protocol/openid-connect/certs
        issuer-uri: ${KEYCLOAK_URL:https://keycloak.dev.lumirental.com}/realms/lumi
      lease:
        issuer-uri: ${KEYCLOAK_URL:https://keycloak.dev.lumirental.com}/realms/lease
        keycloak-config:
          manager-username: ${LEASE_KEYCLOAK_MANAGER_USERNAME:lease-users-manager}
          manager-password: ${LEASE_KEYCLOAK_MANAGER_PASSWORD:123456}
          policy-enforcer-path: ${LEASE_KEYCLOAK_POLICY_ENFORCER_CONFIG_PATH:src/main/resources/policy-enforcer-lease.json}
          config-path: ${LEASE_KEYCLOAK_CONFIG_PATH:src/main/resources/keycloak-lease.json}
        token-converter-properties:
          resource-id: account
          principal-attribute: preferred_username
        jwk-set-uri: ${config.multitenant.tenants.lease.issuer-uri}/protocol/openid-connect/certs
        tenant-identifier: LEASE
    default-tenant-identifier: LUMI

## Swagger Configs
springdoc:
  api-docs:
    path: /v3/api-docs
    default-consumes-media-type: application/json
    default-produces-media-type: application/json
  swagger-ui:
    host: ${SWAGGER_HOST:}
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha

## Slack Configuration
slack:
  enabled: ${SLACK_ENABLED:true}
  webhook:
    url: ${SLACK_WEBHOOK_URL:}

swagger:
  context: ${SWAGGER_CONTEXT:}
  contact:
    url: https://lumirental.com
    email: <EMAIL>
  security:
    option:
      enable: true