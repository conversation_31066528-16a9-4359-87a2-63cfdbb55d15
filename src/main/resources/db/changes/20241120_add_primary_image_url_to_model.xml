<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">
    <changeSet author="mohd.danish" id="add_primary_image_url_to_model">
        <addColumn tableName="model">
            <column name="primary_image_url" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="mohd.danish" id="add_idx_in_nrm">
        <sql>
            ALTER TABLE `lumi-core-fleet-service`.`non_revenue_movement`
            ADD INDEX `idx_plate_no` (`plate_no` ASC) VISIBLE,
            ADD INDEX `idx_status_id` (`status_id` ASC) VISIBLE;
        </sql>
    </changeSet>
    <changeSet author="mohd.danish" id="update_model_table_drop_specification">
        <sql>
            ALTER TABLE `lumi-core-fleet-service`.`model`
            DROP COLUMN `specification`,
            CHANGE COLUMN `primary_image_url` `primary_image_url` VARCHAR(255) NULL DEFAULT NULL AFTER `face_model_id`;
        </sql>
    </changeSet>
</databaseChangeLog>
