package com.lumi.rental.core.fleet.service;

import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.mapper.VehicleFeatureMapper;
import com.lumi.rental.core.fleet.repository.VehicleFeatureRepository;
import com.lumi.rental.core.fleet.response.VehicleFeatureResponse;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

/**
 * Service class handling business logic for vehicle feature operations. Provides methods for CRUD
 * operations and feature management.
 */
@Service
@RequiredArgsConstructor
public class VehicleFeatureService {
  private static final Logger log = LoggerFactory.getLogger("application");

  private final VehicleFeatureMapper vehicleFeatureMapper;
  private final VehicleFeatureRepository repository;

  /**
   * Retrieves paginated vehicle features.
   *
   * @param pageable Pagination configuration
   * @return Paginated list of vehicle feature responses
   */
  public Page<VehicleFeatureResponse> getAllVehicleFeatures(PageRequest pageable) {
    log.info(
        "Fetching all vehicle features - page: {}, size: {}",
        pageable.getPageNumber(),
        pageable.getPageSize());

    Page<VehicleFeatureResponse> response =
        repository.findAll(pageable).map(vehicleFeatureMapper::buildVehicleFeatureResponse);

    log.debug(
        "Found {} vehicle features in page {}",
        response.getNumberOfElements(),
        pageable.getPageNumber());
    return response;
  }

  /**
   * Retrieves a single vehicle feature by its ID.
   *
   * @param vehicleFeatureId ID of the feature to retrieve
   * @return Vehicle feature response
   * @throws BusinessException if feature is not found
   */
  public VehicleFeatureResponse getVehicleFeatureById(Integer vehicleFeatureId) {
    log.info("Fetching vehicle feature by ID: {}", vehicleFeatureId);

    VehicleFeatureResponse response =
        repository
            .findById(vehicleFeatureId)
            .map(vehicleFeatureMapper::buildVehicleFeatureResponse)
            .orElseThrow(
                () -> {
                  log.warn("Vehicle feature with ID {} not found", vehicleFeatureId);
                  return new BusinessException(BaseError.NOT_FOUND, "VehicleFeatureId");
                });

    log.debug("Successfully retrieved vehicle feature: {}", response);
    return response;
  }
}
