package com.lumi.rental.core.fleet.filters;

import com.lumi.rental.core.fleet.service.LoggerService;
import com.lumi.rental.core.fleet.util.DateUtil;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static com.lumi.rental.core.fleet.constants.Constants.DEFAULT_MDC_CLIENT_ID_VALUE;
import static com.lumi.rental.core.fleet.constants.Constants.LogConstants.START_TIME;
import static com.lumi.rental.core.fleet.constants.Constants.LogConstants.START_TIMESTAMP;

@Component
@RequiredArgsConstructor
public class ReqResLoggingFilter implements Filter {
  private static final Logger log = LoggerFactory.getLogger("application");
  private static final String ACTUATOR_ENDPOINTS = "actuator";
  private static final List<String> EXCLUDED_PATHS =
      Arrays.asList("/actuator/health", "/actuator/metrics", "/actuator/prometheus");

  private final LoggerService loggerService;

  @Override
  public void init(FilterConfig config) {
    log.debug("Request Response filter initialized");
  }

  @Override
  public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain)
      throws IOException, ServletException {
    if (!(request instanceof HttpServletRequest httpRequest)
        || !(response instanceof HttpServletResponse)) {
      filterChain.doFilter(request, response);
      return;
    }

    String requestURI = httpRequest.getRequestURI();

    // Skip logging for excluded paths
    if (shouldSkipLogging(requestURI)) {
      filterChain.doFilter(request, response);
      return;
    }

    // Set MDC context
    setMDCContext();

    // Wrap request and response
    ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(httpRequest);
    ContentCachingResponseWrapper responseWrapper =
        new ContentCachingResponseWrapper((HttpServletResponse) response);

    try {
      filterChain.doFilter(requestWrapper, responseWrapper);
    } finally {
      processRequestResponse(requestWrapper, responseWrapper);
    }
  }

  private boolean shouldSkipLogging(String requestURI) {
    return StringUtils.isBlank(requestURI)
        || requestURI.contains(ACTUATOR_ENDPOINTS)
        || EXCLUDED_PATHS.stream().anyMatch(requestURI::startsWith);
  }

  private void setMDCContext() {
    MDC.put(START_TIME, String.valueOf(System.currentTimeMillis()));
    MDC.put(START_TIMESTAMP, DateUtil.getCurrentDateTimeInString());
  }

  private void processRequestResponse(
      ContentCachingRequestWrapper requestWrapper, ContentCachingResponseWrapper responseWrapper) {
    try {
      String requestURI = buildRequestURI(requestWrapper);

      // Log request and response
      loggerService.logRequest(requestWrapper, requestURI);
      copyClientIdHeader(requestWrapper, responseWrapper);
      loggerService.logResponse(responseWrapper, requestURI);

      // Copy response body
      responseWrapper.copyBodyToResponse();
    } catch (IOException ex) {
      log.error("Error processing response: {}", ex.getMessage());
    }
  }

  private String buildRequestURI(ContentCachingRequestWrapper requestWrapper) {
    String queryString = requestWrapper.getQueryString();
    return requestWrapper.getRequestURI()
        + (StringUtils.isBlank(queryString) ? "" : "?" + queryString);
  }

  private void copyClientIdHeader(
      ContentCachingRequestWrapper requestWrapper, ContentCachingResponseWrapper responseWrapper) {
    String clientId = requestWrapper.getHeader(DEFAULT_MDC_CLIENT_ID_VALUE);
    if (StringUtils.isNotBlank(clientId)) {
      responseWrapper.setHeader(DEFAULT_MDC_CLIENT_ID_VALUE, clientId);
    }
  }

  @Override
  public void destroy() {
    log.debug("Request Response filter destroyed");
  }
}
