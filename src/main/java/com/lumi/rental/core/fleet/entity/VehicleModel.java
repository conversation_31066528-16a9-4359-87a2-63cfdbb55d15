package com.lumi.rental.core.fleet.entity;

import com.lumi.rental.core.fleet.dto.Specification;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.List;
import lombok.*;
import org.hibernate.annotations.Type;

@NamedEntityGraph(
    name = "NEG_VEHICLE_MODEL_MAKE",
    attributeNodes = {
      @NamedAttributeNode(value = "vehicleClass"),
      @NamedAttributeNode(value = "make"),
      @NamedAttributeNode(value = "vehicleGroup")
    })
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "model")
public class VehicleModel extends BaseEntity {

  @Column(name = "name_en", unique = true)
  private String nameEn;

  @Column(name = "name_ar")
  private String nameAr;

  @ManyToOne(targetEntity = VehicleMake.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  private VehicleMake make;

  @Column(name = "model_version")
  private String modelVersion;

  @Column(name = "model_series")
  private String modelSeries;

  @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "group_code", referencedColumnName = "code")
  private VehicleGroup vehicleGroup;

  @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "class_id", referencedColumnName = "id")
  private VehicleClass vehicleClass;

  @Column(name = "face_model_id")
  private Integer faceModelId;

  @Column(name = "sap_material_id")
  private String sapMaterialId;

  @Column(name = "material_name")
  private String materialName;

  @Column(name = "primary_image_url")
  private String primaryImageUrl;

  @Column(name = "is_enabled")
  private Boolean enabled;

  @Column(name = "fleet_count")
  private Integer fleetCount;

  @OneToMany(
      mappedBy = "vehicleModel",
      cascade = CascadeType.ALL,
      orphanRemoval = true,
      fetch = FetchType.LAZY)
  private List<ModelImages> modelImages;

  @Type(JsonType.class)
  @Column(name = "specs", columnDefinition = "json")
  private Specification specification;
}
