package com.lumi.rental.core.fleet.enums;

import java.util.List;
import lombok.Getter;

@Getter
public enum StatusType {
  READY(1, "Ready"),
  RENTED(2, "Rented"),
  NEED_PREPARATION(13, "Need Preparation"),
  NRM_OPENED(14, "NRM Opened"),
  OUT_OF_SERVICE(6, "Out Of Service"),
  DISPUTED(15, "Disputed"),
  FOREIGN_CAR_RETURNED(11, "Foreign Car Returned"),
  STOLEN(3, "Stolen"),
  TOTAL_LOSS(12, "Total Loss"),
  IN_SALE_CYCLE(4, "In Sale Cycle"),
  SOLD(7, "Sold"),
  STAFF_USE_AVAILABLE(16, "Staff Use Available"),
  BACKUP(17, "Backup"),

  AT_CUSTOMER(5, "At Customer"),
  AT_FOREIGN_BRANCH(8, "At Foreign Branch"),
  RESERVED(9, "Reserved"),
  PRE_CHECKED_IN(10, "Pre Checked In");

  private final Integer id;
  private final String code;

  StatusType(Integer id, String code) {
    this.id = id;
    this.code = code;
  }

  public static StatusType fromId(Integer id) {
    for (StatusType type : values()) {
      if (type.id.equals(id)) {
        return type;
      }
    }
    return null;
  }

  public static StatusType fromCode(String code) {
    for (StatusType type : values()) {
      if (type.code.equalsIgnoreCase(code)) {
        return type;
      }
    }
    return null;
  }

  public static List<StatusType> getMultilevelStatusTypes() {
    return List.of(NEED_PREPARATION, NRM_OPENED, OUT_OF_SERVICE, DISPUTED, IN_SALE_CYCLE);
  }
}
