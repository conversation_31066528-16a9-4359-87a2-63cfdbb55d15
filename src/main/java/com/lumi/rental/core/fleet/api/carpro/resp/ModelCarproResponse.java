package com.lumi.rental.core.fleet.api.carpro.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Data;

@Data
public class ModelCarproResponse implements Serializable {

  private Integer makeId;
  private String modelName;
  private String modelVersion;
  private String vehicleGroup;
  private String sapMaterialId;
  private String fuelCapacity;

  @JsonProperty("FUEL_TYPE_NAME")
  private String fuelType;
}
