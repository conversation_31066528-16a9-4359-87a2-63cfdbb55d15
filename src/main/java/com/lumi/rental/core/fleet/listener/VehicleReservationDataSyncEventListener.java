package com.lumi.rental.core.fleet.listener;

import com.lumi.rental.core.fleet.dto.VehicleReservationSyncEventDTO;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleReservationDataSyncEventListener {

  private static final Logger log = LoggerFactory.getLogger("application");

  //  @KafkaListener(
  //      topics = {"${kafka.topic.sync.vehicle.reservation.data}"},
  //      groupId = "sync-vehicle-reservation",
  //      concurrency = "${kafka.listen.concurrency}",
  //      containerFactory = "kafkaListenerContainerFactory",
  //      autoStartup = "${kafka.listen.auto.start:true}")
  public void listen(VehicleReservationSyncEventDTO vehicleReservationSyncEventDTO) {
    try {
      log.info(
          "Message received for vehicle reservation data sync-vehicle-reservation {}",
          vehicleReservationSyncEventDTO);
      //      syncService.syncVehicleReservation(vehicleReservationSyncEventDTO);
    } catch (Exception ex) {
      log.error(
          "Error while listening vehicle reservation data sync-vehicle-reservation event", ex);
    }
  }

  //  @DltHandler
  //  public void listenToDLT(
  //      @Header(ORIGINAL_TOPIC) String originalTopic,
  //      @Header(RECEIVED_KEY) String receivedKey,
  //      @Header(EXCEPTION_MESSAGE) String exceptionMessage,
  //      @Header(EXCEPTION_STACKTRACE) String exceptionStackTrace) {
  //    log.info(
  //        "Unable to process message on topic:{} for messageKey:{} , errorMessage:{},
  // exceptionStacktrace:{}",
  //        originalTopic, receivedKey, exceptionMessage, exceptionStackTrace);
  //  }
}
