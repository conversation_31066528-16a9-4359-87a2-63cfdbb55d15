package com.lumi.rental.core.fleet.repository;

import com.lumi.rental.core.fleet.entity.MaintenanceType;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface MaintenanceTypeRepository extends JpaRepository<MaintenanceType, Long> {

  //  @EntityGraph("maintenance-type-all-relation")
  List<MaintenanceType> findAll();

  MaintenanceType findByNameEn(String name);

  @Query(value = "Select t from MaintenanceType t join fetch t.nameEn")
  List<MaintenanceType> findAllMaintenanceTypes();
}
