package com.lumi.rental.core.fleet.service;

import static com.lumi.rental.core.fleet.util.DateUtil.getCurrentDateTimeUTC;
import static com.lumi.rental.core.fleet.util.PageUtil.getPageable;
import static java.util.stream.Collectors.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumi.rental.core.fleet.dto.BookingStatusDTO;
import com.lumi.rental.core.fleet.dto.LocationDTO;
import com.lumi.rental.core.fleet.dto.VehicleHoldDTO;
import com.lumi.rental.core.fleet.entity.*;
import com.lumi.rental.core.fleet.enums.NonRevenueMovementStatus;
import com.lumi.rental.core.fleet.enums.ServiceType;
import com.lumi.rental.core.fleet.enums.StatusType;
import com.lumi.rental.core.fleet.enums.SubServiceType;
import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.mapper.VehicleAvailabilityMapper;
import com.lumi.rental.core.fleet.repository.InspectionRepository;
import com.lumi.rental.core.fleet.repository.VehicleOperationalDataRepository;
import com.lumi.rental.core.fleet.repository.VehicleReservationDataRepository;
import com.lumi.rental.core.fleet.repository.specification.VehicleAvailabilitySpecification;
import com.lumi.rental.core.fleet.request.VehicleSearchRequest;
import com.lumi.rental.core.fleet.response.*;
import com.lumi.rental.core.fleet.util.CacheKeyUtil;
import com.seera.lumi.core.cache.service.CacheService;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleAvailabilityService {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final ObjectMapper objMapper;
  private final CacheService cacheService;
  private final BranchInfoCache branchInfoCache;

  private final VehicleModelServiceV2 modelService;
  private final UserService userService;
  private final VehicleMetadataService metadataService;
  private final InspectionRepository inspectionRepository;
  private final MaintenanceLogService maintenanceLogService;
  private final VehicleAvailabilityMapper vehicleAvailabilityMapper;

  private final VehicleReservationDataRepository reservationDataRepository;
  private final VehicleOperationalDataRepository operationalDataRepository;

  public List<VehicleBasicResponseV2> getVehicleAvailabilityOnBranch(
      Integer branchId, LocalDateTime fromDate, LocalDateTime toDate) {
    Set<String> plateNos =
        reservationDataRepository.getAvailablePlateNosAtBranch(fromDate, toDate, branchId);
    return metadataService.getVehiclesV2(plateNos.stream().toList(), false, false);
  }

  public Page<VehicleResponseV3> allVehicle(VehicleSearchRequest request) {
    VehicleAvailabilitySpecification specification = new VehicleAvailabilitySpecification(request);
    Page<VehicleOperationalData> operationalDataPage =
        operationalDataRepository.findAll(specification, getPageable(request));
    return buildResponseWithBookingStatus(operationalDataPage);
  }

  public Page<VehicleResponseV3> readyVehicle(VehicleSearchRequest request) {
    VehicleAvailabilitySpecification specification = new VehicleAvailabilitySpecification(request);
    Page<VehicleOperationalData> operationalDataPage =
        operationalDataRepository.findAll(specification, getPageable(request));
    return buildResponseWithBookingStatus(operationalDataPage);
  }

  public Page<VehicleBookingResponse> rentedVehicle(VehicleSearchRequest request) {
    return operationalDataRepository
        .findRentedVehiclesWithBookingNumber(
            request.getPlateNo(),
            request.getModelIds(),
            request.getGroupCodes(),
            ServiceType.RENTAL.getId(),
            SubServiceType.RENTAL_FLEET.getId(),
            StatusType.RENTED.getId(),
            request.getCurrentLocationIds(),
            getCurrentDateTimeUTC(),
            getPageable(request))
        .map(
            response -> {
              LocationDTO checkInBranch = response.getCheckInBranch();
              checkInBranch.setName(branchInfoCache.getBranchName(checkInBranch.getLumiBranchId()));
              return response;
            });
  }

  public Page<VehicleResponseV3> needPreparationVehicle(VehicleSearchRequest request) {
    VehicleAvailabilitySpecification specification = new VehicleAvailabilitySpecification(request);
    return operationalDataRepository
        .findAll(specification, getPageable(request))
        .map(vehicleAvailabilityMapper::buildVehicleResponseV3);
  }

  public Page<VehicleNRMResponse> nrmVehicle(VehicleSearchRequest request) {
    return operationalDataRepository
        .findNRMVehiclesWithDetail(
            request.getPlateNo(),
            request.getNrmCheckOutLocationIds(),
            request.getNrmCheckInLocationIds(),
            request.getModelIds(),
            request.getGroupCodes(),
            ServiceType.RENTAL.getId(),
            SubServiceType.RENTAL_FLEET.getId(),
            NonRevenueMovementStatus.OPEN.getId(),
            StatusType.NRM_OPENED.getId(),
            request.getStatusReasonIds(),
            getPageable(request))
        .map(
            response -> {
              LocationDTO checkOutLocation = response.getCheckoutLocation();
              checkOutLocation.setName(
                  branchInfoCache.getBranchName(checkOutLocation.getLumiBranchId()));
              LocationDTO checkInLocation = response.getCheckInLocation();
              checkInLocation.setName(
                  branchInfoCache.getBranchName(checkInLocation.getLumiBranchId()));
              response.setCheckoutLocation(checkOutLocation);
              response.setCheckInLocation(checkInLocation);
              Optional.ofNullable(response.getDriverName())
                  .filter(StringUtils::isNotBlank)
                  .map(userService::findUserBySuccessFactorId)
                  .ifPresent(
                      user ->
                          response.setDriverName(user.getFirstName() + " " + user.getLastName()));
              return response;
            });
  }

  public Page<VehicleResponseV3> oosVehicle(VehicleSearchRequest request) {
    VehicleAvailabilitySpecification specification = new VehicleAvailabilitySpecification(request);
    return operationalDataRepository
        .findAll(specification, getPageable(request))
        .map(vehicleAvailabilityMapper::buildVehicleResponseV3);
  }

  public List<VehicleAvailabilityResponse> getVehicleLiveAvailability(
      VehicleSearchRequest request) {
    // Expand model IDs to include family models if necessary
    if (ObjectUtils.isNotEmpty(request.getModelIds())) {
      List<Integer> faceModelIds = modelService.findFaceModelIdsByIds(request.getModelIds());
      List<Integer> modelFamilyIds = modelService.getModelFamilyIds(faceModelIds);
      log.info(
          "Fetched all family model-ids {} for given models {}",
          modelFamilyIds,
          request.getModelIds());
      request.setModelIds(modelFamilyIds);
    }

    // Fetch operational data based on the request
    VehicleAvailabilitySpecification specification = new VehicleAvailabilitySpecification(request);
    List<VehicleOperationalData> vehicleList = operationalDataRepository.findAll(specification);
    List<VehicleOperationalData> availableVehicleList = removeHoldVehicles(vehicleList);
    if (availableVehicleList.isEmpty()) {
      return Collections.emptyList();
    }

    // Extract unique plate numbers
    Set<String> plateNos =
        availableVehicleList.stream().map(VehicleOperationalData::getPlateNo).collect(toSet());
    //    log.info("Available vehicles based on the criteria {}", plateNos);

    // Fetch vehicle and group metadata
    CompletableFuture<Map<String, Vehicle>> vehicleMetadataFuture =
        CompletableFuture.supplyAsync(() -> metadataService.getVehicleMap(plateNos));

    CompletableFuture<Map<String, Long>> lastInspectionFuture =
        CompletableFuture.supplyAsync(
            () -> inspectionRepository.getLatestInspectionDateByPlateNos(plateNos));

    CompletableFuture<Map<String, Long>> lastMaintenanceFuture =
        CompletableFuture.supplyAsync(
            () -> maintenanceLogService.findLatestMaintenanceDateByPlateNos(plateNos));

    // Wait for all futures to complete
    CompletableFuture.allOf(vehicleMetadataFuture, lastInspectionFuture, lastMaintenanceFuture)
        .join();

    // Get results
    Map<String, Vehicle> vehicleMetadataMap = vehicleMetadataFuture.join();
    Map<String, Long> lastInspectionMap = lastInspectionFuture.join();
    Map<String, Long> lastMaintenanceMap = lastMaintenanceFuture.join();

    // Build responses
    List<VehicleAvailabilityResponse> responseList =
        (availableVehicleList.size() > 500
                ? availableVehicleList.parallelStream()
                : availableVehicleList.stream())
            .map(
                operationalData ->
                    buildResponse(
                        operationalData, vehicleMetadataMap, lastInspectionMap, lastMaintenanceMap))
            .collect(toList());

    // Sort by last check-in if required
    if (request.getSortInList().contains("checkIn")) {
      List<VehicleAvailabilityResponse> sortedList = sortByLastCheckIn(responseList, plateNos);
      return sortedList.stream().limit(request.getPageSize()).toList();
    }

    return responseList.stream().limit(request.getPageSize()).toList();
  }

  public List<VehicleAvailabilityResponse> getSimilarVehicleLiveAvailability(
      VehicleSearchRequest request) {
    // Fetch base model ID and details
    Integer baseModelId = getBaseModelId(request);
    VehicleModelBasicResponseV2 modelBasicResponseV2 =
        modelService.getVehicleModelBasicResponseById(baseModelId);
    if (Objects.isNull(modelBasicResponseV2)
        || Objects.isNull(modelBasicResponseV2.getVehicleGroup())) {
      log.error("Vehicle model or group not found for base model ID: {}", baseModelId);
      return Collections.emptyList();
    }
    // Fetch operational data for similar vehicles
    List<VehicleOperationalData> availableVehicleList =
        fetchOperationalData(request, modelBasicResponseV2);
    if (availableVehicleList.isEmpty()) {
      return Collections.emptyList();
    }

    // Extract unique plate numbers
    Set<String> plateNos =
        availableVehicleList.stream().map(VehicleOperationalData::getPlateNo).collect(toSet());

    // Fetch vehicle and group metadata
    CompletableFuture<Map<String, Vehicle>> vehicleMetadataFuture =
        CompletableFuture.supplyAsync(() -> metadataService.getVehicleMap(plateNos));

    CompletableFuture<Map<String, Long>> lastInspectionFuture =
        CompletableFuture.supplyAsync(
            () -> inspectionRepository.getLatestInspectionDateByPlateNos(plateNos));

    CompletableFuture<Map<String, Long>> lastMaintenanceFuture =
        CompletableFuture.supplyAsync(
            () -> maintenanceLogService.findLatestMaintenanceDateByPlateNos(plateNos));

    // Wait for all futures to complete
    CompletableFuture.allOf(vehicleMetadataFuture, lastInspectionFuture, lastMaintenanceFuture)
        .join();

    // Get results
    Map<String, Vehicle> vehicleMetadataMap = vehicleMetadataFuture.join();
    Map<String, Long> lastInspectionMap = lastInspectionFuture.join();
    Map<String, Long> lastMaintenanceMap = lastMaintenanceFuture.join();

    // Build responses
    List<VehicleAvailabilityResponse> responseList =
        (availableVehicleList.size() > 500
                ? availableVehicleList.parallelStream()
                : availableVehicleList.stream())
            .map(
                operationalData ->
                    buildResponse(
                        operationalData, vehicleMetadataMap, lastInspectionMap, lastMaintenanceMap))
            .collect(toList());

    // Sort by last check-in if required
    if (request.getSortInList().contains("checkIn")) {
      return sortByLastCheckIn(responseList, plateNos).stream()
          .limit(request.getPageSize())
          .collect(toList());
    }
    return responseList.stream().limit(request.getPageSize()).toList();
  }

  public VehicleOperationalData getVehicleOperationalData(String plateNo) {
    return operationalDataRepository
        .findByPlateNo(plateNo)
        .orElseThrow(() -> new BusinessException(BaseError.NOT_FOUND, "Vehicle Operational Data"));
  }

  public void updateVehicleOperationData(VehicleOperationalData vehicleOperationalData) {
    operationalDataRepository.saveAndFlush(vehicleOperationalData);
  }

  public void updateVehicleOperationDataList(
      List<VehicleOperationalData> vehicleOperationalDataList) {
    operationalDataRepository.saveAllAndFlush(vehicleOperationalDataList);
  }

  private VehicleAvailabilityResponse buildResponse(
      VehicleOperationalData operationalData,
      Map<String, Vehicle> vehicleMap,
      Map<String, Long> lastInspectionMap,
      Map<String, Long> lastMaintenanceMap) {
    VehicleAvailabilityResponse response =
        vehicleAvailabilityMapper.buildVehicleAvailabilityResponse(operationalData);
    vehicleAvailabilityMapper.populateVehicleInfo(
        response, vehicleMap.get(operationalData.getPlateNo()));
    response.setLastInspected(lastInspectionMap.get(response.getPlateNo()));
    response.setLastMaintenance(lastMaintenanceMap.get(response.getPlateNo()));
    return response;
  }

  /** Retrieves the base model ID from the request. */
  private Integer getBaseModelId(VehicleSearchRequest request) {
    if (request.getModelIds() == null || request.getModelIds().isEmpty()) {
      throw new BusinessException(BaseError.INVALID_ARG_VALUE, "Model ID is required");
    }
    return request.getModelIds().getFirst();
  }

  /** Fetches operational data for similar vehicles based on the request and base model. */
  private List<VehicleOperationalData> fetchOperationalData(
      VehicleSearchRequest request, VehicleModelBasicResponseV2 modelResponse) {
    request.setGroupCodes(List.of(modelResponse.getVehicleGroup()));
    List<Integer> modelFamilyIds =
        modelService.getModelFamilyIds(List.of(modelResponse.getFaceModelId()));
    request.setNotModelIds(modelFamilyIds);
    request.setModelIds(Collections.emptyList());
    request.setStatusIds(List.of(StatusType.READY.getId()));
    VehicleAvailabilitySpecification specification = new VehicleAvailabilitySpecification(request);
    List<VehicleOperationalData> vehicleList = operationalDataRepository.findAll(specification);
    return removeHoldVehicles(vehicleList);
  }

  /** Removes vehicles that are on hold from the provided list. */
  private List<VehicleOperationalData> removeHoldVehicles(
      List<VehicleOperationalData> vehicleList) {

    // Generate cache keys for all plate numbers
    List<String> plateNoCacheKeys =
        vehicleList.stream()
            .map(VehicleOperationalData::getPlateNo)
            .map(CacheKeyUtil::getKeyForHoldReservation)
            .collect(toList());

    // Fetch from cache and identify held vehicles
    Set<String> holdPlateNos =
        cacheService.get(plateNoCacheKeys).stream()
            .filter(Objects::nonNull)
            .map(
                holdRefObject ->
                    objMapper.convertValue(holdRefObject, VehicleHoldDTO.class).getPlateNo())
            .filter(Objects::nonNull)
            .collect(toSet());

    if (!holdPlateNos.isEmpty())
      log.info("Removing hold vehicle from live availability {}", holdPlateNos);

    // Filter out held vehicles
    List<VehicleOperationalData> result =
        vehicleList.stream()
            .filter(vehicle -> !holdPlateNos.contains(vehicle.getPlateNo()))
            .collect(toList());
    return result;
  }

  /** Sorts the availability responses by the last check-in date. */
  private List<VehicleAvailabilityResponse> sortByLastCheckIn(
      List<VehicleAvailabilityResponse> records, Set<String> plateNos) {
    // Fetch the latest reservation data and map by plate number
    Map<String, Long> reservationMap =
        reservationDataRepository.findLatestReservationDateByPlateNos(plateNos);

    // Update responses with the last check-in date
    records.forEach(
        record -> {
          record.setLastCheckIn(reservationMap.get(record.getPlateNo()));
        });

    // Sort records by last check-in date (nulls last)
    List<VehicleAvailabilityResponse> sortedList = new ArrayList<>(records);
    sortedList.sort(
        Comparator.comparing(
            VehicleAvailabilityResponse::getLastCheckIn,
            Comparator.nullsLast(Comparator.naturalOrder())));
    return sortedList;
  }

  private Page<VehicleResponseV3> buildResponseWithBookingStatus(
      Page<VehicleOperationalData> dataPage) {
    // Step 1: Collect all plateNos and their cache keys
    List<String> cacheKeys =
        dataPage.getContent().stream()
            .map(opsData -> CacheKeyUtil.getKeyForHoldReservation(opsData.getPlateNo()))
            .collect(Collectors.toList());

    // Step 2: Bulk fetch from cache
    Map<String, VehicleHoldDTO> cacheResults =
        cacheService.get(cacheKeys).stream()
            .filter(Objects::nonNull) // Filter out nulls
            .map(x -> objMapper.convertValue(x, VehicleHoldDTO.class))
            .collect(Collectors.toMap(VehicleHoldDTO::getPlateNo, Function.identity()));

    // Step 3: Map results back to VehicleResponseV3
    return dataPage.map(
        opsData -> {
          VehicleResponseV3 response = vehicleAvailabilityMapper.buildVehicleResponseV3(opsData);
          response.setBookingStatus(buildBookingStatusDTO(cacheResults.get(opsData.getPlateNo())));
          return response;
        });
  }

  private BookingStatusDTO buildBookingStatusDTO(VehicleHoldDTO vehicleHoldDTO) {
    return new BookingStatusDTO(
        vehicleHoldDTO != null,
        vehicleHoldDTO != null ? vehicleHoldDTO.getCacheReferenceKey() : null);
  }
}
