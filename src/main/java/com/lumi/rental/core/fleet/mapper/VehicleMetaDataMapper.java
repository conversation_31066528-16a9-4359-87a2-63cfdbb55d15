package com.lumi.rental.core.fleet.mapper;

import com.lumi.rental.core.fleet.dto.*;
import com.lumi.rental.core.fleet.entity.*;
import com.lumi.rental.core.fleet.response.VehicleBasicResponseV2;
import com.lumi.rental.core.fleet.response.VehicleOperationResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = "spring",
    uses = {VehicleModelMapper.class, FuelMapper.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VehicleMetaDataMapper {

  @Mapping(target = "modelYear", source = "year")
  VehicleOperationResponse buildVehicleResponse(Vehicle vehicle);

  @Mapping(target = "modelYear", source = "year")
  VehicleBasicResponseV2 buildVehicleBasicResponseV2(Vehicle vehicle);

  VehicleFinancialDTO buildVehicleFinancialDTO(VehicleFinancialInfo record);

  @Mapping(target = "vehicleStatus", source = "vehicleStatus")
  @Mapping(target = "serviceType", source = "serviceTypeId")
  @Mapping(target = "subServiceType", source = "subServiceTypeId")
  VehicleOperationDTO buildVehicleOperationDTO(VehicleOperationalData record);

  @Mapping(target = "lastInspected", source = "createdOn")
  VehicleInspectionDTO buildVehicleInspectionDTO(InspectionReport inspectionReport);

  default VehicleStatusDTO buildVehicleStatusDTO(VehicleStatusInfo vehicleStatusInfo) {
    if (vehicleStatusInfo != null) {
      return new VehicleStatusDTO(
          vehicleStatusInfo.getStatusId(),
          vehicleStatusInfo.getSubStatusId(),
          vehicleStatusInfo.getDate());
    } else {
      return new VehicleStatusDTO();
    }
  }

  default String getVehicleGroup(VehicleGroup vehicleGroup) {
    return vehicleGroup.getCode();
  }
}
