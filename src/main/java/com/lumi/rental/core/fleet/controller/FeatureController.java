package com.lumi.rental.core.fleet.controller;

import com.lumi.rental.core.fleet.request.BasePageRequest;
import com.lumi.rental.core.fleet.response.VehicleFeatureResponse;
import com.lumi.rental.core.fleet.service.VehicleFeatureService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for managing vehicle features. Provides endpoints for CRUD operations on vehicle
 * features.
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/feature")
public class FeatureController {
  private static final Logger log = LoggerFactory.getLogger("application");
  private final VehicleFeatureService featureService;

  @GetMapping
  public ResponseEntity<Page<VehicleFeatureResponse>> getAllVehicleFeatures(
      @Parameter(description = "Pagination parameters") final BasePageRequest request) {

    log.info(
        "Fetching all vehicle features - page: {}, size: {}",
        request.getPageNumber(),
        request.getPageSize());

    PageRequest pageRequest = PageRequest.of(request.getPageNumber(), request.getPageSize());

    Page<VehicleFeatureResponse> response = featureService.getAllVehicleFeatures(pageRequest);
    log.debug(
        "Found {} vehicle features on page {}",
        response.getNumberOfElements(),
        request.getPageNumber());

    return ResponseEntity.ok(response);
  }

  @GetMapping("/{id}")
  public ResponseEntity<VehicleFeatureResponse> getVehicleFeatureById(
      @Parameter(description = "ID of the vehicle feature to retrieve", required = true)
          @PathVariable("id")
          final Integer vehicleFeatureId) {

    log.info("Fetching vehicle feature with ID: {}", vehicleFeatureId);

    VehicleFeatureResponse response = featureService.getVehicleFeatureById(vehicleFeatureId);
    log.debug("Found vehicle feature: {}", response);

    return ResponseEntity.ok(response);
  }
}
