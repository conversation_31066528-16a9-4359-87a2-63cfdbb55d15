package com.lumi.rental.core.fleet.entity.es;

import jakarta.persistence.Id;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.opensearch.common.geo.GeoPoint;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

@Getter
@Setter
@Accessors(chain = true)
@Document(indexName = "vehicle_tracking_history")
public class ESVehicleTrackingInfoHistory {

  @Id private String id;

  @Field(type = FieldType.Keyword)
  private String plateNo;

  private Double latitude;
  private Double longitude;
  private Long mileage;
  private Boolean engineStatus;

  @Field(
      name = "recordDateTime",
      type = FieldType.Date,
      format = DateFormat.date_hour_minute_second)
  private LocalDateTime recordDateTime;

  private Integer vehicleID;
  private String displayName;
  private Integer speedLimit;
  private Integer literPer100KM;
  private Integer speed;
  private Integer direction;
  private GeoPoint location;
}
