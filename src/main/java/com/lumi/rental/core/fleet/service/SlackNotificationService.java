package com.lumi.rental.core.fleet.service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@RequiredArgsConstructor
public class SlackNotificationService {

  private static final Logger log = LoggerFactory.getLogger("application");
  private static final String DEFAULT_WEBHOOK =
      "*********************************************************************************";
  private final RestTemplate restTemplate;

  @Value("${slack.webhook.url:}")
  private String slackWebhookUrl;

  @Value("${spring.profiles.active:local}")
  private String activeProfile;

  public void sendNotification(String message) {
    String webhookUrl = slackWebhookUrl.isEmpty() ? DEFAULT_WEBHOOK : slackWebhookUrl;

    try {
      Map<String, Object> payload = createSlackPayload(message);
      sendToSlack(payload, webhookUrl);
      log.info("Validation report sent to Slack successfully");
    } catch (Exception e) {
      log.error("Failed to send notification to Slack", e);
    }
  }

  private Map<String, Object> createSlackPayload(String message) {
    Map<String, Object> payload = new HashMap<>();
    payload.put("text", createHeader());
    payload.put("username", "Fleet Data Validator");
    payload.put("icon_emoji", ":rotating_light:");
    payload.put("attachments", parseReportToAttachments(message));
    return payload;
  }

  private String createHeader() {
    return String.format(
        "*LUMI FLEET DATA VALIDATION REPORT*\n" + "*Generated:* %s | *Environment:* %s\n\n",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
        activeProfile.toUpperCase());
  }

  private List<Map<String, Object>> parseReportToAttachments(String message) {
    String[] lines = message.split("\n");
    ReportData reportData = extractReportData(lines);

    List<Map<String, Object>> attachments = new ArrayList<>();

    if (!reportData.failedChecks.isEmpty()) {
      attachments.add(createFailedChecksAttachment(reportData));
    }

    if (!reportData.passedChecks.isEmpty()) {
      attachments.add(createPassedChecksAttachment(reportData));
    }

    if (reportData.hasValidSummary()) {
      attachments.add(createSummaryAttachment(reportData));
    }

    return attachments;
  }

  private ReportData extractReportData(String[] lines) {
    ReportData data = new ReportData();
    boolean inDataSection = false;

    for (String line : lines) {
      if (line.contains("VALIDATION CHECK") || line.contains("─")) {
        inDataSection = line.contains("─");
        continue;
      }

      if (line.startsWith("SUMMARY:")) {
        data.parseSummary(line);
        break;
      }

      if (inDataSection && !line.trim().isEmpty() && line.contains("|")) {
        data.processDataLine(line);
      }
    }

    return data;
  }

  private Map<String, Object> createFailedChecksAttachment(ReportData data) {
    Map<String, Object> attachment = new HashMap<>();
    attachment.put("color", "danger");
    attachment.put("title", String.format("FAILED (%d)", data.totalFailed));
    attachment.put("text", createTable(data.failedChecks));
    return attachment;
  }

  private Map<String, Object> createPassedChecksAttachment(ReportData data) {
    Map<String, Object> attachment = new HashMap<>();
    attachment.put("color", "good");
    attachment.put("title", String.format("PASSED (%d)", data.totalPassed));
    attachment.put("text", createTable(data.passedChecks));
    return attachment;
  }

  private Map<String, Object> createSummaryAttachment(ReportData data) {
    Map<String, Object> attachment = new HashMap<>();
    attachment.put("color", data.getSummaryColor());
    attachment.put("title", "SUMMARY");
    attachment.put("text", data.createSummaryTable());
    return attachment;
  }

  private String createTable(List<String> rows) {
    StringBuilder table = new StringBuilder();
    table.append("```\n");
    table.append(String.format("%-42s | %-8s | %-10s\n", "QUERY", "COUNT", "PRIORITY"));
    table.append("─".repeat(70)).append("\n");
    rows.forEach(row -> table.append(row).append("\n"));
    table.append("```");
    return table.toString();
  }

  private void sendToSlack(Map<String, Object> payload, String webhookUrl) {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    HttpEntity<Map<String, Object>> request = new HttpEntity<>(payload, headers);

    ResponseEntity<String> response = restTemplate.postForEntity(webhookUrl, request, String.class);

    if (!response.getStatusCode().is2xxSuccessful()) {
      log.error("Failed to send message to Slack. Status: {}", response.getStatusCode());
    }
  }

  // Inner class to hold report data
  private static class ReportData {
    List<String> failedChecks = new ArrayList<>();
    List<String> passedChecks = new ArrayList<>();
    int totalFailed = 0;
    int totalPassed = 0;
    int totalIssues = 0;

    void processDataLine(String line) {
      String[] parts = line.split("\\|");
      if (parts.length >= 4) {
        String checkName = parts[0].trim();
        String count = parts[1].trim();
        String priority = parts[2].trim();
        String status = parts[3].trim();

        String formattedLine = String.format("%-42s | %-8s | %-10s", checkName, count, priority);

        if (status.contains("FAILED")) {
          failedChecks.add(formattedLine);
        } else if (status.contains("PASSED")) {
          passedChecks.add(formattedLine);
        }
      }
    }

    void parseSummary(String summaryLine) {
      String[] parts = summaryLine.split("\\|");
      for (String part : parts) {
        String cleanPart = part.replaceAll("[^0-9]", "");
        if (!cleanPart.isEmpty()) {
          int value = Integer.parseInt(cleanPart);
          if (part.contains("Total Issues")) totalIssues = value;
          else if (part.contains("Failed Checks")) totalFailed = value;
          else if (part.contains("Passed Checks")) totalPassed = value;
        }
      }
    }

    boolean hasValidSummary() {
      return totalFailed > 0 || totalPassed > 0;
    }

    String getSummaryColor() {
      return totalFailed == 0 ? "#36a64f" : totalFailed <= 3 ? "#ff9500" : "#ff0000";
    }

    String getHealthStatus() {
      return totalFailed == 0 ? "HEALTHY" : totalFailed <= 3 ? "NEEDS ATTENTION" : "CRITICAL";
    }

    double getHealthPercentage() {
      return totalPassed + totalFailed > 0
          ? (double) totalPassed / (totalPassed + totalFailed) * 100
          : 100;
    }

    String createSummaryTable() {
      return String.format(
          "```\n"
              + "╔═══════════════════════════════════════════════════════════╗\n"
              + "║                    SUMMARY REPORT                         ║\n"
              + "╠═══════════════════════════════════════════════════════════╣\n"
              + "║ Health Status     │ %-37s ║\n"
              + "║ Health Score      │ %-37s ║\n"
              + "║ Total Issues      │ %-37s ║\n"
              + "║ Failed Checks     │ %-37s ║\n"
              + "║ Passed Checks     │ %-37s ║\n"
              + "╚═══════════════════════════════════════════════════════════╝\n"
              + "```",
          getHealthStatus(),
          String.format("%.1f%%", getHealthPercentage()),
          totalIssues,
          totalFailed,
          totalPassed);
    }
  }
}
