package com.lumi.rental.core.fleet.service;

import static com.seera.lumi.core.fleet.utils.ApplicationUtil.calculatePercentage;

import com.lumi.rental.core.fleet.action.CountVehicleHandlerFactory;
import com.lumi.rental.core.fleet.enums.VehicleCountType;
import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.repository.es.ESVehicleRepository;
import com.lumi.rental.core.fleet.request.VehicleCountRequest;
import com.lumi.rental.core.fleet.response.VehicleCountResponse;
import com.seera.lumi.core.fleet.dto.*;
import com.seera.lumi.core.fleet.enums.VehicleSplitKeyName;
import com.seera.lumi.core.fleet.vo.GroupedData;
import com.seera.lumi.core.fleet.vo.KeyCount;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleDashboardService {

  private final VehicleTrackerService trackerService;
  private final ESVehicleRepository esVehicleRepository;
  private final CountVehicleHandlerFactory countVehicleHandlerFactory;

  public VehicleCountResponse getVehiclesCount(VehicleCountRequest request) {
    return countVehicleHandlerFactory
        .getHandler(request.getCountType())
        .map(handler -> handler.execute(request))
        .orElseThrow(() -> new BusinessException(BaseError.BAD_REQUEST_WITH_REASON));
  }

  public VehicleSplitResponseDTO getVehiclesSplitPercentage(
      Set<String> plateNos, VehicleSplitKeyName keyName) {
    VehicleSplitResponseDTO responseDTO = new VehicleSplitResponseDTO();
    GroupedData groupedData = esVehicleRepository.fetchVehicleGroupByKey(plateNos, keyName);
    responseDTO.setDataList(
        groupedData.getData().stream()
            .map(keyData -> prepareVehicleKeyWiseStats(keyData, groupedData.getTotalDocCount()))
            .toList());
    return responseDTO;
  }

  public VehicleCrossedSpeedLimitListResponseDTO getVehiclesCrossedSpeedLimit(
      VehicleSpeedLimitRequestDTO request) {
    return trackerService.getVehicleBreachedSpeedLimitNTimesInSelectedMonth(
        request.getPlateNumbers(), request.getBreachCount(), request.getNoOfMonths());
  }

  private VehicleKeyWiseStats prepareVehicleKeyWiseStats(KeyCount keyData, long totalCount) {
    VehicleCountResponse countResponseDTO =
        countVehicleHandlerFactory
            .getHandler(VehicleCountType.RUNNING_STATUS_COUNT)
            .map(service -> service.execute(new VehicleCountRequest(keyData.getDataList())))
            .orElse(new VehicleCountResponse());
    return new VehicleKeyWiseStats()
        .setKey(keyData.getKey())
        .setKeyCount(keyData.getCount())
        .setValue(calculatePercentage(keyData.getCount(), totalCount))
        .setIdle(countResponseDTO.getIdle())
        .setRunning(countResponseDTO.getRunning())
        .setRunningPercent(calculatePercentage(countResponseDTO.getRunning(), keyData.getCount()));
  }
}
