package com.lumi.rental.core.fleet.service;

import static com.seera.lumi.core.fleet.constants.CacheConstants.*;

import com.lumi.rental.core.fleet.dto.CountVO;
import com.lumi.rental.core.fleet.entity.VehicleClass;
import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.mapper.VehicleClassMapper;
import com.lumi.rental.core.fleet.repository.VehicleClassRepository;
import com.lumi.rental.core.fleet.request.CreateUpdateVehicleClassRequest;
import com.lumi.rental.core.fleet.response.VehicleClassResponse;
import com.seera.lumi.core.cache.service.CacheService;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

/**
 * Service class for managing vehicle classes. Provides methods for retrieving, creating, and
 * updating vehicle classes, with caching and exception handling for improved performance and
 * reliability.
 */
@Service
@RequiredArgsConstructor
public class VehicleClassService {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final CacheService cacheService;
  private final VehicleClassMapper vehicleClassMapper;
  private final VehicleClassRepository repository;

  /**
   * Retrieves a paginated list of all vehicle classes, with vehicle counts mapped to each class.
   * Results are cached for improved performance.
   *
   * @param pageable Pagination information (page number and size).
   * @return A paginated list of {@link VehicleClassResponse}.
   */
  @Cacheable(
      cacheNames = CLASS_RESPONSE_ALL,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public Page<VehicleClassResponse> getAllVehicleClass(PageRequest pageable) {
    log.info("Fetching all vehicle classes with pagination: {}", pageable);
    Map<Integer, Long> vehicleCountMap = findVehicleCountByClass();
    return repository
        .findAll(pageable)
        .map(
            vehicleClass ->
                vehicleClassMapper.buildVehicleClassResponse(
                    vehicleClass, vehicleCountMap.get(vehicleClass.getId())));
  }

  /**
   * Retrieves a specific vehicle class by its ID, with the associated vehicle count. Results are
   * cached for improved performance.
   *
   * @param vehicleClassId The ID of the vehicle class to retrieve.
   * @return The {@link VehicleClassResponse} for the specified ID.
   * @throws BusinessException If the vehicle class is not found.
   */
  @Cacheable(
      cacheNames = CLASS_RESPONSE_BY_ID,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public VehicleClassResponse getVehicleClassById(Integer vehicleClassId) {
    log.info("Fetching vehicle class by ID: {}", vehicleClassId);
    Map<Integer, Long> vehicleCountMap = findVehicleCountByClass();
    return repository
        .findById(vehicleClassId)
        .map(
            vehicleClass ->
                vehicleClassMapper.buildVehicleClassResponse(
                    vehicleClass, vehicleCountMap.get(vehicleClass.getId())))
        .orElseThrow(() -> new BusinessException(BaseError.NOT_FOUND, "VehicleClassId"));
  }

  /**
   * Creates a new vehicle class with the provided details.
   *
   * @param request The request containing details for the new vehicle class.
   * @return The created {@link VehicleClassResponse}.
   * @throws BusinessException If a vehicle class with the same code already exists.
   */
  public VehicleClassResponse createVehicleClass(CreateUpdateVehicleClassRequest request) {
    log.info("Creating a new vehicle class with request: {}", request);
    VehicleClass vehicleClass = vehicleClassMapper.buildVehicleClass(request);
    try {
      VehicleClass savedVehicleClass = repository.saveAndFlush(vehicleClass);
      clearCache();
      return vehicleClassMapper.buildVehicleClassResponse(savedVehicleClass, 0L);
    } catch (DataIntegrityViolationException ex) {
      log.error("VehicleClass with code {} already exists.", request.getName().getEn());
      throw new BusinessException(BaseError.ALREADY_EXISTS, "VehicleClass-Code");
    }
  }

  /**
   * Updates an existing vehicle class with the provided details.
   *
   * @param id The ID of the vehicle class to update.
   * @param request The request containing updated details for the vehicle class.
   * @return The updated {@link VehicleClassResponse}.
   * @throws BusinessException If the vehicle class is not found.
   */
  public VehicleClassResponse updateVehicleClass(int id, CreateUpdateVehicleClassRequest request) {
    log.info("Updating vehicle class with ID: {} and request: {}", id, request);
    return repository
        .findById(id)
        .map(
            existingVehicleClass -> {
              VehicleClass updatedVehicleClass = vehicleClassMapper.buildVehicleClass(request);
              updatedVehicleClass.setId(id);
              VehicleClass savedVehicleClass = repository.saveAndFlush(updatedVehicleClass);
              clearCache();
              return vehicleClassMapper.buildVehicleClassResponse(savedVehicleClass, 0L);
            })
        .orElseThrow(() -> new BusinessException(BaseError.NOT_FOUND, "VehicleClassId"));
  }

  /**
   * Retrieves a map of vehicle counts by class ID. Results are cached for improved performance.
   *
   * @return A map where the key is the vehicle class ID and the value is the count of vehicles in
   *     that class.
   */
  @Cacheable(
      cacheNames = CLASS_VEHICLE_COUNT,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public Map<Integer, Long> findVehicleCountByClass() {
    log.info("Fetching vehicle counts by class");
    return repository.findVehicleCountByClass().stream()
        .collect(Collectors.toMap(CountVO::getId, CountVO::getCount));
  }

  private void clearCache() {
    cacheService.clearAllKeys(CLASS_CACHE_PREFIX + "*");
  }
}
