package com.lumi.rental.core.fleet.entity;

import jakarta.persistence.*;
import lombok.*;

@NamedEntityGraph(
    name = "NEG_VEHICLE_MODEL_FEATURE",
    attributeNodes = {@NamedAttributeNode(value = "featureValue")})
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "model_feature")
@EqualsAndHashCode(callSuper = true)
public class ModelFeature extends BaseEntity {

  @Column(name = "model_id", nullable = false)
  private Integer modelId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "feature_value_id", nullable = false)
  private VehicleFeatureValue featureValue;

  @Column(name = "is_enabled", nullable = false)
  private Boolean enabled;

  @Column(name = "is_primary", nullable = false)
  private Boolean primary;
}
