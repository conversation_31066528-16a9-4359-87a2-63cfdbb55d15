package com.lumi.rental.core.fleet.controller;

import static org.springframework.http.ResponseEntity.ok;

import com.lumi.rental.core.fleet.response.VehicleTrackingResponse;
import com.lumi.rental.core.fleet.service.VehicleTrackerService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/v1/vehicle/track")
public class VehicleTrackingController {

  private final VehicleTrackerService trackerService;

  @GetMapping("/details")
  public ResponseEntity<VehicleTrackingResponse> getVehicleTrackingDetails(
      @RequestParam String plateNo) {
    return ok(trackerService.getVehicle(plateNo));
  }
}
