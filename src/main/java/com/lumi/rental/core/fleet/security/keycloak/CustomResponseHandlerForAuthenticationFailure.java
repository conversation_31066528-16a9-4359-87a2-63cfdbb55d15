package com.lumi.rental.core.fleet.security.keycloak;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.LumiCoreErrResponseMdl;
import com.lumi.rental.core.fleet.util.LogUtil;
import io.micrometer.tracing.Tracer;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.server.resource.BearerTokenError;
import org.springframework.security.oauth2.server.resource.BearerTokenErrorCodes;
import org.springframework.security.oauth2.server.resource.authentication.AbstractOAuth2TokenAuthenticationToken;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Primary
@Component
@RequiredArgsConstructor
public class CustomResponseHandlerForAuthenticationFailure
    implements AccessDeniedHandler, AuthenticationEntryPoint {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final ObjectMapper objectMapper;
  private final Tracer tracer;

  private String realmName;

  public static String computeWWWAuthenticateHeaderValue(Map<String, String> parameters) {
    StringBuilder wwwAuthenticate = new StringBuilder();
    wwwAuthenticate.append("Bearer");
    if (!parameters.isEmpty()) {
      wwwAuthenticate.append(" ");
      int i = 0;
      for (Map.Entry<String, String> entry : parameters.entrySet()) {
        wwwAuthenticate.append(entry.getKey()).append("=\"").append(entry.getValue()).append("\"");
        if (i != parameters.size() - 1) {
          wwwAuthenticate.append(", ");
        }
        i++;
      }
    }
    return wwwAuthenticate.toString();
  }

  /**
   * Collect error details from the provided parameters and format according to RFC 6750,
   * specifically {@code error}, {@code error_description}, {@code error_uri}, and {@code scope}.
   *
   * @param request that resulted in an <code>AuthenticationException</code>
   * @param response so that the user agent can begin authentication
   * @param authException that caused the invocation
   */
  @Override
  public void commence(
      HttpServletRequest request,
      HttpServletResponse response,
      AuthenticationException authException)
      throws IOException {
    HttpStatus status = HttpStatus.UNAUTHORIZED;
    Map<String, String> parameters = new LinkedHashMap<>();
    if (this.realmName != null) {
      parameters.put("realm", this.realmName);
    }
    if (authException instanceof OAuth2AuthenticationException authenticationException) {
      OAuth2Error error = authenticationException.getError();
      parameters.put("error", error.getErrorCode());
      if (StringUtils.hasText(error.getDescription())) {
        parameters.put("error_description", error.getDescription());
      }
      if (StringUtils.hasText(error.getUri())) {
        parameters.put("error_uri", error.getUri());
      }
      if (error instanceof BearerTokenError bearerTokenError) {
        if (StringUtils.hasText(bearerTokenError.getScope())) {
          parameters.put("scope", bearerTokenError.getScope());
        }
        status = bearerTokenError.getHttpStatus();
      }
    }
    String wwwAuthenticate = computeWWWAuthenticateHeaderValue(parameters);
    response.addHeader(HttpHeaders.WWW_AUTHENTICATE, wwwAuthenticate);
    response.setStatus(status.value());
    LumiCoreErrResponseMdl responseMdl = BaseError.UNAUTHORIZED.httpResponseMdl();
    responseMdl.setReqId(LogUtil.getTraceId(tracer));
    response.getOutputStream().println(this.objectMapper.writeValueAsString(responseMdl));
    response.setContentType(MediaType.APPLICATION_JSON_VALUE);
    log.info("Unauthorized access for: {}", request.getRequestURI());
  }

  @Override
  public void handle(
      HttpServletRequest request,
      HttpServletResponse response,
      AccessDeniedException accessDeniedException)
      throws IOException {
    Map<String, String> parameters = new LinkedHashMap<>();
    if (this.realmName != null) {
      parameters.put("realm", this.realmName);
    }
    if (request.getUserPrincipal() instanceof AbstractOAuth2TokenAuthenticationToken) {
      parameters.put("error", BearerTokenErrorCodes.INSUFFICIENT_SCOPE);
      parameters.put(
          "error_description",
          "The request requires higher privileges than provided by the access token.");
      parameters.put("error_uri", "https://tools.ietf.org/html/rfc6750#section-3.1");
    }
    String wwwAuthenticate = computeWWWAuthenticateHeaderValue(parameters);
    response.addHeader(HttpHeaders.WWW_AUTHENTICATE, wwwAuthenticate);
    response.setStatus(HttpStatus.FORBIDDEN.value());
    LumiCoreErrResponseMdl responseMdl = BaseError.ACCESS_DENIED.httpResponseMdl();
    responseMdl.setReqId(LogUtil.getTraceId(tracer));
    response.getOutputStream().println(objectMapper.writeValueAsString(responseMdl));
    response.setContentType(MediaType.APPLICATION_JSON_VALUE);
    log.info("Access denied for: {}", request.getRequestURI());
  }

  /**
   * Set the default realm name to use in the bearer token error response
   *
   * @param realmName
   */
  public void setRealmName(String realmName) {
    this.realmName = realmName;
  }
}
