package com.lumi.rental.core.fleet.entity;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class Addon extends BaseEntity {

  @Column(nullable = false, unique = true, length = 32)
  private String code;

  @Column(name = "name_en", length = 64)
  private String nameEn;

  @Column(name = "name_ar", length = 64)
  private String nameAr;

  @Column(name = "description_en", length = 128)
  private String descriptionEn;

  @Column(name = "description_ar", length = 128)
  private String descriptionAr;

  @Column(name = "image_url", length = 128)
  private String imageUrl;

  @Column(name = "is_enabled", nullable = false)
  private Boolean enabled;
}
