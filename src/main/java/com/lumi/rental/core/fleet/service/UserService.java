package com.lumi.rental.core.fleet.service;

import com.lumi.rental.core.fleet.api.user.UserServiceClient;
import com.lumi.rental.core.fleet.api.user.resp.UserResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserService {
  private final UserServiceClient userServiceClient;

  @Autowired
  public UserService(UserServiceClient userServiceClient) {
    this.userServiceClient = userServiceClient;
  }

  public UserResponseDTO findUserBySuccessFactorId(String successFactorId) {
    return userServiceClient.getUserBySuccessFactorId(successFactorId).getData().stream()
        .findFirst()
        .orElse(null);
  }
}
