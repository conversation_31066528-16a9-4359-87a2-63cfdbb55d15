package com.lumi.rental.core.fleet.controller;

import static org.springframework.http.ResponseEntity.ok;

import com.lumi.rental.core.fleet.enums.*;
import com.lumi.rental.core.fleet.request.VehicleSearchRequest;
import com.lumi.rental.core.fleet.service.VehicleAvailabilityService;
import com.newrelic.api.agent.Trace;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/availability")
public class VehicleAvailabilityController {

  private static final Logger log = LoggerFactory.getLogger("application");
  private final VehicleAvailabilityService availabilityService;

  // real time availability
  @GetMapping("/availability-by-branch/{branch-id}")
  public ResponseEntity<?> getVehicleAvailabilityByBranch(
      @PathVariable("branch-id") Integer branchId,
      @RequestParam(value = "from") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
          LocalDateTime fromDate,
      @RequestParam(value = "to") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
          LocalDateTime toDate) {
    return ok(availabilityService.getVehicleAvailabilityOnBranch(branchId, fromDate, toDate));
  }

  @GetMapping("/ready")
  public ResponseEntity<?> readyVehicle(
      @Valid VehicleSearchRequest request,
      @RequestHeader(value = "allowed-branch-ids", required = false) String allowedBranchIds) {
    log.info("Received request to fetch ready vehicles with parameters: {}", request);
    log.info("allowedBranchIds: {}", allowedBranchIds);
    List<Integer> filteredBranchIds =
        getFilteredBranchIds(allowedBranchIds, request.getCurrentLocationIds());
    log.info("filteredBranchIds: {}", filteredBranchIds);
    request.setCurrentLocationIds(filteredBranchIds);
    request.setServiceTypeIds(List.of(ServiceType.RENTAL.getId()));
    request.setStatusIds(List.of(StatusType.READY.getId()));
    request.setStatusReasonIds(Collections.emptyList());
    return ok(availabilityService.readyVehicle(request));
  }

  @GetMapping("/rented")
  public ResponseEntity<?> rentedVehicle(
      @Valid VehicleSearchRequest request,
      @RequestHeader(value = "allowed-branch-ids", required = false) String allowedBranchIds) {
    log.info("Received request to fetch rented vehicles with parameters: {}", request);
    log.info("allowedBranchIds: {}", allowedBranchIds);
    List<Integer> filteredBranchIds =
        getFilteredBranchIds(allowedBranchIds, request.getCurrentLocationIds());
    log.info("filteredBranchIds: {}", filteredBranchIds);
    request.setCurrentLocationIds(filteredBranchIds);
    return ok(availabilityService.rentedVehicle(request));
  }

  @GetMapping("/need-preparation")
  public ResponseEntity<?> needPreparationVehicle(
      @Valid VehicleSearchRequest request,
      @RequestHeader(value = "allowed-branch-ids", required = false) String allowedBranchIds) {
    log.info("Received request to fetch need preparation vehicles with parameters: {}", request);
    log.info("allowedBranchIds: {}", allowedBranchIds);
    List<Integer> filteredBranchIds =
        getFilteredBranchIds(allowedBranchIds, request.getCurrentLocationIds());
    log.info("filteredBranchIds: {}", filteredBranchIds);
    request.setServiceTypeIds(List.of(ServiceType.RENTAL.getId()));
    request.setCurrentLocationIds(filteredBranchIds);
    request.setStatusIds(List.of(StatusType.NEED_PREPARATION.getId()));
    return ok(availabilityService.needPreparationVehicle(request));
  }

  @GetMapping("/need-preparation/reasons")
  public ResponseEntity<?> needPreparationReasons() {
    log.info("Received request to fetch need-preparation reasons");
    List<Map<String, Object>> reasons =
        Arrays.stream(NeedPreparationReason.values())
            .map(
                reason ->
                    Map.<String, Object>of("id", reason.getId(), "name", reason.getDisplayName()))
            .collect(Collectors.toList());
    return ok(reasons);
  }

  @GetMapping("/nrm")
  public ResponseEntity<?> nrmVehicle(
      @Valid VehicleSearchRequest request,
      @RequestHeader(value = "allowed-branch-ids", required = false) String allowedBranchIds) {
    log.info("Received request to fetch nrm opened vehicles with parameters: {}", request);
    log.info("allowedBranchIds: {}", allowedBranchIds);
    List<Integer> filteredNrmCheckInBranchIds = getFilteredBranchIds(allowedBranchIds, request.getNrmCheckInLocationIds());
    log.info("filteredNrmCheckInBranchIds: {}", filteredNrmCheckInBranchIds);
    request.setNrmCheckInLocationIds(filteredNrmCheckInBranchIds);
    List<Integer> filteredNrmCheckOutBranchIds = getFilteredBranchIds(allowedBranchIds, request.getNrmCheckOutLocationIds());
    log.info("filteredNrmCheckOutBranchIds: {}", filteredNrmCheckOutBranchIds);
    request.setNrmCheckOutLocationIds(filteredNrmCheckOutBranchIds);
    return ok(availabilityService.nrmVehicle(request));
  }

  @GetMapping("/oos")
  public ResponseEntity<?> oosVehicle(
      @Valid VehicleSearchRequest request,
      @RequestHeader(value = "allowed-branch-ids", required = false) String allowedBranchIds) {
    log.info("Received request to fetch out of service vehicles with parameters: {}", request);
    log.info("allowedBranchIds: {}", allowedBranchIds);
    List<Integer> filteredBranchIds =
        getFilteredBranchIds(allowedBranchIds, request.getCurrentLocationIds());
    log.info("filteredBranchIds: {}", filteredBranchIds);
    request.setServiceTypeIds(List.of(ServiceType.RENTAL.getId()));
    request.setCurrentLocationIds(filteredBranchIds);
    request.setStatusIds(List.of(StatusType.OUT_OF_SERVICE.getId()));
    return ok(availabilityService.oosVehicle(request));
  }

  @GetMapping("/oos/reasons")
  public ResponseEntity<?> oosReasons() {
    log.info("Received request to fetch oos reasons");
    List<Map<String, Object>> reasons =
        Arrays.stream(YaqeenOutOfServiceReason.values())
            .map(
                reason ->
                    Map.<String, Object>of("id", reason.getId(), "name", reason.getDisplayName()))
            .collect(Collectors.toList());
    return ok(reasons);
  }

  @GetMapping("/live")
  public ResponseEntity<?> getVehicleLiveAvailability(@Valid VehicleSearchRequest request) {
    try {
      log.info("Received request to fetch available vehicles with parameters: {}", request);
      request.setServiceTypeIds(List.of(ServiceType.RENTAL.getId()));
      request.setStatusIds(List.of(StatusType.READY.getId()));
      return ResponseEntity.ok(availabilityService.getVehicleLiveAvailability(request));
    } catch (Exception e) {
      log.error("Error in getVehicleLiveAvailability: {}", e.getMessage(), e);
      throw e;
    }
  }

  @GetMapping("/live/similar")
  public ResponseEntity<?> getSimilarVehicleLiveAvailability(@Valid VehicleSearchRequest request) {
    log.info("Received request to fetch similar available vehicles with parameters: {}", request);
    request.setServiceTypeIds(List.of(ServiceType.RENTAL.getId()));
    return ok(availabilityService.getSimilarVehicleLiveAvailability(request));
  }

  private List<Integer> getFilteredBranchIds(
      String allowedBranchIds, List<Integer> preferredBranchIds) {
    if (StringUtils.isBlank(allowedBranchIds)) {
      return Collections.emptyList();
    }
    List<Integer> allowedBranchIdArray =
        Arrays.stream(allowedBranchIds.split(",")).map(Integer::parseInt).toList();
    if (ObjectUtils.isNotEmpty(preferredBranchIds)) {
      // set intersection of allowedBranchIdArray and preferredBranchIds
      return preferredBranchIds.stream()
          .filter(allowedBranchIdArray::contains)
          .collect(Collectors.toList());
    } else {
      return allowedBranchIdArray;
    }
  }
}
