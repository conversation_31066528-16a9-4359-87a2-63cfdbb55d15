package com.lumi.rental.core.fleet.service;

import static com.seera.lumi.core.fleet.constants.CacheConstants.*;

import com.lumi.rental.core.fleet.dto.CountVO;
import com.lumi.rental.core.fleet.entity.VehicleMake;
import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.mapper.VehicleMakeMapper;
import com.lumi.rental.core.fleet.repository.VehicleMakeRepository;
import com.lumi.rental.core.fleet.request.CreateUpdateVehicleMakeRequest;
import com.lumi.rental.core.fleet.response.VehicleMakeResponse;
import com.seera.lumi.core.cache.service.CacheService;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

/**
 * Service make for managing vehicle makes. Provides methods for retrieving, creating, and updating
 * vehicle makes, with caching and exception handling for improved performance and reliability.
 */
@Service
@RequiredArgsConstructor
public class VehicleMakeService {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final CacheService cacheService;
  private final VehicleMakeMapper vehicleMakeMapper;
  private final VehicleMakeRepository repository;

  /**
   * Retrieves a paginated list of all vehicle makes, with vehicle counts mapped to each make.
   * Results are cached for improved performance.
   *
   * @param pageable Pagination information (page number and size).
   * @return A paginated list of {@link VehicleMakeResponse}.
   */
  @Cacheable(
      cacheNames = MAKE_RESPONSE_ALL,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public Page<VehicleMakeResponse> getAllVehicleMake(PageRequest pageable) {
    log.info("Fetching all vehicle makes with pagination: {}", pageable);
    Map<Integer, Long> vehicleCountMap = findVehicleCountByMake();
    return repository
        .findAll(pageable)
        .map(
            make ->
                vehicleMakeMapper.buildVehicleMakeResponse(
                    make, vehicleCountMap.get(make.getId())));
  }

  /**
   * Retrieves a specific vehicle make by its ID, with the associated vehicle count. Results are
   * cached for improved performance.
   *
   * @param vehicleMakeId The ID of the vehicle make to retrieve.
   * @return The {@link VehicleMakeResponse} for the specified ID.
   * @throws BusinessException If the vehicle make is not found.
   */
  @Cacheable(
      cacheNames = MAKE_RESPONSE_BY_ID,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public VehicleMakeResponse getVehicleMakeById(Integer vehicleMakeId) {
    log.info("Fetching vehicle make by ID: {}", vehicleMakeId);
    Map<Integer, Long> vehicleCountMap = findVehicleCountByMake();
    return repository
        .findById(vehicleMakeId)
        .map(
            make ->
                vehicleMakeMapper.buildVehicleMakeResponse(make, vehicleCountMap.get(make.getId())))
        .orElseThrow(() -> new BusinessException(BaseError.NOT_FOUND, "VehicleMakeId"));
  }

  /**
   * Creates a new vehicle make with the provided details.
   *
   * @param request The request containing details for the new vehicle make.
   * @return The created {@link VehicleMakeResponse}.
   * @throws BusinessException If a vehicle make with the same code already exists.
   */
  public VehicleMakeResponse createVehicleMake(CreateUpdateVehicleMakeRequest request) {
    log.info("Creating a new vehicle make with request: {}", request);
    VehicleMake vehicleMake = vehicleMakeMapper.buildVehicleMake(request);
    try {
      VehicleMake savedVehicleMake = repository.saveAndFlush(vehicleMake);
      clearCache();
      return vehicleMakeMapper.buildVehicleMakeResponse(savedVehicleMake, 0L);
    } catch (DataIntegrityViolationException ex) {
      log.error("VehicleMake with code {} already exists.", request.getName().getEn());
      throw new BusinessException(BaseError.ALREADY_EXISTS, "Make");
    }
  }

  /**
   * Updates an existing vehicle make with the provided details.
   *
   * @param id The ID of the vehicle make to update.
   * @param request The request containing updated details for the vehicle make.
   * @return The updated {@link VehicleMakeResponse}.
   * @throws BusinessException If the vehicle make is not found.
   */
  public VehicleMakeResponse updateVehicleMake(int id, CreateUpdateVehicleMakeRequest request) {
    log.info("Updating vehicle make with ID: {} and request: {}", id, request);
    return repository
        .findById(id)
        .map(
            existingVehicleMake -> {
              if (repository.findByNameEn(request.getName().getEn()).isPresent()) {
                log.info("VehicleMake with code {} already exists.", request.getName().getEn());
                throw new BusinessException(BaseError.ALREADY_EXISTS, "Make");
              }
              VehicleMake updatedVehicleMake = vehicleMakeMapper.buildVehicleMake(request);
              updatedVehicleMake.setId(id);
              VehicleMake savedVehicleMake = repository.saveAndFlush(updatedVehicleMake);
              clearCache();
              return vehicleMakeMapper.buildVehicleMakeResponse(savedVehicleMake, 0L);
            })
        .orElseThrow(() -> new BusinessException(BaseError.NOT_FOUND, "VehicleMakeId"));
  }

  /**
   * Retrieves a map of vehicle counts by make ID. Results are cached for improved performance.
   *
   * @return A map where the key is the vehicle make ID and the value is the count of vehicles in
   *     that make.
   */
  @Cacheable(
      cacheNames = MAKE_VEHICLE_COUNT,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public Map<Integer, Long> findVehicleCountByMake() {
    log.info("Fetching vehicle counts by make");
    return repository.findVehicleCountByMake().stream()
        .collect(Collectors.toMap(CountVO::getId, CountVO::getCount));
  }

  private void clearCache() {
    cacheService.clearAllKeys(MAKE_CACHE_PREFIX + "*");
  }

  public VehicleMake findById(Integer makeId) {
    return repository
        .findById(makeId)
        .orElseThrow(() -> new BusinessException(BaseError.NOT_FOUND, "VehicleMakeId"));
  }
}
