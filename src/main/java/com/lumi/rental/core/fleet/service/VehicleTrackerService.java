package com.lumi.rental.core.fleet.service;

import static com.lumi.rental.core.fleet.util.VehicleUtil.getPlateNumberKeyForIOT;
import static com.seera.lumi.core.fleet.utils.ApplicationUtil.calculatePercentage;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;

import com.lumi.rental.core.fleet.entity.es.ESVehicleTrackingInfoHistory;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.mapper.VehicleTrackerMapper;
import com.lumi.rental.core.fleet.repository.es.ESVehicleTrackerHistoryRepository;
import com.lumi.rental.core.fleet.response.VehicleCountResponse;
import com.lumi.rental.core.fleet.response.VehicleTrackingResponse;
import com.lumi.rental.core.fleet.util.VehicleUtil;
import com.seera.lumi.core.cache.service.CacheService;
import com.seera.lumi.core.fleet.dto.VehicleCrossedSpeedLimitListResponseDTO;
import com.seera.lumi.core.fleet.dto.VehicleLiveTrackingEventDTO;
import com.seera.lumi.core.fleet.dto.VehicleResponseDTO;
import com.seera.lumi.core.fleet.exception.VehicleErrors;
import com.seera.lumi.core.fleet.vo.GroupedData;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleTrackerService {

  private final ESVehicleTrackerHistoryRepository esVehicleTrackerHistoryRepository;
  private final VehicleTrackerMapper vehicleTrackerMapper;
  private final CacheService cacheService;

  public VehicleLiveTrackingEventDTO getLatestVehicleTrackingInfo(final String plateNo) {
    return ofNullable(
            cacheService.get(getPlateNumberKeyForIOT(plateNo), VehicleLiveTrackingEventDTO.class))
        .orElseGet(() -> populateCacheAndGetDataFromDataSource(plateNo));
  }

  private Optional<ESVehicleTrackingInfoHistory> fetchLatestHistory(String plateNo) {
    return esVehicleTrackerHistoryRepository
        .findByPlateNoOrderByRecordDateTimeDesc(plateNo)
        .stream()
        .findFirst();
  }

  /**
   * Doing this manually and not via cacheable so that if IOT pipeline update data in cache we do
   * not need to lookup opensearch
   */
  public VehicleLiveTrackingEventDTO populateCacheAndGetDataFromDataSource(String plateNo) {
    return fetchLatestHistory(plateNo)
        .map(
            history -> {
              VehicleLiveTrackingEventDTO liveTrackingEventDTO =
                  vehicleTrackerMapper.mapVehicleTrackingInfoHistoryToDTO(history);
              cacheService.put(getPlateNumberKeyForIOT(plateNo), liveTrackingEventDTO);
              return liveTrackingEventDTO;
            })
        .orElse(null);
  }

  public VehicleTrackingResponse getVehicle(String plateNo) {
    // generate plateNumber key
    String plateNumberKey = VehicleUtil.getPlateNumberKeyForIOT(plateNo);
    // check if iot data is present in cache for this vehicle
    Optional<VehicleLiveTrackingEventDTO> record =
        ofNullable(cacheService.get(plateNumberKey, VehicleLiveTrackingEventDTO.class));
    if (record.isPresent()) {
      return vehicleTrackerMapper.mapVehicleTrackingDTO(record.get());
    } else {
      // fetch from elastic search
      ESVehicleTrackingInfoHistory vehicleTrackingInfo = fetchLatestHistory(plateNo).orElse(null);
      if (vehicleTrackingInfo == null) {
        throw new BusinessException(VehicleErrors.VEHICLE_NOT_FOUND);
      }
      return vehicleTrackerMapper.toDTO(vehicleTrackingInfo);
    }
  }

  public void saveVehicleTrackerInfoHistory(
      List<ESVehicleTrackingInfoHistory> vehicleTrackingInfoHistoryList) {
    esVehicleTrackerHistoryRepository.saveAll(vehicleTrackingInfoHistoryList);
  }

  public VehicleCountResponse getVehicleStatsCrossedSpeedLimitInSelectedMonth(
      Set<String> plateNumbers, int noOfMonth) {
    LocalDate fromDate = LocalDate.now().minusMonths(noOfMonth);
    long noOfVehicleCrossedSpeedLimit =
        esVehicleTrackerHistoryRepository.getVehicleStatsCrossedSpeedLimit(plateNumbers, fromDate);
    return new VehicleCountResponse()
        .setTotalCount(noOfVehicleCrossedSpeedLimit)
        .setPercentage(calculatePercentage(noOfVehicleCrossedSpeedLimit, plateNumbers.size()));
  }

  public VehicleCrossedSpeedLimitListResponseDTO getVehicleBreachedSpeedLimitNTimesInSelectedMonth(
      Set<String> plateNumbers, int breachLimit, int noOfMonth) {
    LocalDate fromDate = LocalDate.now().minusMonths(noOfMonth);
    GroupedData groupedData =
        esVehicleTrackerHistoryRepository.getVehiclesBreachSpeedLimitNTimeFromDate(
            plateNumbers, fromDate, breachLimit);
    List<VehicleResponseDTO> vehicleList =
        groupedData.getData().stream()
            .map(keyCount -> new VehicleResponseDTO(keyCount.getCount(), keyCount.getKey()))
            .collect(toList());
    return new VehicleCrossedSpeedLimitListResponseDTO(groupedData.getTotalDocCount(), vehicleList);
  }
}
