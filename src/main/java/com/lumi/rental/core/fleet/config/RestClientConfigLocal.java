package com.lumi.rental.core.fleet.config;

import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import javax.net.ssl.SSLContext;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.SSLContexts;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.data.client.orhlc.AbstractOpenSearchConfiguration;
import org.opensearch.data.client.orhlc.ClientConfiguration;
import org.opensearch.data.client.orhlc.RestClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
@Profile("local")
public class RestClientConfigLocal extends AbstractOpenSearchConfiguration {

  @Value("${opensearch.uris}")
  private String opensearchURL;

  @Value("${opensearch.username}")
  private String esUser;

  @Value("${opensearch.password}")
  private String esPwd;

  @Value("${spring.profiles.active}")
  private String activeProfile;

  @Override
  @Bean
  public RestHighLevelClient opensearchClient() {
    SSLContextBuilder sslBuilder;
    try {
      sslBuilder = SSLContexts.custom().loadTrustMaterial(null, (x509Certificates, s) -> true);
      final SSLContext sslContext = sslBuilder.build();
      final ClientConfiguration.MaybeSecureClientConfigurationBuilder clientConfiguration =
          ClientConfiguration.builder().connectedTo(opensearchURL);
      clientConfiguration.usingSsl(sslContext);
      clientConfiguration.withBasicAuth(esUser, esPwd);
      return RestClients.create(clientConfiguration.build()).rest();
    } catch (NoSuchAlgorithmException | KeyStoreException | KeyManagementException e) {
      throw new RuntimeException(e);
    }
  }
}
