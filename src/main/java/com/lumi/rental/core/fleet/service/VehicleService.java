package com.lumi.rental.core.fleet.service;

import static com.lumi.rental.core.fleet.util.PageUtil.getPageable;
import static com.lumi.rental.core.fleet.util.PageUtil.getPageableWithoutSort;
import static java.util.List.of;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumi.rental.core.fleet.entity.Vehicle;
import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.repository.VehicleRepository;
import com.lumi.rental.core.fleet.repository.es.ESVehicleRepository;
import com.lumi.rental.core.fleet.repository.specification.VehicleSpecification;
import com.lumi.rental.core.fleet.request.SearchRequestBase;
import com.lumi.rental.core.fleet.util.PageUtil;
import com.seera.lumi.core.fleet.dto.*;
import com.seera.lumi.core.fleet.entity.opensearch.ESVehicleData;
import com.seera.lumi.core.fleet.exception.VehicleErrors;
import com.seera.lumi.core.fleet.mapper.VehicleResponseMapper;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleService {

  private static final Logger log = LoggerFactory.getLogger("application");
  private final ObjectMapper objectMapper;
  private final VehicleRepository vehicleRepository;
  private final ESVehicleRepository esVehicleRepository;
  private final VehicleResponseMapper vehicleResponseMapper;
  private final MaintenanceLogService maintenanceLogService;

  public Optional<Vehicle> findByPlateNo(String plateNo) {
    return vehicleRepository.findByPlateNo(plateNo);
  }

  public Page<VehicleBasicResponseDTO> getVehicleByPlateNumbers(
      VehicleSearchRequestDTO requestDTO) {
    requestDTO.setSort(of("plateNo"));
    if (isNull(requestDTO.getLastMaintenanceInDays())) {
      return getVehicleWithOutLastMaintenanceFilter(requestDTO);
    } else {
      return getVehicleWithLastMaintenanceFilter(requestDTO);
    }
  }

  public ESVehicleData getVehicles(String plateNo) {
    return esVehicleRepository
        .findByPlateNo(plateNo)
        .map(
            v -> {
              if (v.getKm() != null) {
                v.setKm(v.getKm() / 1000);
              }
              return v;
            })
        .orElseThrow(() -> new BusinessException(VehicleErrors.VEHICLE_NOT_FOUND));
  }

  public VehicleFilterResponseDTO getVehicleFilterData(VehicleSearchRequestDTO requestDTO) {
    VehicleFilterResponseDTO vehicleFilterResponseDTO = new VehicleFilterResponseDTO();
    esVehicleRepository
        .findByPlateNoIncludeKeys(
            requestDTO.getPlateNumbers(), of("make", "year", "model", "carGroup"))
        .map(hit -> objectMapper.convertValue(hit.getSourceAsMap(), ESVehicleData.class))
        .forEach(
            data -> {
              if (nonNull(data.getYear())) {
                vehicleFilterResponseDTO.getModelYears().add(data.getYear());
              }
              if (nonNull(data.getMake())) {
                vehicleFilterResponseDTO.getMakes().add(data.getMake().getName());
              }
              if (nonNull(data.getModel())) {
                vehicleFilterResponseDTO.getModels().add(data.getModel().getName());
              }
              if (nonNull(data.getCarGroup())) {
                vehicleFilterResponseDTO.getCarGroups().add(data.getCarGroup());
              }
            });
    return vehicleFilterResponseDTO;
  }

  public SearchResponse<String> getVehicleAutoCompleteSuggestions(SearchRequestBase searchRequest) {
    if (ObjectUtils.isEmpty(searchRequest.getQuery())) {
      throw new BusinessException(BaseError.validationError("Invalid query parameter"));
    }
    return new SearchResponse<>(
        vehicleRepository.getVehicleSuggestions(
            searchRequest.getQuery(), getPageableWithoutSort(searchRequest)));
  }

  private Page<VehicleBasicResponseDTO> getVehicleWithOutLastMaintenanceFilter(
      VehicleSearchRequestDTO requestDTO) {
    long point1 = System.currentTimeMillis();

    Page<Vehicle> vehicles =
        vehicleRepository.findAll(new VehicleSpecification(requestDTO), getPageable(requestDTO));
    long point2 = System.currentTimeMillis();
    log.info("Total time taken in fetching vehicles {}", point2 - point1);

    List<String> plates = vehicles.stream().map(Vehicle::getPlateNo).toList();
    long point3 = System.currentTimeMillis();
    log.info("Total time taken in fetching plates {}", point3 - point2);

    Map<String, MaintenanceLogDTO> lastMaintenanceMap =
        getLastMaintenanceFromCache(plates).stream()
            .collect(toMap(MaintenanceLogDTO::getPlateNo, identity(), (o, n) -> o));
    long point4 = System.currentTimeMillis();
    log.info("Total time taken in fetching lastMaintenanceMap {}", point4 - point3);

    Page<VehicleBasicResponseDTO> result =
        vehicles.map(
            vehicle ->
                vehicleResponseMapper.toVehicleBasicResponseDTO(vehicle, lastMaintenanceMap));
    long point5 = System.currentTimeMillis();
    log.info("Total time taken in preparing finalResponse {}", point5 - point4);

    return result;
  }

  private Page<VehicleBasicResponseDTO> getVehicleWithLastMaintenanceFilter(
      VehicleSearchRequestDTO requestDTO) {
    LocalDateTime filterDate =
        LocalDate.now().atStartOfDay().minusDays(requestDTO.getLastMaintenanceInDays());
    List<Vehicle> vehicles =
        vehicleRepository.findAll(
            new VehicleSpecification(requestDTO), PageUtil.getPageable(requestDTO).getSort());
    Map<String, Vehicle> vehicleMap =
        vehicles.stream().collect(toMap(Vehicle::getPlateNo, identity()));
    List<MaintenanceLogDTO> filteredMaintenance =
        getLastMaintenanceFromCache(new ArrayList<>(vehicleMap.keySet())).stream()
            .sorted(Comparator.comparing(MaintenanceLogDTO::getPlateNo))
            .filter(maintenanceLogDTO -> nonNull(maintenanceLogDTO.getDate()))
            .filter(maintenanceLogDTO -> maintenanceLogDTO.getDate().isAfter(filterDate))
            .toList();
    long totalSize = filteredMaintenance.size();
    List<VehicleBasicResponseDTO> paginatedData =
        filteredMaintenance.stream()
            .skip(requestDTO.getPageNumber().longValue() * requestDTO.getPageSize())
            .limit(requestDTO.getPageSize())
            .map(
                maintenanceLogDTO ->
                    vehicleResponseMapper.toVehicleBasicResponseDTO(
                        vehicleMap.get(maintenanceLogDTO.getPlateNo()), maintenanceLogDTO))
            .toList();
    return new PageImpl<>(paginatedData, getPageable(requestDTO), totalSize);
  }

  private List<MaintenanceLogDTO> getLastMaintenanceFromCache(List<String> plateNos) {
    return plateNos.isEmpty()
        ? List.of()
        : maintenanceLogService.getLastMaintenance(
            new LastMaintenanceRequestDTO().setPlateNos(new HashSet<>(plateNos)));
  }
}
