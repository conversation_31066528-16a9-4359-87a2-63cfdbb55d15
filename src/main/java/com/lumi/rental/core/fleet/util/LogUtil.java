package com.lumi.rental.core.fleet.util;

import io.micrometer.tracing.Tracer;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class LogUtil {

  private static final String FORMAT = "%s-%s";

  public static String getTraceId(Tracer tracer) {
    try {
      String traceId = Objects.requireNonNull(tracer.currentSpan()).context().traceId();
      String spanId = Objects.requireNonNull(tracer.currentSpan()).context().spanId();
      return String.format(FORMAT, spanId, traceId);
    } catch (Exception var4) {
      return "";
    }
  }
}
