package com.lumi.rental.core.fleet.controller;

import static org.springframework.http.ResponseEntity.ok;

import com.lumi.rental.core.fleet.request.SearchRequestBase;
import com.lumi.rental.core.fleet.request.VehicleCountRequest;
import com.lumi.rental.core.fleet.response.VehicleCountResponse;
import com.lumi.rental.core.fleet.service.VehicleDashboardService;
import com.lumi.rental.core.fleet.service.VehicleService;
import com.seera.lumi.core.fleet.dto.*;
import com.seera.lumi.core.fleet.entity.opensearch.ESVehicleData;
import com.seera.lumi.core.fleet.enums.VehicleSplitKeyName;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/vehicles")
public class VehicleControllerV1 {

  private final VehicleService vehicleService;
  private final VehicleDashboardService dashboardService;

  @PostMapping
  public ResponseEntity<Page<VehicleBasicResponseDTO>> getVehicleByPlateNumbers(
      @Valid @RequestBody VehicleSearchRequestDTO requestDTO) {
    return ok(vehicleService.getVehicleByPlateNumbers(requestDTO));
  }

  @GetMapping("es/{plate-number}")
  public ResponseEntity<ESVehicleData> getVehicleByPlateNumberFromES(
      @PathVariable("plate-number") String plateNumber) {
    return ok(vehicleService.getVehicles(plateNumber));
  }

  @GetMapping("/suggestions")
  public ResponseEntity getAutoCompletePlateNoSearch(SearchRequestBase searchRequest) {
    return ok(vehicleService.getVehicleAutoCompleteSuggestions(searchRequest));
  }

  @PostMapping("/filter/options")
  public ResponseEntity<VehicleFilterResponseDTO> getVehicleFilterData(
      @RequestBody VehicleSearchRequestDTO requestDTO) {
    return ok(vehicleService.getVehicleFilterData(requestDTO));
  }

  @PostMapping("/count")
  public ResponseEntity<VehicleCountResponse> getVehiclesCount(
      @RequestBody @Valid VehicleCountRequest request) {
    return ok(dashboardService.getVehiclesCount(request));
  }

  @PostMapping("/split/percentage")
  public ResponseEntity<VehicleSplitResponseDTO> getVehicleSplitPercentage(
      @RequestParam(defaultValue = "MODEL") VehicleSplitKeyName keyName,
      @RequestBody @Valid VehiclePlateNoRequestDTO request) {
    return ok(dashboardService.getVehiclesSplitPercentage(request.getPlateNumbers(), keyName));
  }

  @PostMapping("/speed-limit/list")
  public ResponseEntity<VehicleCrossedSpeedLimitListResponseDTO> getVehiclesCrossedSpeedLimit(
      @RequestBody @Valid VehicleSpeedLimitRequestDTO request) {
    return ok(dashboardService.getVehiclesCrossedSpeedLimit(request));
  }
}
