package com.lumi.rental.core.fleet.enums;

import lombok.Getter;

@Getter
public enum SubServiceType {
  RENTAL_FLEET(1, "Rental Fleet"),
  LEASE_FLEET(2, "Lease Fleet"),
  ALLOCATION_AWAITING(3, "Allocation Awaiting"),
  TOWING_VEHICLES(4, "Towing Vehicles"),
  LEASE_RETURN(5, "Lease Return"),
  BR_SERVICE_VEHICLE(6, "Br Service Vehicle"),
  WS_MAINTENANCE(7, "Ws Maintenance"),
  MOBILE_SERVICE(8, "Mobile Service"),
  WS_ACCIDENT(9, "Ws Accident"),
  WS_INSURANCE(10, "Ws Insurance"),
  DFS(11, "Dfs"),
  STAFF_USE(12, "Staff Use"),
  YARD(13, "Yard"),
  DFA(14, "Dfa"),
  DFI(15, "Dfi"),
  WS_AWAITING(16, "Ws Awaiting"),
  CHAUFFEUR_FLEET(17, "Chauffeur Fleet"),
  SERVICE_VEHICLE(18, "Service Vehicle"),
  PRE_LEASE(19, "Pre Lease"),
  GENERAL_BACKUP(20, "General Backup"),
  CONTRACT_BACKUP(21, "Contract Backup");

  private final Integer id;
  private final String code;

  SubServiceType(Integer id, String code) {
    this.id = id;
    this.code = code;
  }

  public static SubServiceType fromId(Integer id) {
    for (SubServiceType type : values()) {
      if (type.id.equals(id)) {
        return type;
      }
    }
    return null;
  }

  public static SubServiceType fromCode(String code) {
    for (SubServiceType type : values()) {
      if (type.code.equalsIgnoreCase(code)) {
        return type;
      }
    }
    return null;
  }
}
