package com.lumi.rental.core.fleet.repository;

import com.lumi.rental.core.fleet.entity.InspectionReport;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface InspectionRepository extends JpaRepository<InspectionReport, Integer> {

  Optional<InspectionReport> findByPlateNoAndReferenceIdAndReferenceTypeAndReportType(
      String plateNo, String referenceId, String referenceType, Integer reportType);

  Optional<InspectionReport>
      findFirstByPlateNoAndReferenceIdAndReferenceTypeAndReportTypeOrderByCreatedOnDesc(
          String plateNo, String referenceId, String referenceType, Integer reportType);

  Optional<InspectionReport> findFirstByPlateNoOrderByCreatedOnDesc(String plateNo);

  Page<InspectionReport> findAllByPlateNo(String plateNo, Pageable pageable);

  @Query(
      "SELECT i.plateNo as plateNo, UNIX_TIMESTAMP(MAX(i.createdOn)) as lastInspectedDate "
          + "FROM InspectionReport i "
          + "WHERE i.plateNo IN :plateNos "
          + "AND i.statusId = 2 "
          + "GROUP BY i.plateNo")
  List<Map<String, Object>> findLastInspectionDatesByPlateNos(
      @Param("plateNos") List<String> plateNos);

  default Map<String, Long> getLatestInspectionDateByPlateNos(Set<String> plateNos) {
    return plateNos == null || plateNos.isEmpty()
        ? Collections.emptyMap()
        : findLastInspectionDatesByPlateNos(new ArrayList<>(plateNos)).stream()
            .collect(
                Collectors.toMap(
                    map -> (String) map.get("plateNo"),
                    map -> ((Number) map.get("lastInspectedDate")).longValue(),
                    (v1, v2) -> v1,
                    LinkedHashMap::new));
  }
}
