package com.lumi.rental.core.fleet.request;

import com.lumi.rental.core.fleet.util.CommonUtils;
import java.io.Serial;
import java.util.ArrayList;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class VehicleSearchRequest extends BasePageRequest {

  @Serial private static final long serialVersionUID = -650126160341757685L;

  private String plateNo;
  private List<Integer> modelIds = new ArrayList<>();
  private List<Integer> notModelIds = new ArrayList<>();
  private List<String> groupCodes = new ArrayList<>();
  private List<Integer> vehicleClassIds = new ArrayList<>();
  private List<Integer> serviceTypeIds = new ArrayList<>();
  // private List<Integer> subServiceTypeIds = new ArrayList<>();
  private List<Integer> statusIds = new ArrayList<>();
  private List<Integer> statusReasonIds = new ArrayList<>(); // reasons for need-prep, nrm, oos etc
  private Integer branchId; // Deprecated , use currentLocationIds instead
  private List<Integer> currentLocationIds = new ArrayList<>(); // for current location
  private List<Integer> nrmCheckOutLocationIds = new ArrayList<>(); // for nrm check-out location
  private List<Integer> nrmCheckInLocationIds = new ArrayList<>(); // for nrm check-in location

  @Override
  public String toString() {
    return CommonUtils.getStrFromObj(this);
  }
}
