package com.lumi.rental.core.fleet.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.lumi.rental.core.fleet.dto.ModelImageDTO;
import com.lumi.rental.core.fleet.dto.MultilingualDTO;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VehicleModelBasicResponse implements Serializable {

  @Serial private static final long serialVersionUID = -1L;

  private Integer id;
  private MultilingualDTO name;
  private VehicleMakeResponse make;
  private String materialId;
  private List<ModelImageDTO> images;
  private List<ModelFeatureResponse> features;
  private VehicleClassResponse vehicleClassResponse;
  private Integer modelYear;
}
