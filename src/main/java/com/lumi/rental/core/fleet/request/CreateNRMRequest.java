package com.lumi.rental.core.fleet.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

@Data
public class CreateNRMRequest implements Serializable {

  @NotNull @NotEmpty private String plateNo;

  @NotNull private Integer checkoutBranch;

  private String checkoutRemarks;

  @NotNull private Integer checkinBranch;

  private String driverId;

  @NotNull private Integer reasonId;
}
