package com.lumi.rental.core.fleet.controller;

import static org.springframework.http.ResponseEntity.ok;

import com.lumi.rental.core.fleet.enums.NonRevenueMovementReason;
import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.request.BasePageRequest;
import com.lumi.rental.core.fleet.request.CloseNRMRequest;
import com.lumi.rental.core.fleet.request.CreateNRMRequest;
import com.lumi.rental.core.fleet.service.NRMService;
import jakarta.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/nrm")
public class NRMController {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final NRMService nrmService;

  @GetMapping("/reasons")
  public ResponseEntity<?> getNRMReasons() {
    log.info("Received request to fetch nrm reasons");
    List<Map<String, Object>> reasons =
        Arrays.stream(NonRevenueMovementReason.values())
            .map(
                reason -> {
                  Map<String, Object> map = Map.of("id", reason.getId(), "name", reason.getName());
                  return map;
                })
            .collect(Collectors.toList());
    return ok(reasons);
  }

  @GetMapping("/{nrmId}")
  public ResponseEntity<?> getNRM(@PathVariable Integer nrmId) {
    return ResponseEntity.ok(nrmService.getNRM(nrmId));
  }

  @GetMapping("/vehicle/logs")
  public ResponseEntity<?> getNRMLogsByPlate(
      @RequestParam(value = "plateNo") String plateNo, @Valid final BasePageRequest request) {
    return ok(
        nrmService.findAllNRMLogsByPlateNo(
            plateNo, PageRequest.of(request.getPageNumber(), request.getPageSize())));
  }

  @PostMapping("/create")
  public ResponseEntity<?> createNRM(
      @Valid @RequestBody CreateNRMRequest request,
      @RequestHeader(value = "allowed-branch-ids", required = false) String allowedBranchIds) {
    log.info("allowedBranchIds: {}", allowedBranchIds);
    if (StringUtils.isEmpty(allowedBranchIds)) {
      throw new BusinessException(
          BaseError.BAD_REQUEST_WITH_REASON, "allowedBranchIds is required");
    }
    return ResponseEntity.ok(nrmService.createNRM(request, allowedBranchIds));
  }

  @PostMapping("/close")
  public ResponseEntity<?> closeNRM(
      @Valid @RequestBody CloseNRMRequest request,
      @RequestHeader(value = "allowed-branch-ids", required = false) String allowedBranchIds) {
    if (StringUtils.isEmpty(allowedBranchIds)) {
      throw new BusinessException(
          BaseError.BAD_REQUEST_WITH_REASON, "allowedBranchIds is required");
    }
    return ResponseEntity.ok(nrmService.closeNRM(request, allowedBranchIds));
  }
}
