package com.lumi.rental.core.fleet.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

@Getter
public enum InspectionStatus {
  INITIATED(1, "INITIATED"),
  COMPLETED(2, "COMPLETED"),
  SAVED_FOR_LATER(3, "SAVED_FOR_LATER");

  private final Integer id;
  private final String code;

  InspectionStatus(Integer id, String code) {
    this.id = id;
    this.code = code;
  }

  @JsonCreator
  public static InspectionStatus fromId(Integer id) {
    for (InspectionStatus type : values()) {
      if (type.getId().equals(id)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Unknown enum value: " + id);
  }

  public static InspectionStatus fromCode(String code) {
    for (InspectionStatus type : values()) {
      if (type.code.equalsIgnoreCase(code)) {
        return type;
      }
    }
    return null;
  }

  public Integer getId() {
    return id;
  }

  @Override
  public String toString() {
    return id.toString();
  }
}
