package com.lumi.rental.core.fleet.repository;

import com.lumi.rental.core.fleet.entity.VehicleOperationalData;
import com.lumi.rental.core.fleet.response.SearchVehicleInspectionResponse;
import com.lumi.rental.core.fleet.response.VehicleBookingResponse;
import com.lumi.rental.core.fleet.response.VehicleNRMResponse;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface VehicleOperationalDataRepository
    extends JpaRepository<VehicleOperationalData, Integer> {

  @EntityGraph(value = "vehicle-ops-all-relation-graph")
  Optional<VehicleOperationalData> findByPlateNo(String plateNo);

  @EntityGraph(value = "vehicle-ops-all-relation-graph")
  List<VehicleOperationalData> findAll(Specification<VehicleOperationalData> spec);

  @EntityGraph(value = "vehicle-ops-all-relation-graph")
  Page<VehicleOperationalData> findAll(
      Specification<VehicleOperationalData> spec, Pageable pageable);

  @EntityGraph("vehicle-ops-all-relation-graph")
  List<VehicleOperationalData> findAllByPlateNoIn(List<String> plateNos);

  @Query(
      """
                      SELECT
                          NEW com.lumi.rental.core.fleet.response.VehicleBookingResponse(
                              v.plateNo,
                              v.model.vehicleGroup.code,
                              v.model.make.nameEn,
                              v.model.nameEn,
                              v.model.modelVersion,
                              r.referenceId,
                              r.checkInBranch,
                              r.checkInDate
                          )
                      FROM
                          VehicleOperationalData v
                      JOIN (
                          SELECT
                              r1.plateNo AS plateNo,
                              r1.referenceId AS referenceId,
                              r1.checkInBranch AS checkInBranch,
                              r1.checkInDate AS checkInDate,
                              r1.id AS id
                          FROM
                              VehicleReservationData r1
                          JOIN (
                              SELECT
                                  rd.plateNo AS plateNo,
                                  MAX(rd.id) AS maxId
                              FROM
                                  VehicleReservationData rd
                              GROUP BY
                                  rd.plateNo
                          ) AS latest
                          ON
                              r1.plateNo = latest.plateNo
                              AND r1.id = latest.maxId
                      ) AS r
                      ON
                          v.plateNo = r.plateNo
                      WHERE
                          (:plateNo IS NULL OR v.plateNo LIKE CONCAT(:plateNo, '%'))
                          AND (:#{#modelIds.isEmpty()} = true OR v.model.faceModelId IN :modelIds)
                          AND (:#{#groupCodes.isEmpty()} = true OR v.model.vehicleGroup.code IN :groupCodes)
                          AND v.serviceTypeId = :serviceTypeId
                          AND FUNCTION('JSON_EXTRACT', v.vehicleStatus, '$.statusId') = :statusId
                          AND (:#{#currentLocationIds.isEmpty()} = true OR v.currentLocationId IN :currentLocationIds)
                      ORDER BY
                          r.checkInDate DESC
      """)
  Page<VehicleBookingResponse> findRentedVehiclesWithBookingNumber(
      @Param("plateNo") String plateNo,
      @Param("modelIds") List<Integer> modelIds,
      @Param("groupCodes") List<String> groupCodes,
      @Param("serviceTypeId") Integer serviceTypeId,
      @Param("subServiceTypeId") Integer subServiceTypeId,
      @Param("statusId") Integer statusId,
      @Param("currentLocationIds") List<Integer> currentLocationIds,
      @Param("currentDate") LocalDateTime currentDate,
      Pageable pageable);

  @Query(
      """
                      SELECT    new com.lumi.rental.core.fleet.response.VehicleNRMResponse(nrm.id, v.plateNo, v.model.make.nameEn, v.model.nameEn, v.model.modelVersion, CAST(FUNCTION('JSON_EXTRACT', nrm.checkoutData, '$.branchId') AS integer ), CAST(FUNCTION('JSON_EXTRACT', nrm.checkinData, '$.branchId') AS integer ), nrm.reasonId, nrm.driverId, CAST(FUNCTION('JSON_EXTRACT', nrm.checkoutData, '$.date') AS long ))
                      FROM      VehicleOperationalData v
                      LEFT JOIN NonRevenueMovement nrm
                      ON        v.plateNo = nrm.plateNo
                      WHERE     (:plateNo IS NULL OR v.plateNo LIKE CONCAT(:plateNo, '%'))
                      AND      ( (:#{#nrmCheckOutLocationIds.isEmpty()} = true OR FUNCTION('JSON_EXTRACT', nrm.checkoutData, '$.branchId')  IN :nrmCheckOutLocationIds) OR (:#{#nrmCheckInLocationIds.isEmpty()} = true OR FUNCTION('JSON_EXTRACT', nrm.checkinData, '$.branchId')  IN :nrmCheckInLocationIds) )
                      AND       (:#{#modelIds.isEmpty()} = true OR v.model.faceModelId IN :modelIds)
                      AND       (:#{#groupCodes.isEmpty()} = true OR v.model.vehicleGroup.code IN :groupCodes)
                      AND       v.serviceTypeId = :serviceTypeId
                      AND       nrm.statusId = :nrmStatusId
                      AND       FUNCTION('JSON_EXTRACT', v.vehicleStatus, '$.statusId') = :statusId
                      AND       (:#{#statusReasonIds.isEmpty()} = true OR FUNCTION('JSON_EXTRACT', v.vehicleStatus, '$.subStatusId') IN :statusReasonIds)
                      ORDER BY  FUNCTION('JSON_EXTRACT', nrm.checkoutData, '$.date')
                    """)
  Page<VehicleNRMResponse> findNRMVehiclesWithDetail(
      @Param("plateNo") String plateNo,
      @Param("nrmCheckOutLocationIds") List<Integer> nrmCheckOutLocationIds,
      @Param("nrmCheckInLocationIds") List<Integer> nrmCheckInLocationIds,
      @Param("modelIds") List<Integer> modelIds,
      @Param("groupCodes") List<String> groupCodes,
      @Param("serviceTypeId") Integer serviceTypeId,
      @Param("subServiceTypeId") Integer subServiceTypeId,
      @Param("nrmStatusId") Integer nrmStatusId,
      @Param("statusId") Integer statusId,
      @Param("statusReasonIds") List<Integer> statusReasonIds,
      Pageable pageable);

  @Query(
      value =
          """
        SELECT vod.plate_no,
               v.plate_no_ar,
               CONCAT(make.name_en, ' ', m.name_en) AS name_en,
               CONCAT(make.name_ar, ' ', m.name_ar) AS name_ar,
               UNIX_TIMESTAMP(r.checkin_date) AS check_in_time
        FROM   vehicle_operational_data vod
               LEFT JOIN vehicle v ON vod.plate_no = v.plate_no
               LEFT JOIN (
                   SELECT plate_no, checkin_date,
                          ROW_NUMBER() OVER (PARTITION BY plate_no ORDER BY checkin_date DESC) as rn
                   FROM reservation
               ) r ON vod.plate_no = r.plate_no AND r.rn = 1
               LEFT JOIN model m ON vod.model_id = m.id
               LEFT JOIN make ON m.make_id = make.id
        WHERE  (:plateNo IS NULL OR vod.plate_no LIKE CONCAT(:plateNo, '%'))
               AND (:branchId IS NULL OR vod.current_location_id = :branchId)
               AND JSON_EXTRACT(vehicle_status, '$.statusId') = 2
        ORDER BY r.checkin_date DESC
      """,
      nativeQuery = true)
  Page<SearchVehicleInspectionResponse> findAvailableVehiclesForCheckinInspection(
      String plateNo, Integer branchId, Pageable pageable);

  @Query(
      value =
          """
        SELECT vod.plate_no,
               v.plate_no_ar,
               CONCAT(make.name_en, ' ', m.name_en) AS name_en,
               CONCAT(make.name_ar, ' ', m.name_ar) AS name_ar,
               CASE WHEN JSON_EXTRACT(vehicle_status, '$.statusId') = 2 
                    THEN JSON_EXTRACT(vehicle_status, '$.date') ELSE 0 END AS check_out_time,
               CASE WHEN JSON_EXTRACT(vehicle_status, '$.statusId') = 2 
                    THEN 1 ELSE 0 END AS is_assigned,
               insp.id AS last_inspection_id,
               UNIX_TIMESTAMP(insp.created_on) AS last_inspected
        FROM   vehicle_operational_data vod
               LEFT JOIN vehicle v ON vod.plate_no = v.plate_no
               LEFT JOIN (
                   SELECT plate_no, id, created_on,
                          ROW_NUMBER() OVER (PARTITION BY plate_no ORDER BY created_on DESC) as rn
                   FROM inspection_report
               ) insp ON vod.plate_no = insp.plate_no AND insp.rn = 1
               LEFT JOIN model m ON vod.model_id = m.id
               LEFT JOIN make ON m.make_id = make.id
        WHERE  (:plateNo IS NULL OR vod.plate_no LIKE CONCAT(:plateNo, '%'))
               AND (:branchId IS NULL OR vod.current_location_id = :branchId)
               AND (JSON_EXTRACT(vehicle_status, '$.statusId') = 1 
                    OR (JSON_EXTRACT(vehicle_status, '$.statusId') = 2 
                        AND FROM_UNIXTIME(JSON_EXTRACT(vehicle_status, '$.date')/1000) >= NOW() - INTERVAL 2 HOUR))
        ORDER BY check_out_time DESC
      """,
      nativeQuery = true)
  Page<SearchVehicleInspectionResponse> findAvailableVehiclesForCheckoutInspection(
      String plateNo, Integer branchId, Pageable pageable);
}
