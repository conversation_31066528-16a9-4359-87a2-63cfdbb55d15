package com.lumi.rental.core.fleet.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "maintenance_type")
public class MaintenanceType extends BaseEntity {

  @Column(name = "name_en")
  private String nameEn;

  @Column(name = "name_ar")
  private String nameAr;
}
