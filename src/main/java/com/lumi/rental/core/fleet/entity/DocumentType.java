package com.lumi.rental.core.fleet.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Inheritance(strategy = InheritanceType.JOINED)
@Table(name = "document_type")
public class DocumentType extends BaseEntity {

  @Column(name = "code")
  private String code;

  @Column(name = "description_en")
  private String descriptionEn;

  @Column(name = "description_ar")
  private String descriptionAr;

  @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(unique = true)
  private DocumentCategory category;

  @Column(name = "order_no")
  private Integer orderNo;
}
