package com.lumi.rental.core.fleet.config;

import org.opensearch.client.RestHighLevelClient;
import org.opensearch.data.client.orhlc.AbstractOpenSearchConfiguration;
import org.opensearch.data.client.orhlc.ClientConfiguration;
import org.opensearch.data.client.orhlc.RestClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
@Profile("!local")
public class RestClientConfig extends AbstractOpenSearchConfiguration {

  @Value("${opensearch.uris}")
  private String opensearchURL;

  @Value("${opensearch.username}")
  private String esUser;

  @Value("${opensearch.password}")
  private String esPwd;

  @Value("${spring.profiles.active}")
  private String activeProfile;

  @Override
  @Bean
  public RestHighLevelClient opensearchClient() {
    final ClientConfiguration.MaybeSecureClientConfigurationBuilder clientConfiguration =
        ClientConfiguration.builder().connectedTo(opensearchURL);
    clientConfiguration.usingSsl();
    clientConfiguration.withConnectTimeout(5000);
    clientConfiguration.withSocketTimeout(60000);
    clientConfiguration.withBasicAuth(esUser, esPwd);
    return RestClients.create(clientConfiguration.build()).rest();
  }
}
