package com.lumi.rental.core.fleet.service;

import static com.lumi.rental.core.fleet.enums.InspectionReportType.CHECK_IN;
import static com.lumi.rental.core.fleet.enums.InspectionReportType.CHECK_OUT;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

import com.lumi.rental.core.fleet.api.agreement.AgreementClient;
import com.lumi.rental.core.fleet.api.agreement.req.AgreementInspectionUpdate;
import com.lumi.rental.core.fleet.api.agreement.req.AgreementInspectionUpdate.InspectionData;
import com.lumi.rental.core.fleet.api.agreement.resp.AgreementInspectionUpdateResponse;
import com.lumi.rental.core.fleet.dto.InspectionMetadataDTO;
import com.lumi.rental.core.fleet.entity.InspectionReport;
import com.lumi.rental.core.fleet.entity.VehicleOperationalData;
import com.lumi.rental.core.fleet.enums.InspectionReportType;
import com.lumi.rental.core.fleet.enums.InspectionStatus;
import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.exception.InspectionErrors;
import com.lumi.rental.core.fleet.mapper.InspectionMapper;
import com.lumi.rental.core.fleet.repository.InspectionRepository;
import com.lumi.rental.core.fleet.repository.VehicleOperationalDataRepository;
import com.lumi.rental.core.fleet.request.CompleteInspectionRequest;
import com.lumi.rental.core.fleet.request.InitiateInspectionRequest;
import com.lumi.rental.core.fleet.response.InspectionResponse;
import com.lumi.rental.core.fleet.response.SearchVehicleInspectionResponse;
import com.seera.lumi.core.fleet.utils.SecurityUtil;
import java.util.Optional;
import javax.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class InspectionService {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final InspectionMapper inspectionMapper;
  private final VehicleService vehicleService;
  private final VehicleOperationalDataRepository operationalDataRepository;
  private final VehicleAvailabilityService availabilityService;
  private final InspectionRepository repository;
  private final AgreementClient agreementClient;

  public InspectionReport getInspectionReportById(Integer inspectionId) {
    return repository
        .findById(inspectionId)
        .orElseThrow(
            () ->
                new BusinessException(BaseError.NOT_FOUND, "Inspection report id:" + inspectionId));
  }

  public InspectionReport getLatestInspectionByPlateNo(String plateNo) {
    return findLatestInspectionByPlateNo(plateNo)
        .orElseThrow(
            () ->
                new BusinessException(
                    BaseError.NOT_FOUND, "Inspection report for plate-no:" + plateNo));
  }

  public Page<InspectionResponse> getInspectionLogsByPlateNo(String plateNo, Pageable pageable) {
    return repository
        .findAllByPlateNo(plateNo, pageable)
        .map(inspectionMapper::buildInspectionResponse);
  }

  public Page<SearchVehicleInspectionResponse> searchVehicleForInspection(
      String plateNo,
      Integer branchId,
      InspectionReportType inspectionReportType,
      Pageable pageable) {
    return switch (inspectionReportType) {
      case CHECK_IN -> operationalDataRepository.findAvailableVehiclesForCheckinInspection(
          plateNo, branchId, pageable);
      case CHECK_OUT -> operationalDataRepository.findAvailableVehiclesForCheckoutInspection(
          plateNo, branchId, pageable);
    };
  }

  public Optional<InspectionReport> findLatestInspectionByPlateNo(String plateNo) {
    return repository.findFirstByPlateNoOrderByCreatedOnDesc(plateNo);
  }

  public InspectionResponse initiateInspection(InitiateInspectionRequest request) {
    // Ensure the vehicle exists, else throw an exception
    //    vehicleService
    //        .findByPlateNo(request.getPlateNo())
    //        .orElseThrow(() -> new BusinessException(VehicleErrors.VEHICLE_NOT_FOUND));
    availabilityService.getVehicleOperationalData(request.getPlateNo());

    // Attempt to find an existing inspection report with the given criteria
    InspectionReport inspectionReport =
        repository
            .findFirstByPlateNoAndReferenceIdAndReferenceTypeAndReportTypeOrderByCreatedOnDesc(
                request.getPlateNo(),
                request.getReferenceId(),
                request.getReferenceType().toUpperCase(),
                request.getReportType().getId())
            .orElseGet(
                () -> {
                  // Create a new report if not found
                  InspectionReport newReport = inspectionMapper.buildInspectionReport(request);
                  newReport.setStatusId(InspectionStatus.INITIATED.getId());
                  newReport.setReportType(request.getReportType().getId());
                  return newReport;
                });

    // Save and flush the inspection report
    try {
      repository.saveAndFlush(inspectionReport);
      InspectionResponse response = inspectionMapper.buildInspectionResponse(inspectionReport);
      // In case of check-in, send the last inspection-id during the time of checkout
      if (CHECK_IN.equals(request.getReportType())) {
        InspectionReport checkOutReport =
            repository
                .findByPlateNoAndReferenceIdAndReferenceTypeAndReportType(
                    request.getPlateNo(),
                    request.getReferenceId(),
                    request.getReferenceType().toUpperCase(),
                    CHECK_OUT.getId())
                .orElseThrow(
                    () ->
                        new BusinessException(
                            BaseError.NOT_FOUND,
                            "Checkout Inspection report not found for plate-no: "
                                + request.getPlateNo()));
        response.setCheckoutInspectionId(checkOutReport.getId());
      }
      return response;
    } catch (BusinessException ex) {
      throw ex;
    } catch (Exception ex) {
      log.error("Error while initiating the inspection Ex:{} Request:{}", ex, request);
      throw new BusinessException(
          BaseError.INTERNAL_SERVER_BASE_ERROR,
          "An error occurred while initiating the inspection.");
    }
  }

  @Transactional
  public InspectionReport updateInspectionData(
      Integer inspectionId, InspectionMetadataDTO metadata) {
    // Retrieve the existing inspection report by ID
    InspectionReport inspectionReport = getInspectionReportById(inspectionId);
    VehicleOperationalData opsData =
        availabilityService.getVehicleOperationalData(inspectionReport.getPlateNo());
    if (InspectionStatus.COMPLETED.getId().equals(inspectionReport.getStatusId())) {
      throw new BusinessException(InspectionErrors.INSPECTION_ALREADY_COMPLETED);
    }
    inspectionReport.setMetadata(metadata);
    validateOdometerReading(metadata, inspectionReport, opsData);
    InspectionReport report = saveInspectionReport(inspectionReport);
    return report;
  }

  public InspectionReport saveForLater(Integer inspectionId) {
    // Retrieve the existing inspection report by ID
    InspectionReport inspectionReport = getInspectionReportById(inspectionId);
    if (InspectionStatus.COMPLETED.getId().equals(inspectionReport.getStatusId())) {
      throw new BusinessException(InspectionErrors.INSPECTION_ALREADY_COMPLETED);
    }
    inspectionReport.setStatusId(InspectionStatus.SAVED_FOR_LATER.getId());
    return repository.saveAndFlush(inspectionReport);
  }

  @Transactional
  public InspectionReport completeInspection(
      Integer inspectionId, String referenceId, InspectionMetadataDTO metadataDTO) {
    // Retrieve the existing inspection report by ID
    InspectionReport inspectionReport = getInspectionReportById(inspectionId);
    if (InspectionStatus.COMPLETED.getId().equals(inspectionReport.getStatusId())) {
      throw new BusinessException(InspectionErrors.INSPECTION_ALREADY_COMPLETED);
    }
    VehicleOperationalData opsData =
        availabilityService.getVehicleOperationalData(inspectionReport.getPlateNo());
    assert ObjectUtils.isNotEmpty(metadataDTO)
        && ObjectUtils.isNotEmpty(metadataDTO.getOdometerReading());
    assert ObjectUtils.isNotEmpty(metadataDTO)
        && ObjectUtils.isNotEmpty(metadataDTO.getFuelLevel());
    validateOdometerReading(metadataDTO, inspectionReport, opsData);
    if (StringUtils.isBlank(inspectionReport.getReferenceId())) {
      assert StringUtils.isNotBlank(referenceId);
      inspectionReport.setReferenceId(referenceId);
    }
    return completeInspection(inspectionReport, metadataDTO);
  }

  @Transactional
  public InspectionReport completeInspection(CompleteInspectionRequest request) {
    // Retrieve the existing inspection report based on request parameters
    InspectionReport existingReport =
        repository
            .findByPlateNoAndReferenceIdAndReferenceTypeAndReportType(
                request.getPlateNo(),
                request.getReferenceId(),
                request.getReferenceType().toUpperCase(),
                request.getReportType().getId())
            .orElseThrow(
                () -> new BusinessException(BaseError.NOT_FOUND, "Inspection report not found"));
    if (InspectionStatus.COMPLETED.getId().equals(existingReport.getStatusId())) {
      throw new BusinessException(InspectionErrors.INSPECTION_ALREADY_COMPLETED);
    }
    VehicleOperationalData opsData =
        availabilityService.getVehicleOperationalData(existingReport.getPlateNo());
    validateOdometerReading(request.getMetadata(), existingReport, opsData);
    return completeInspection(existingReport, request.getMetadata());
  }

  @Transactional
  private InspectionReport completeInspection(
      InspectionReport inspectionReport, InspectionMetadataDTO metadataDTO) {
    // Update inspection report status and metadata
    inspectionReport.setStatusId(InspectionStatus.COMPLETED.getId());
    /*
    TODO: In future we will be enabling this code to mandate the vehicle status from parking app
    if ("AGREEMENT".equalsIgnoreCase(inspectionReport.getReferenceType())) {
      if (CHECK_IN.getId().equals(inspectionReport.getReportType())) {
        if (nonNull(metadataDTO) && isNull(metadataDTO.getVehicleStatus())) {
          throw new BusinessException(BaseError.validationError("Vehicle status is required"));
        }
      }
    }*/
    checkAndCompleteCheckoutInspection(inspectionReport);
    if (isNull(inspectionReport.getMetadata())) {
      inspectionReport.setMetadata(metadataDTO);
    } else {
      inspectionReport.setMetadata(
          inspectionMapper.metadataMapper(inspectionReport.getMetadata(), metadataDTO));
    }
    VehicleOperationalData opsData =
        availabilityService.getVehicleOperationalData(inspectionReport.getPlateNo());
    updateOperationalData(opsData, metadataDTO);
    if (nonNull(inspectionReport.getMetadata())) {
      inspectionReport.getMetadata().setCompletedBy(SecurityUtil.getLoggedInUserId());
    }
    // Return the saved inspection report
    return saveInspectionReport(inspectionReport);
  }

  private void checkAndCompleteCheckoutInspection(InspectionReport inspectionReport) {
    if (CHECK_IN.getId().equals(inspectionReport.getReportType())) {
      repository
          .findByPlateNoAndReferenceIdAndReferenceTypeAndReportType(
              inspectionReport.getPlateNo(),
              inspectionReport.getReferenceId(),
              inspectionReport.getReferenceType().toUpperCase(),
              CHECK_OUT.getId())
          .filter(
              checkOutReport ->
                  InspectionStatus.INITIATED.getId().equals(checkOutReport.getStatusId()))
          .ifPresent(
              checkOutReport -> {
                log.info(
                    "Auto Completing the checkout inspection for id {} ,plateNo {}, referenceId {}",
                    checkOutReport.getId(),
                    checkOutReport.getPlateNo(),
                    checkOutReport.getReferenceId());
                checkOutReport.setStatusId(InspectionStatus.COMPLETED.getId());
                if (nonNull(checkOutReport.getMetadata())) {
                  checkOutReport.getMetadata().setCompletedBy(SecurityUtil.getLoggedInUserId());
                }
              });
    }
  }

  public InspectionReport saveInspectionReport(InspectionReport inspectionReport) {
    return repository.saveAndFlush(inspectionReport);
  }

  public AgreementInspectionUpdateResponse notifyAgreementService(
      InspectionReport inspectionReport) {
    log.info(
        "Executing inspection report callback executing for plateNo = {} agreementNo {} and reportType {}",
        inspectionReport.getPlateNo(),
        inspectionReport.getReferenceId(),
        InspectionReportType.fromId(inspectionReport.getReportType()));
    InspectionData inspectionData = mapInspectionData(inspectionReport);
    if (CHECK_OUT.getId().equals(inspectionReport.getReportType())) {
      return agreementClient.updateCheckoutInspection(
          inspectionReport.getReferenceId(),
          AgreementInspectionUpdate.builder()
              .plateNo(inspectionReport.getPlateNo())
              .checkoutInspection(inspectionData)
              .build());
    } else if (CHECK_IN.getId().equals(inspectionReport.getReportType())) {
      InspectionData checkOutInspectionData = checkInInspectionData(inspectionReport);
      return agreementClient.updateCheckinInspection(
          inspectionReport.getReferenceId(),
          AgreementInspectionUpdate.builder()
              .plateNo(inspectionReport.getPlateNo())
              .checkoutInspection(checkOutInspectionData)
              .checkInInspection(inspectionData)
              .build());
    }
    return null;
  }

  private InspectionData checkInInspectionData(InspectionReport checkInReport) {
    InspectionReport checkOutReport =
        repository
            .findByPlateNoAndReferenceIdAndReferenceTypeAndReportType(
                checkInReport.getPlateNo(),
                checkInReport.getReferenceId(),
                checkInReport.getReferenceType().toUpperCase(),
                CHECK_OUT.getId())
            .orElse(null);
    return mapInspectionData(checkOutReport);
  }

  private InspectionData mapInspectionData(InspectionReport inspectionReport) {
    if (isNull(inspectionReport)) {
      return null;
    }
    InspectionData inspectionData = new InspectionData();
    inspectionData.setInspectionId(inspectionReport.getId());
    inspectionData.setBranchId(inspectionReport.getBranchId());
    inspectionData.setMetadata(inspectionReport.getMetadata());
    inspectionData.setStatus(InspectionStatus.fromId(inspectionReport.getStatusId()).name());
    return inspectionData;
  }

  /** For Edit case during agreement closing when detail is incorrect */
  @Transactional
  public InspectionReport modifyInspectionData(
      Integer inspectionId, InspectionMetadataDTO metadata) {
    InspectionReport inspectionReport = getInspectionReportById(inspectionId);
    VehicleOperationalData opsData =
        availabilityService.getVehicleOperationalData(inspectionReport.getPlateNo());
    validateOdometerReading(metadata, inspectionReport, opsData);
    updateOperationalData(opsData, metadata);
    inspectionMapper.metadataMapper(inspectionReport.getMetadata(), metadata);
    return saveInspectionReport(inspectionReport);
  }

  public void updateOperationalData(
      VehicleOperationalData opsData, InspectionMetadataDTO metadata) {
    if (ObjectUtils.isNotEmpty(metadata.getOdometerReading())) {
      opsData.setOdometerReading(metadata.getOdometerReading());
    }
    if (ObjectUtils.isNotEmpty(metadata.getFuelLevel())) {
      opsData.setFuelLevel(metadata.getFuelLevel());
    }
    availabilityService.updateVehicleOperationData(opsData);
  }

  private void validateOdometerReading(
      InspectionMetadataDTO inputMetadata,
      InspectionReport currentReport,
      VehicleOperationalData opsData) {
    if (ObjectUtils.isNotEmpty(inputMetadata.getOdometerReading())) {
      if (CHECK_IN.getId().equals(currentReport.getReportType())) {
        InspectionReport checkOutReport =
            repository
                .findByPlateNoAndReferenceIdAndReferenceTypeAndReportType(
                    currentReport.getPlateNo(),
                    currentReport.getReferenceId(),
                    currentReport.getReferenceType().toUpperCase(),
                    CHECK_OUT.getId())
                .orElseThrow(
                    () ->
                        new BusinessException(
                            BaseError.NOT_FOUND,
                            "Checkout Inspection report not found for plate-no: "
                                + currentReport.getPlateNo()));
        if (ObjectUtils.isEmpty(checkOutReport.getMetadata().getOdometerReading())) {
          throw new BusinessException(
              BaseError.validationError(
                  "Checkout odometer reading is required for checkin inspection"));
        }
        if (inputMetadata.getOdometerReading()
            <= checkOutReport.getMetadata().getOdometerReading()) {
          throw new BusinessException(
              BaseError.validationError(
                  "Current Odometer reading should be greater than checkout reading"));
        }
      } else if (CHECK_OUT.getId().equals(currentReport.getReportType())) {
        // TODO: no validation in case of checkout for now
        //        if (inputMetadata.getOdometerReading() < opsData.getOdometerReading()) {
        //          throw new BusinessException(
        //                  BaseError.validationError("Current Odometer reading should be greater or
        // equal to previous reading"));
        //        }
      }
    }
  }
}
