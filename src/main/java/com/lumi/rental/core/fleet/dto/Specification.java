package com.lumi.rental.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Map;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Specification implements Serializable {

  private String version;
  private Integer seatingCapacity;
  private Integer doors;
  private Integer bootSpace;
  private Integer luggageCountBig;
  private Integer luggageCountMedium;
  private Integer luggageCountSmall;
  private String transmission;
  private String transmissionType;
  private Integer engineSize;
  private Integer horsepower;
  private DimensionDTO dimensions;
  private String fuelType;
  private Integer fuelCapacity;
  private SuspensionDTO suspension;
  private BrakeDTO brakes;
  private TyreDetailDTO tyres;
  private Map<String, Boolean> interiorFeatures;
  private Map<String, Boolean> exteriorFeatures;
  private Map<String, Boolean> safetyFeatures;

  @Data
  public static class DimensionDTO {
    private Integer length;
    private Integer width;
    private Integer height;
    private Integer wheelbase;
  }

  @Data
  public static class SuspensionDTO {
    private String front;
    private String rear;
  }

  @Data
  public static class BrakeDTO {
    private String front;
    private String rear;
  }

  @Data
  public static class TyreDetailDTO {
    private String front;
    private String rear;
  }
}
