package com.lumi.rental.core.fleet.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumi.rental.core.fleet.entity.MaintenanceLog;
import com.lumi.rental.core.fleet.entity.MaintenanceType;
import com.lumi.rental.core.fleet.mapper.MaintenanceMapper;
import com.lumi.rental.core.fleet.repository.MaintenanceLogRepository;
import com.seera.lumi.core.fleet.dto.VehicleMaintenanceEventDTO;
import com.seera.lumi.core.fleet.service.MaintenanceTypeService;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.common.utils.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class MaintenanceService {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final ObjectMapper objectMapper;
  private final MaintenanceMapper maintenanceMapper;
  private final MaintenanceTypeService maintenanceTypeService;
  private final MaintenanceLogRepository logRepository;

  @Transactional(isolation = Isolation.READ_UNCOMMITTED)
  public void syncVehicleMaintenanceDataInSQLDataSource(List<Bytes> eventList) {
    Map<String, MaintenanceType> maintenanceTypeMap =
        maintenanceTypeService.maintenanceTypeOnServiceType();
    eventList.forEach(
        event -> {
          try {
            VehicleMaintenanceEventDTO eventDTO =
                objectMapper.readValue(event.get(), VehicleMaintenanceEventDTO.class);
            Optional<MaintenanceLog> maintenanceLogOptional =
                logRepository.findByPlateNoAndRecordDate(
                    eventDTO.getPlateNo(), eventDTO.getDocDate().atStartOfDay());
            if (maintenanceLogOptional.isEmpty()) {
              MaintenanceLog maintenanceLog = maintenanceMapper.toMaintenanceLog(eventDTO);
              maintenanceLog.setMaintenanceType(maintenanceTypeMap.get(eventDTO.getServiceType()));
              logRepository.saveAndFlush(maintenanceLog);
            }
          } catch (Exception ex) {
            log.error("Error in processing even for sql-db-sync {}", new String(event.get()), ex);
          }
        });
  }
}
