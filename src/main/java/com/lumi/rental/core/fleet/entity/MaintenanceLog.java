package com.lumi.rental.core.fleet.entity;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
public class MaintenanceLog extends BaseEntity {

  @Column(name = "plate_no")
  private String plateNo;

  @Column(name = "record_date")
  private LocalDateTime recordDate;

  @ManyToOne(targetEntity = MaintenanceType.class, cascade = CascadeType.ALL)
  private MaintenanceType maintenanceType;

  @Column(name = "remarks")
  private String remarks;

  @Column(name = "odometer_reading")
  private Long odometerReading;
}
