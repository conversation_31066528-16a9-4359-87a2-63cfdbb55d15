package com.lumi.rental.core.fleet.repository.specification;

import com.lumi.rental.core.fleet.entity.VehicleOperationalData;
import com.lumi.rental.core.fleet.request.VehicleSearchRequest;
import jakarta.persistence.criteria.*;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

@AllArgsConstructor
public class VehicleAvailabilitySpecification implements Specification<VehicleOperationalData> {

  // Constants for column names
  private static final String PLATE_NO_COLUMN = "plateNo";
  private static final String MODEL_COLUMN = "model";
  private static final String GROUP_COLUMN = "vehicleGroup";
  private static final String VEHICLE_CLASS_COLUMN = "vehicleClass";
  private static final String SERVICE_TYPE_ID = "serviceTypeId";
  // private static final String SUB_SERVICE_TYPE_ID = "subServiceTypeId";
  private static final String VEHICLE_STATUS = "vehicleStatus";
  private static final String CURRENT_LOCATION_ID = "currentLocationId";

  private final VehicleSearchRequest filter;

  @Override
  public Predicate toPredicate(
      Root<VehicleOperationalData> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    // Collect all predicates
    List<Predicate> predicates = new ArrayList<>();

    // Add optional filters based on the filter request
    addOptionalFilters(root, criteriaBuilder, predicates);

    // Return the combined predicate
    return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
  }

  private void addOptionalFilters(
      Root<VehicleOperationalData> root,
      CriteriaBuilder criteriaBuilder,
      List<Predicate> predicates) {

    // PlateNo filter
    if (StringUtils.isNotEmpty(filter.getPlateNo())) {
      predicates.add(
          criteriaBuilder.like(
              criteriaBuilder.upper(root.get(PLATE_NO_COLUMN)),
              filter.getPlateNo().toUpperCase() + "%"));
    }

    // Model IDs filter
    if (ObjectUtils.isNotEmpty(filter.getModelIds())) {
      predicates.add(root.get(MODEL_COLUMN).get("id").in(filter.getModelIds()));
    }

    // Not Model IDs filter
    if (ObjectUtils.isNotEmpty(filter.getNotModelIds())) {
      predicates.add(
          criteriaBuilder.not(
              root.get(MODEL_COLUMN).get("faceModelId").in(filter.getNotModelIds())));
    }

    // Group Codes filter
    if (ObjectUtils.isNotEmpty(filter.getGroupCodes())) {
      predicates.add(
          root.get(MODEL_COLUMN).get(GROUP_COLUMN).get("code").in(filter.getGroupCodes()));
    }

    if (ObjectUtils.isNotEmpty(filter.getVehicleClassIds())) {
      predicates.add(
          root.get(MODEL_COLUMN)
              .get(VEHICLE_CLASS_COLUMN)
              .get("id")
              .in(filter.getVehicleClassIds()));
    }

    // ServiceType filter
    if (ObjectUtils.isNotEmpty(filter.getServiceTypeIds())) {
      predicates.add(root.get(SERVICE_TYPE_ID).in(filter.getServiceTypeIds()));
    }

    // SubServiceType filter
    //    if (ObjectUtils.isNotEmpty(filter.getSubServiceTypeIds())) {
    //      predicates.add(root.get(SUB_SERVICE_TYPE_ID).in(filter.getSubServiceTypeIds()));
    //    }

    // StatusIds filter (Status IDs) filter
    if (ObjectUtils.isNotEmpty(filter.getStatusIds())) {
      Expression<Integer> jsonPathExpr =
          criteriaBuilder.function(
              "JSON_EXTRACT",
              Integer.class,
              root.get(VEHICLE_STATUS), // JSON column
              criteriaBuilder.literal("$.statusId") // JSON path
              );
      // Directly use the extracted value in the IN clause
      predicates.add(
          jsonPathExpr.in(
              filter.getStatusIds().stream().map(String::valueOf).collect(Collectors.toList())));
    }

    // SubStatusIds filter (Status Reason IDs) filter
    if (ObjectUtils.isNotEmpty(filter.getStatusReasonIds())) {
      Expression<Integer> jsonPathExpr =
          criteriaBuilder.function(
              "JSON_EXTRACT",
              Integer.class,
              root.get(VEHICLE_STATUS),
              criteriaBuilder.literal("$.subStatusId"));
      // Directly use the extracted value in the IN clause
      predicates.add(
          jsonPathExpr.in(
              filter.getStatusReasonIds().stream()
                  .map(String::valueOf)
                  .collect(Collectors.toList())));
    }

    // Location ID filter
    if (ObjectUtils.isNotEmpty(filter.getBranchId())) {
      predicates.add(criteriaBuilder.equal(root.get(CURRENT_LOCATION_ID), filter.getBranchId()));
    } else if (ObjectUtils.isNotEmpty(filter.getCurrentLocationIds())) {
      predicates.add(root.get(CURRENT_LOCATION_ID).in(filter.getCurrentLocationIds()));
    }
  }
}
