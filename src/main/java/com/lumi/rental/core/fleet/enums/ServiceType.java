package com.lumi.rental.core.fleet.enums;

import lombok.Getter;

@Getter
public enum ServiceType {
  RENTAL(1, "RENTAL", "A"),
  LEASE(2, "LEASE", "B"),
  LIMOUSINE(3, "LIMOUSINE", "C");

  private final Integer id;
  private final String code;
  private final String carproCode;

  ServiceType(Integer id, String code, String carproCode) {
    this.id = id;
    this.code = code;
    this.carproCode = carproCode;
  }

  public static ServiceType fromId(Integer id) {
    for (ServiceType type : values()) {
      if (type.id.equals(id)) {
        return type;
      }
    }
    return null;
  }

  public static ServiceType fromCode(String code) {
    for (ServiceType type : values()) {
      if (type.code.equalsIgnoreCase(code)) {
        return type;
      }
    }
    return null;
  }

  public static ServiceType fromCarproCode(String carproCode) {
    for (ServiceType type : values()) {
      if (type.carproCode.equalsIgnoreCase(carproCode)) {
        return type;
      }
    }
    return null;
  }

  public Integer getId() {
    return id;
  }

  public String getCarproCode() {
    return carproCode;
  }
}
