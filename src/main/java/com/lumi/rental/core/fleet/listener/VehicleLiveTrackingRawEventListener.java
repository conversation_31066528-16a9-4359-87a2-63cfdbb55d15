package com.lumi.rental.core.fleet.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumi.rental.core.fleet.mapper.VehicleTrackerMapper;
import com.lumi.rental.core.fleet.util.VehicleUtil;
import com.seera.lumi.core.cache.service.CacheService;
import com.seera.lumi.core.fleet.dto.VehicleLiveTrackingEventDTO;
import com.seera.lumi.core.fleet.dto.VehicleLiveTrackingRawEventDTO;
import com.seera.lumi.core.fleet.producer.VehicleLiveTrackingEventProducer;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleLiveTrackingRawEventListener {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final CacheService cacheService;
  private final ObjectMapper objectMapper;
  private final VehicleTrackerMapper vehicleTrackerMapper;
  private final VehicleLiveTrackingEventProducer vehicleLiveTrackingEventProducer;

  @KafkaListener(
      topics = {"${kafka.topic.sync.vehicle.tracking.data}"},
      groupId = "filter-vehicle-live-tracking-raw-events",
      concurrency = "${kafka.listen.concurrency}",
      containerFactory = "kafkaListenerContainerFactory",
      autoStartup = "${kafka.listen.auto.start:true}")
  public void listen(@Payload VehicleLiveTrackingRawEventDTO event) {
    try {
      log.info(
          "[LISTENER] message received for vehicle tracking data filter-vehicle-live-tracking-raw-events {}",
          event);
      // format/clean plate number
      event.setPlateNumber(VehicleUtil.getPlateNumber(event.getDisplayName()));
      // Validate record
      if (!isValidRecord(event)) {
        log.error(
            "[LISTENER] Invalid IOT record | Display-Name {} PlateNumber {} RecordDateTime {}",
            event.getDisplayName(),
            event.getPlateNumber(),
            event.getRecordDateTime());
        return;
      }
      // convert VehicleLiveTrackingRawEventDTO to VehicleLiveTrackingEventDTO
      VehicleLiveTrackingEventDTO vehicleLiveTrackingEventDTO = vehicleTrackerMapper.map(event);
      // check if we have update for vehicle using record date time
      if (isRecordChanged(vehicleLiveTrackingEventDTO)) {
        CompletableFuture.runAsync(
            () -> vehicleLiveTrackingEventProducer.produce(vehicleLiveTrackingEventDTO));
      }
    } catch (Exception ex) {
      log.error(
          "[LISTENER] error while processing vehicle tracking data filter-vehicle-live-tracking-raw-events",
          ex);
    }
  }

  private boolean isValidRecord(VehicleLiveTrackingRawEventDTO vehicleLiveTrackingRawEventDTO) {
    return vehicleLiveTrackingRawEventDTO.getRecordDateTime() != null
        && vehicleLiveTrackingRawEventDTO.getPlateNumber() != null
        && VehicleUtil.isValidPlateNumber(vehicleLiveTrackingRawEventDTO.getPlateNumber());
  }

  private boolean isRecordChanged(VehicleLiveTrackingEventDTO newRecord) {
    try {
      String plateNumber = newRecord.getPlateNo();
      String plateNumberKey = VehicleUtil.getPlateNumberKeyForIOT(plateNumber);
      Object record = cacheService.get(plateNumberKey);
      if (record != null) {
        VehicleLiveTrackingEventDTO cachedRecord =
            objectMapper.convertValue(record, VehicleLiveTrackingEventDTO.class);
        if (cachedRecord.getRecordDateTime().equals(newRecord.getRecordDateTime())) {
          return false;
        }
      }
      cacheService.put(plateNumberKey, newRecord);
      return true;
    } catch (Exception ex) {
      log.error(
          "[LISTENER] exception while checking plate number Exception {}", String.valueOf(ex));
      return true;
    }
  }
}
