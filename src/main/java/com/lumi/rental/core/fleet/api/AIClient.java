package com.lumi.rental.core.fleet.api;

import com.lumi.rental.core.fleet.config.feign.FeignConfig;
import com.lumi.rental.core.fleet.dto.AIGenerateResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(
    name = "AIClient",
    url = "${api.ai.service.base.url}",
    configuration = {FeignConfig.class})
public interface AIClient {

  @GetMapping("/api/v1/ai/generate")
  AIGenerateResponse generateAIResponse(@RequestParam("prompt") String prompt);
}
