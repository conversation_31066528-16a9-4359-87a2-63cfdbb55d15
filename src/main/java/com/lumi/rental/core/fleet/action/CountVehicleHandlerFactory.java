package com.lumi.rental.core.fleet.action;

import static java.util.Optional.ofNullable;

import com.lumi.rental.core.fleet.enums.VehicleCountType;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CountVehicleHandlerFactory {

  private static final Map<VehicleCountType, CountVehicleHandler> COUNT_HANDLER_MAP =
      new EnumMap<>(VehicleCountType.class);

  @Autowired
  public CountVehicleHandlerFactory(List<CountVehicleHandler> handlers) {
    handlers.forEach(handler -> COUNT_HANDLER_MAP.put(handler.countType(), handler));
  }

  public Optional<CountVehicleHandler> getHandler(VehicleCountType countType) {
    return ofNullable(COUNT_HANDLER_MAP.get(countType));
  }
}
