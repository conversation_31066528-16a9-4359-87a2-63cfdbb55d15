package com.lumi.rental.core.fleet.response;

import static lombok.AccessLevel.PRIVATE;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serial;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VehicleBasicResponseV3 implements Serializable {

  @Serial private static final long serialVersionUID = -1L;

  String plateNo;
  String plateNoAr;
  VehicleModelBasicResponseV3 model;
  Integer modelYear;
  String color;
  String chassisNo;
  String assetId;
  String policyNo;
  Boolean active;
}
