package com.lumi.rental.core.fleet.repository.specification;

import com.lumi.rental.core.fleet.entity.MaintenanceLog;
import com.seera.lumi.core.fleet.dto.MaintenanceLogSearchRequestDTO;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;

public class MaintenanceLogSpecification implements Specification<MaintenanceLog> {

  private final MaintenanceLogSearchRequestDTO filter;

  public MaintenanceLogSpecification(MaintenanceLogSearchRequestDTO pageRequest) {
    this.filter = pageRequest;
  }

  @Override
  public Predicate toPredicate(
      Root<MaintenanceLog> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    Predicate p = criteriaBuilder.conjunction();

    if (!CollectionUtils.isEmpty(filter.getPlateNos())) {

      p.getExpressions()
          .add(
              criteriaBuilder.in(criteriaBuilder.equal(root.get("plateNo"), filter.getPlateNos())));
    }

    if (StringUtils.isNotBlank(filter.getQuery())) {

      p.getExpressions()
          .add(criteriaBuilder.or(criteriaBuilder.equal(root.get("plateNo"), filter.getQuery())));
    }

    if (ObjectUtils.isNotEmpty(filter.getFrom()) && ObjectUtils.isNotEmpty(filter.getTo())) {

      p.getExpressions()
          .add(criteriaBuilder.between(root.get("date"), filter.getFrom(), filter.getTo()));
    }
    return p;
  }
}
