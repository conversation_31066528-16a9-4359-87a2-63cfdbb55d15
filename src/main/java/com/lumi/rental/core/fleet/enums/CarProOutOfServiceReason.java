package com.lumi.rental.core.fleet.enums;

import lombok.Getter;

@Getter
public enum CarProOutOfServiceReason {
  TRANSFER(1, "Transfer");

  private final Integer id;
  private final String displayName;

  CarProOutOfServiceReason(Integer id, String displayName) {
    this.id = id;
    this.displayName = displayName;
  }

  public static CarProOutOfServiceReason fromId(Integer id) {
    for (CarProOutOfServiceReason type : values()) {
      if (type.id.equals(id)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Unknown value for OutOfServiceReason: " + id);
  }
}
