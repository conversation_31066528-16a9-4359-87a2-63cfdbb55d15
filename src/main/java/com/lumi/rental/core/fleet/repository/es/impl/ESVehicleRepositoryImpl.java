package com.lumi.rental.core.fleet.repository.es.impl;

import static com.seera.lumi.core.fleet.opensearch.OpenSearchUtil.*;
import static com.seera.lumi.core.fleet.utils.IndexNames.VEHICLE_DATA;
import static java.lang.Integer.MAX_VALUE;
import static java.util.List.of;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.opensearch.search.aggregations.AggregationBuilders.count;
import static org.opensearch.search.aggregations.AggregationBuilders.terms;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumi.rental.core.fleet.repository.es.ESVehicleRepositoryCustom;
import com.lumi.rental.core.fleet.util.PageUtil;
import com.seera.lumi.core.fleet.dto.*;
import com.seera.lumi.core.fleet.entity.opensearch.ESVehicleData;
import com.seera.lumi.core.fleet.enums.VehicleSplitKeyName;
import com.seera.lumi.core.fleet.opensearch.OpenSearchFetchRequest;
import com.seera.lumi.core.fleet.vo.GroupedData;
import com.seera.lumi.core.fleet.vo.KeyCount;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.index.query.*;
import org.opensearch.search.SearchHit;
import org.opensearch.search.SearchHits;
import org.opensearch.search.aggregations.AggregationBuilder;
import org.opensearch.search.aggregations.bucket.MultiBucketsAggregation.Bucket;
import org.opensearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.opensearch.search.aggregations.metrics.ParsedValueCount;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

@RequiredArgsConstructor
public class ESVehicleRepositoryImpl implements ESVehicleRepositoryCustom {

  private static final String GROUP_AGG = "GROUP_AGG";
  private static final String COUNT_AGG = "COUNT_AGG";
  private static final String PLATE_NO_AGG = "PLATE_NO_AGG";
  private static final String PLATE_NO_FIELD = "plateNo.keyword";
  private static final String TOTAL_COUNT_FIELD = "_index";
  private static final String YEAR_FIELD = "year";
  private static final String MAKE_FIELD = "make.name.en.keyword";
  private static final String MODEL_FIELD = "model.name.en.keyword";
  private static final String PLATE_NO_PLAIN_FIELD = "plateNo";
  private static final String CAR_GROUP_FIELD = "carGroup.keyword";
  private static final String LAST_MAINTENANCE_DATE = "lastMaintenance.date";
  private final RestHighLevelClient highLevelClient;
  private final ObjectMapper objectMapper;

  @Override
  public GroupedData fetchVehicleGroupByKey(Set<String> platNos, VehicleSplitKeyName keyName) {
    QueryBuilder queryBuilder = new TermsQueryBuilder(PLATE_NO_FIELD, platNos);
    List<AggregationBuilder> aggregationBuilderList = new ArrayList<>();
    aggregationBuilderList.add(
        terms(GROUP_AGG)
            .field(keyName.getValue())
            .size(MAX_VALUE)
            .subAggregation(terms(PLATE_NO_AGG).field(PLATE_NO_FIELD).size(MAX_VALUE)));
    aggregationBuilderList.add(count(COUNT_AGG).field(TOTAL_COUNT_FIELD));
    SearchResponse searchResponse =
        searchData(
            VEHICLE_DATA, queryBuilder, aggregationBuilderList, of(), false, highLevelClient);
    GroupedData groupedData = new GroupedData();
    ParsedStringTerms parsedStringTerms = searchResponse.getAggregations().get(GROUP_AGG);
    parsedStringTerms
        .getBuckets()
        .forEach(
            bucket -> {
              KeyCount keyCount = new KeyCount(bucket.getKeyAsString(), bucket.getDocCount());
              ParsedStringTerms subBucketTerms = bucket.getAggregations().get(PLATE_NO_AGG);
              keyCount.setDataList(
                  subBucketTerms.getBuckets().stream()
                      .map(Bucket::getKeyAsString)
                      .collect(toList()));
              groupedData.getData().add(keyCount);
            });
    ParsedValueCount totalDocCount = searchResponse.getAggregations().get(COUNT_AGG);
    groupedData.setTotalDocCount(totalDocCount.getValue());
    return groupedData;
  }

  @Override
  public List<ESVehicleData> findByPlateNoIn(Set<String> plateNos) {
    QueryBuilder queryBuilder = new TermsQueryBuilder(PLATE_NO_FIELD, plateNos);
    OpenSearchFetchRequest fetchRequest =
        new OpenSearchFetchRequest(highLevelClient, VEHICLE_DATA)
            .setFetchSource(true)
            .setSize(DEFAULT_OS_FETCH_SIZE)
            .setQueryBuilder(queryBuilder);
    SearchResponse searchResponse = searchData(fetchRequest);
    SearchHits searchHits = searchResponse.getHits();
    return Arrays.stream(searchHits.getHits())
        .map(hit -> objectMapper.convertValue(hit.getSourceAsMap(), ESVehicleData.class))
        .collect(toList());
  }

  @Override
  public Page<VehicleBasicResponseDTO> searchVehicleData(VehicleSearchRequestDTO searchRequest) {
    BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
    boolQueryBuilder.filter(new TermsQueryBuilder(PLATE_NO_FIELD, searchRequest.getPlateNumbers()));
    if (isNotEmpty(searchRequest.getModelYears())) {
      boolQueryBuilder.filter(new TermsQueryBuilder(YEAR_FIELD, searchRequest.getModelYears()));
    }
    if (isNotEmpty(searchRequest.getMakes())) {
      boolQueryBuilder.filter(new TermsQueryBuilder(MAKE_FIELD, searchRequest.getMakes()));
    }
    if (isNotEmpty(searchRequest.getModels())) {
      boolQueryBuilder.filter(new TermsQueryBuilder(MODEL_FIELD, searchRequest.getModels()));
    }
    if (isNotEmpty(searchRequest.getCarGroups())) {
      boolQueryBuilder.filter(new TermsQueryBuilder(CAR_GROUP_FIELD, searchRequest.getCarGroups()));
    }
    if (nonNull(searchRequest.getLastMaintenanceInDays())
        && searchRequest.getLastMaintenanceInDays() != 0) {
      boolQueryBuilder.filter(
          new RangeQueryBuilder(LAST_MAINTENANCE_DATE)
              .gte(lastNDaysDateLiteral(searchRequest.getLastMaintenanceInDays())));
    }
    searchRequest.setSort(List.of("_id"));
    Pageable pageable = PageUtil.getPageable(searchRequest);
    OpenSearchFetchRequest fetchRequest =
        new OpenSearchFetchRequest(highLevelClient, VEHICLE_DATA)
            .setQueryBuilder(boolQueryBuilder)
            .setFetchSource(true)
            .setPageable(pageable)
            .setIncludeTotalCount(true);
    SearchResponse searchResponse = searchData(fetchRequest);
    SearchHits searchHits = searchResponse.getHits();
    List<VehicleBasicResponseDTO> data =
        Arrays.stream(searchHits.getHits())
            .map(
                hit ->
                    objectMapper.convertValue(hit.getSourceAsMap(), VehicleBasicResponseDTO.class))
            .collect(toList());
    assert searchHits.getTotalHits() != null;
    return new PageImpl<>(data, pageable, searchHits.getTotalHits().value);
  }

  @Override
  public Stream<SearchHit> findByPlateNoIncludeKeys(
      Set<String> plateNumbers, List<String> includeKeys) {
    QueryBuilder queryBuilder = new TermsQueryBuilder(PLATE_NO_FIELD, plateNumbers);
    OpenSearchFetchRequest fetchRequest =
        new OpenSearchFetchRequest(highLevelClient, VEHICLE_DATA)
            .setFetchSource(true)
            .setSize(DEFAULT_OS_FETCH_SIZE)
            .setQueryBuilder(queryBuilder)
            .setIncludeKeys(includeKeys);
    SearchResponse searchResponse = searchData(fetchRequest);
    SearchHits searchHits = searchResponse.getHits();
    return Arrays.stream(searchHits.getHits());
  }

  @Override
  public com.seera.lumi.core.fleet.dto.SearchResponse<String> getVehicleSuggestions(
      SearchRequestDTO searchRequestDTO) {
    QueryBuilder queryBuilder =
        new MatchPhrasePrefixQueryBuilder(PLATE_NO_PLAIN_FIELD, searchRequestDTO.getQuery());
    OpenSearchFetchRequest fetchRequest =
        new OpenSearchFetchRequest(highLevelClient, VEHICLE_DATA)
            .setIncludeKeys(List.of(PLATE_NO_PLAIN_FIELD))
            .setPageable(PageUtil.getPageableWithoutSort(searchRequestDTO))
            .setFetchSource(true)
            .setIncludeTotalCount(true)
            .setQueryBuilder(queryBuilder);
    SearchResponse searchResponse = searchData(fetchRequest);
    SearchHits searchHits = searchResponse.getHits();
    assert searchHits.getTotalHits() != null;
    return new com.seera.lumi.core.fleet.dto.SearchResponse(
        Arrays.stream(searchHits.getHits())
            .map(hit -> hit.getSourceAsMap().get(PLATE_NO_PLAIN_FIELD))
            .distinct()
            .collect(toList()),
        searchHits.getTotalHits().value);
  }

  @Override
  public ESDeepSearchResponse<ESVehicleData> scrollOverAllVehiclesWithPagination(
      ESDeepSearchRequestDTO requestDTO) {
    requestDTO.setSort(List.of("_id"));
    OpenSearchFetchRequest fetchRequest =
        new OpenSearchFetchRequest(highLevelClient, VEHICLE_DATA)
            .setPageable(PageUtil.getPageable(requestDTO))
            .setFetchSource(true)
            .setIncludeTotalCount(true)
            .setQueryBuilder(new MatchAllQueryBuilder())
            .setSearchAfter(requestDTO.getSearchAfter());
    SearchResponse searchResponse = deepSearchData(fetchRequest);
    SearchHits searchHits = searchResponse.getHits();
    List<ESVehicleData> data =
        Arrays.stream(searchHits.getHits())
            .map(hit -> objectMapper.convertValue(hit.getSourceAsMap(), ESVehicleData.class))
            .collect(toList());
    List<Object> searchAfter = new ArrayList<>();
    if (searchHits.getHits().length > 0) {
      searchAfter =
          Arrays.asList(searchHits.getHits()[searchHits.getHits().length - 1].getSortValues());
    }
    assert searchHits.getTotalHits() != null;
    return new ESDeepSearchResponse<>(data, searchHits.getTotalHits().value, searchAfter);
  }
}
