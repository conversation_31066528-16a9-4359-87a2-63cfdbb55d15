package com.lumi.rental.core.fleet.enums;

import lombok.Getter;

public enum FuelLevel {
  EMPTY(0, "Empty"),
  QUARTER(1, "Quarter"),
  HALF(2, "Half"),
  THREE_QUARTERS(3, "Three-Quarters"),
  FULL(4, "Full"),
  UNKNOWN(-1, "Unknown");

  @Getter private final Integer value;
  @Getter private final String description;

  FuelLevel(Integer value, String description) {
    this.value = value;
    this.description = description;
  }

  public static FuelLevel fromId(Integer id) {
    for (FuelLevel fuelLevel : values()) {
      if (fuelLevel.getValue().equals(id)) {
        return fuelLevel;
      }
    }
    return FuelLevel.UNKNOWN;
  }
}
