package com.lumi.rental.core.fleet.repository.es.impl;

import static com.seera.lumi.core.fleet.utils.ApplicationUtil.formatDate;
import static com.seera.lumi.core.fleet.utils.IndexNames.VEHICLE_TRACKING_HISTORY;
import static java.util.List.of;
import static org.springframework.data.elasticsearch.annotations.DateFormat.date_hour_minute_second;

import com.lumi.rental.core.fleet.repository.es.ESVehicleTrackerHistoryRepositoryCustom;
import com.seera.lumi.core.fleet.opensearch.OpenSearchUtil;
import com.seera.lumi.core.fleet.vo.GroupedData;
import com.seera.lumi.core.fleet.vo.KeyCount;
import java.time.LocalDate;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.RangeQueryBuilder;
import org.opensearch.index.query.ScriptQueryBuilder;
import org.opensearch.index.query.TermsQueryBuilder;
import org.opensearch.script.Script;
import org.opensearch.search.aggregations.AggregationBuilder;
import org.opensearch.search.aggregations.AggregationBuilders;
import org.opensearch.search.aggregations.PipelineAggregationBuilder;
import org.opensearch.search.aggregations.PipelineAggregatorBuilders;
import org.opensearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.opensearch.search.aggregations.metrics.ParsedCardinality;
import org.opensearch.search.aggregations.pipeline.ParsedStatsBucket;

@RequiredArgsConstructor
public class ESVehicleTrackerHistoryRepositoryImpl
    implements ESVehicleTrackerHistoryRepositoryCustom {

  private static final String COUNT_AGG = "COUNT_AGG";
  private static final String GROUP_AGG = "GROUP_AGG";
  private static final String TOTAL_BUCKET_COUNT_AGG = "TOTAL_BUCKET_COUNT_AGG";
  private static final String PLATE_NO_FIELD = "plateNo.keyword";
  private static final String RECORD_DATE_TIME_FIELD = "recordDateTime";
  private final RestHighLevelClient restHighLevelClient;

  @Override
  public long getVehicleStatsCrossedSpeedLimit(Set<String> plateNumbers, LocalDate fromDate) {
    BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
    boolQueryBuilder.filter(new TermsQueryBuilder(PLATE_NO_FIELD, plateNumbers));
    boolQueryBuilder.filter(
        new ScriptQueryBuilder(new Script("doc['speed'].value > doc['speedLimit'].value")));
    boolQueryBuilder.filter(
        new RangeQueryBuilder(RECORD_DATE_TIME_FIELD)
            .gte(formatDate(fromDate.atStartOfDay(), date_hour_minute_second)));
    AggregationBuilder aggregationBuilder =
        AggregationBuilders.cardinality(COUNT_AGG)
            .field(PLATE_NO_FIELD)
            .precisionThreshold(Integer.MAX_VALUE);
    SearchResponse searchResponse =
        OpenSearchUtil.searchData(
            VEHICLE_TRACKING_HISTORY,
            boolQueryBuilder,
            of(aggregationBuilder),
            of(),
            false,
            restHighLevelClient);
    return ((ParsedCardinality) searchResponse.getAggregations().get(COUNT_AGG)).getValue();
  }

  @Override
  public GroupedData getVehiclesBreachSpeedLimitNTimeFromDate(
      Set<String> plateNumbers, LocalDate fromDate, int breachLimit) {
    BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
    boolQueryBuilder.filter(new TermsQueryBuilder(PLATE_NO_FIELD, plateNumbers));
    boolQueryBuilder.filter(
        new ScriptQueryBuilder(new Script("doc['speed'].value > doc['speedLimit'].value")));
    boolQueryBuilder.filter(
        new RangeQueryBuilder(RECORD_DATE_TIME_FIELD)
            .gte(formatDate(fromDate.atStartOfDay(), date_hour_minute_second)));
    AggregationBuilder groupAggregation =
        AggregationBuilders.terms(GROUP_AGG)
            .field(PLATE_NO_FIELD)
            .size(Integer.MAX_VALUE)
            .subAggregation(
                PipelineAggregatorBuilders.bucketSelector(
                    "filteredBucket",
                    Map.of("doc_count", "_count"),
                    new Script("params.doc_count >" + breachLimit)));
    PipelineAggregationBuilder totalBucketCount =
        PipelineAggregatorBuilders.statsBucket(TOTAL_BUCKET_COUNT_AGG, GROUP_AGG + "._count");
    SearchResponse searchResponse =
        OpenSearchUtil.searchData(
            VEHICLE_TRACKING_HISTORY,
            boolQueryBuilder,
            of(groupAggregation),
            of(totalBucketCount),
            false,
            restHighLevelClient);
    GroupedData groupedData = new GroupedData();
    ParsedStringTerms parsedStringTerms = searchResponse.getAggregations().get(GROUP_AGG);
    parsedStringTerms
        .getBuckets()
        .forEach(
            bucket ->
                groupedData
                    .getData()
                    .add(new KeyCount(bucket.getKeyAsString(), bucket.getDocCount())));
    groupedData.setTotalDocCount(
        ((ParsedStatsBucket) searchResponse.getAggregations().get(TOTAL_BUCKET_COUNT_AGG))
            .getCount());
    return groupedData;
  }
}
