package com.lumi.rental.core.fleet.controller;

import com.lumi.rental.core.fleet.request.UpdateVehicleStatusRequest;
import com.lumi.rental.core.fleet.request.VehicleReservationRequest;
import com.lumi.rental.core.fleet.service.ReservationService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/reservation")
public class VehicleReservationController {

  private final ReservationService service;

  @PutMapping("/vehicle/status")
  public ResponseEntity<?> updateVehicleStatus(
      @Valid @RequestBody UpdateVehicleStatusRequest request) {
    return ResponseEntity.ok(service.updateVehicleStatus(request));
  }

  @PostMapping("/validate/lock")
  public ResponseEntity<?> validateVehicleLock(
      @Valid @RequestBody VehicleReservationRequest request) {
    return ResponseEntity.ok(service.validateVehicleLock(request));
  }

  @PostMapping("/hold")
  public ResponseEntity<?> holdVehicleReservation(
      @Valid @RequestBody VehicleReservationRequest request) {
    return ResponseEntity.ok(service.holdVehicleForReservation(request));
  }

  @PutMapping("/hold/{oldReferenceId}")
  public ResponseEntity<?> holdReplaceVehicleReservation(
      @PathVariable String oldReferenceId, @Valid @RequestBody VehicleReservationRequest request) {
    return ResponseEntity.ok(service.holdReplaceVehicleForReservation(oldReferenceId, request));
  }

  @PostMapping("/assign")
  public ResponseEntity<?> assignVehicleReservation(
      @Valid @RequestBody VehicleReservationRequest request) {
    return ResponseEntity.ok(service.assignVehicleForReservation(request));
  }

  @PutMapping("/cancel")
  public ResponseEntity<?> cancelReservation(
      @Valid @RequestBody VehicleReservationRequest request) {
    return ResponseEntity.ok(service.cancelReservation(request));
  }

  @PostMapping("/confirm")
  public ResponseEntity<?> confirmReservation(
      @Valid @RequestBody VehicleReservationRequest request) {
    return ResponseEntity.ok(service.confirmReservation(request));
  }

  @PutMapping
  public ResponseEntity<?> updateReservationData(
      @Valid @RequestBody VehicleReservationRequest request) {
    return ResponseEntity.ok(service.updateReservation(request));
  }
}
