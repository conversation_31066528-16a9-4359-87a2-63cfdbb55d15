package com.lumi.rental.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.lumi.rental.core.fleet.enums.*;
import java.io.Serializable;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VehicleStatusDTO implements Serializable {
  private String status;
  private String statusReason;
  private Long waitingTime;

  public VehicleStatusDTO(
      StatusType statusType, NeedPreparationReason needPreparationReason, Long date) {
    this.status = statusType.name();
    this.statusReason = needPreparationReason.getDisplayName();
    this.waitingTime = date;
  }

  public VehicleStatusDTO(
      StatusType statusType, YaqeenOutOfServiceReason outOfServiceReason, Long date) {
    this.status = statusType.name();
    this.statusReason = outOfServiceReason.getDisplayName();
    this.waitingTime = date;
  }

  public VehicleStatusDTO(Integer statusId, Integer subStatusId, Long date) {
    if (StatusType.NRM_OPENED.getId().equals(statusId)) {
      this.status = StatusType.NRM_OPENED.name();
      this.statusReason = NonRevenueMovementReason.fromId(subStatusId).name();
      this.waitingTime = date;
    } else if (StatusType.NEED_PREPARATION.getId().equals(statusId)) {
      this.status = StatusType.NEED_PREPARATION.name();
      this.statusReason = NeedPreparationReason.fromId(subStatusId).name();
      this.waitingTime = date;
    } else if (StatusType.OUT_OF_SERVICE.getId().equals(statusId)) {
      this.status = StatusType.OUT_OF_SERVICE.name();
      this.statusReason = YaqeenOutOfServiceReason.fromId(subStatusId).name();
      this.waitingTime = date;
    } else if (StatusType.IN_SALE_CYCLE.getId().equals(statusId)) {
      this.status = StatusType.IN_SALE_CYCLE.name();
      this.statusReason = SaleCycleStatus.fromId(subStatusId).name();
      this.waitingTime = date;
    } else {
      this.status = Objects.requireNonNull(StatusType.fromId(statusId)).name();
      this.waitingTime = date;
    }
  }
}
