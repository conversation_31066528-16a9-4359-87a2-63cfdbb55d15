package com.lumi.rental.core.fleet.repository;

import com.lumi.rental.core.fleet.entity.ModelFeature;
import java.util.List;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ModelFeatureRepository extends JpaRepository<ModelFeature, Integer> {

  @EntityGraph("NEG_VEHICLE_MODEL_FEATURE")
  List<ModelFeature> findAll();

  @EntityGraph("NEG_VEHICLE_MODEL_FEATURE")
  List<ModelFeature> findAllByModelId(Integer modelId);
}
