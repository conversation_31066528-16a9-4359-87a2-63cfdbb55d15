package com.lumi.rental.core.fleet.util;

import static com.seera.lumi.core.fleet.constants.CacheConstants.CACHE_SEPARATOR;
import static com.seera.lumi.core.fleet.constants.CacheConstants.FLEET_CACHE_PREFIX;

public class CacheKeyUtil {
  public static String getKeyForCacheHash(String... keys) {
    if (keys == null || keys.length == 0) {
      return "";
    }
    return String.join("", keys);
  }

  public static String getKeyForHoldReservation(String plateNo) {
    return getKeyForCacheHash(
        FLEET_CACHE_PREFIX, "HoldReservation", CACHE_SEPARATOR, plateNo.replace(" ", ""));
  }
}
