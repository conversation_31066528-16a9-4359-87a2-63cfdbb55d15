package com.lumi.rental.core.fleet.request;

import com.lumi.rental.core.fleet.util.CommonUtils;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class FindMyVehiclesRequest extends BasePageRequest {

  @Serial private static final long serialVersionUID = -650126160341757685L;

  @NotNull private List<Integer> branchIds;
  private List<String> plateNos;
  private List<Integer> modelIds;
  private List<String> groupCodes;

  @Override
  public String toString() {
    return CommonUtils.getStrFromObj(this);
  }
}
