package com.lumi.rental.core.fleet.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lumi.rental.core.fleet.dto.MultilingualDTO;
import com.lumi.rental.core.fleet.dto.SpecificationDTOV2;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VehicleModelBasicResponseV3 implements Serializable {

  @Serial private static final long serialVersionUID = -1L;

  private Integer id;
  private MultilingualDTO name;
  private VehicleMakeResponse make;
  private VehicleClassResponse vehicleClass;
  private Integer faceModelId;
  private Integer fleetCount;
  private String vehicleGroup;
  private VehicleGroupBaseResponse groupResponse;

  @JsonProperty("version")
  private String modelVersion;

  @JsonProperty("series")
  private String modelSeries;

  @JsonProperty("materialId")
  private String sapMaterialId;

  private String primaryImageUrl;
  private SpecificationDTOV2 specification;
}
