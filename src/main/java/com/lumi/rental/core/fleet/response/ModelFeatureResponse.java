package com.lumi.rental.core.fleet.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.lumi.rental.core.fleet.dto.FeatureDTO;
import com.lumi.rental.core.fleet.dto.FeatureValueDTO;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModelFeatureResponse implements Serializable {

  @Serial private static final long serialVersionUID = -1L;

  private FeatureDTO feature;
  private List<FeatureValueDTO> featureValue;
  private Boolean primary;
}
