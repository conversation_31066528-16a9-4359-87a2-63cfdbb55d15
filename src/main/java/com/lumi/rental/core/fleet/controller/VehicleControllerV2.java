package com.lumi.rental.core.fleet.controller;

import static org.springframework.http.ResponseEntity.ok;

import com.lumi.rental.core.fleet.response.VehicleResponseV2;
import com.lumi.rental.core.fleet.service.VehicleMetadataService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v2/vehicles")
public class VehicleControllerV2 {

  private final VehicleMetadataService metadataService;

  // active
  @GetMapping("/{plate-number}")
  public ResponseEntity<VehicleResponseV2> getVehicleByPlateNumber(
      @PathVariable("plate-number") String plateNumber,
      @RequestParam(value = "requireAgreement", required = false, defaultValue = "false")
          boolean requireAgreementDetails) {
    return ok(
        metadataService.getVehicleByPlateNumberWithAgreementStatus(
            plateNumber, requireAgreementDetails));
  }

  @GetMapping
  public ResponseEntity<?> getVehicleV2(
      @RequestParam(name = "plateNo") String plateNo,
      @RequestParam(value = "requireOpsData", required = false, defaultValue = "false")
          boolean requireOpsData,
      @RequestParam(value = "requireFinancialData", required = false, defaultValue = "false")
          boolean requireFinancialData,
      @RequestParam(value = "requireInspectionData", required = false, defaultValue = "false")
          boolean requireInspectionData) {
    return ok(
        metadataService.getVehicleV2(
            plateNo, requireOpsData, requireFinancialData, requireInspectionData));
  }

  @GetMapping("/list")
  public ResponseEntity<?> getVehiclesV2(
      @RequestParam(name = "plateNos") List<String> plateNos,
      @RequestParam(value = "requireOpsData", required = false, defaultValue = "false")
          boolean requireOpsData,
      @RequestParam(value = "requireFinancialData", required = false, defaultValue = "false")
          boolean requireFinancialData) {
    return ok(metadataService.getVehiclesV2(plateNos, requireOpsData, requireFinancialData));
  }
}
