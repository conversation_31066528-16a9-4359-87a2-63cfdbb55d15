package com.lumi.rental.core.fleet.controller;

import static java.util.Map.entry;

import com.lumi.rental.core.fleet.entity.VehicleMake;
import com.lumi.rental.core.fleet.entity.VehicleModel;
import com.lumi.rental.core.fleet.entity.VehicleSyncEntity;
import com.lumi.rental.core.fleet.repository.VehicleMakeRepository;
import com.lumi.rental.core.fleet.repository.VehicleModelRepository;
import com.lumi.rental.core.fleet.repository.VehicleSyncRepository;
import com.lumi.rental.core.fleet.response.BaseResponse;
import com.lumi.rental.core.fleet.service.AIService;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/test")
public class TranslateController {
  private static final Logger log = LoggerFactory.getLogger("application");
  private static final Map<Character, Character> ARABIC_CHARACTERS =
      Map.ofEntries(
          entry('A', 'أ'),
          entry('B', 'ب'),
          entry('C', 'ج'),
          entry('D', 'د'),
          entry('E', 'ع'),
          entry('F', 'ف'),
          entry('G', 'ق'),
          entry('H', 'ه'),
          entry('I', 'ع'),
          entry('J', 'ح'),
          entry('K', 'ك'),
          entry('L', 'ل'),
          entry('M', 'م'),
          entry('N', 'ن'),
          entry('O', 'و'),
          entry('P', 'ب'),
          entry('Q', 'ق'),
          entry('R', 'ر'),
          entry('S', 'س'),
          entry('T', 'ط'),
          entry('U', 'و'),
          entry('V', 'ى'),
          entry('W', 'و'),
          entry('X', 'ص'),
          entry('Y', 'ي'),
          entry('Z', 'م'));
  private final VehicleSyncRepository vehicleSyncRepository;
  private final VehicleMakeRepository makeRepository;
  private final VehicleModelRepository modelRepository;
  private final AIService aiService;

  @GetMapping("/translate-plate-no")
  @Scheduled(cron = "0 0 1 * * *")
  @Profile("prod")
  public ResponseEntity<BaseResponse> translateVehiclePlateNoInArabic() {
    log.info("Starting vehicle plate number translation to Arabic");
    processVehicleTranslations();
    return ResponseEntity.ok(new BaseResponse("Translation process initiated successfully"));
  }

  @GetMapping("/translate-make")
  @Scheduled(cron = "0 0 2 * * *")
  @Profile("prod")
  public ResponseEntity<BaseResponse> translateAndUpdateMake() {
    log.info("Starting translation and update of make names to Arabic");
    processMakeTranslations();
    return ResponseEntity.ok(new BaseResponse("Make translation process initiated successfully"));
  }

  @GetMapping("/translate-models-name")
  @Scheduled(cron = "0 0 3 * * *")
  @Profile("prod")
  public ResponseEntity<BaseResponse> translateAndUpdateModels() {
    log.info("Starting translation and update of model names to Arabic");
    processModelTranslations();
    return ResponseEntity.ok(new BaseResponse("Model translation process initiated successfully"));
  }

  @Async
  protected void processVehicleTranslations() {
    processTranslations(
        vehicleSyncRepository.findByPlateNoArIsNull(), "vehicles", this::translateAndSaveVehicle);
  }

  @Async
  protected void processMakeTranslations() {
    processTranslations(
        makeRepository.findAllMakesWithEnglishNameAndWithoutArabicName(),
        "makes",
        this::translateAndSaveMake);
  }

  @Async
  protected void processModelTranslations() {
    processTranslations(
        modelRepository.findAllModelsWithEnglishNameAndWithoutArabicName(),
        "models",
        this::translateAndSaveModel);
  }

  private <T> void processTranslations(List<T> items, String entityType, Consumer<T> translator) {
    log.info("Found {} {} requiring Arabic translation", items.size(), entityType);
    items.forEach(translator);
  }

  private void translateAndSaveVehicle(VehicleSyncEntity vehicle) {
    try {
      String plateNo = vehicle.getPlateNo();
      if (StringUtils.isBlank(plateNo)) {
        log.warn("Skipping vehicle with null plate number");
        return;
      }

      String plateNoAr = translateToArabic(plateNo);
      if (StringUtils.isNotBlank(plateNoAr)) {
        vehicle.setPlateNoAr(plateNoAr);
        vehicleSyncRepository.saveAndFlush(vehicle);
        log.debug("Successfully translated plate number for vehicle: {}", plateNo);
      }
    } catch (Exception e) {
      log.error("Error translating plate number for vehicle: {}", vehicle.getPlateNo(), e);
    }
  }

  private void translateAndSaveMake(VehicleMake make) {
    try {
      String nameAr = aiService.translateMakeNameToArabic(make.getNameEn());
      if (StringUtils.isNotBlank(nameAr)) {
        make.setNameAr(nameAr);
        makeRepository.saveAndFlush(make);
        log.info("Translated make {} to {}", make.getNameEn(), nameAr);
      }
    } catch (Exception e) {
      log.error("Error translating make {}: {}", make.getNameEn(), e.getMessage());
    }
  }

  private void translateAndSaveModel(VehicleModel model) {
    try {
      String nameAr = aiService.translateModelNameToArabic(model.getNameEn());
      if (StringUtils.isNotBlank(nameAr)) {
        model.setNameAr(nameAr);
        modelRepository.saveAndFlush(model);
        log.info("Translated model {} to {}", model.getNameEn(), nameAr);
      }
    } catch (Exception e) {
      log.error("Error translating model {}: {}", model.getNameEn(), e.getMessage());
    }
  }

  private String translateToArabic(String text) {
    return text.chars()
        .mapToObj(c -> ARABIC_CHARACTERS.getOrDefault((char) c, (char) c))
        .collect(StringBuilder::new, StringBuilder::append, StringBuilder::append)
        .toString();
  }
}
