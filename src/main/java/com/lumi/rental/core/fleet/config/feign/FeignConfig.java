package com.lumi.rental.core.fleet.config.feign;

import feign.Client;
import feign.Feign;
import feign.Logger;
import feign.Retryer;
import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Scope;

@Configuration
public class FeignConfig {

  @Bean
  public Retryer feignRetryer() {
    return new Retryer.Default(5000L, 30000L, 3);
  }

  @Bean
  @Scope("prototype")
  public Feign.Builder feignBuilder() {
    return Feign.builder().logLevel(Logger.Level.FULL);
  }

  @Bean
  @Primary
  @Scope("prototype")
  public Encoder multipartFormEncoder(ObjectFactory<HttpMessageConverters> messageConverters) {
    return new SpringFormEncoder(new SpringEncoder(messageConverters));
  }

  @Bean
  @Primary
  @Scope("prototype")
  public Client feignClient() {
    return new Client.Default(null, null);
  }

  @Bean
  public Decoder feignDecoder(ObjectFactory<HttpMessageConverters> messageConverters) {
    return new CustomJacksonDecoder(messageConverters);
  }
}
