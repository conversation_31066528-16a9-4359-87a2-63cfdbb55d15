package com.lumi.rental.core.fleet.repository.specification;

import com.lumi.rental.core.fleet.entity.DocumentType;
import com.lumi.rental.core.fleet.request.DocumentTypeSearchRequestDTO;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

@AllArgsConstructor
public class DocumentTypeSpecification implements Specification<DocumentType> {

  private static final String COLUMN_CODE = "code";
  private static final String NAME = "name";
  private static final String EN = "en";
  private static final String AR = "ar";
  private final DocumentTypeSearchRequestDTO filterDTO;

  @Override
  public Predicate toPredicate(
      Root<DocumentType> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
    List<Predicate> predicates = new ArrayList<>();
    if (StringUtils.isNotBlank(filterDTO.getQuery())) {
      String query = filterDTO.getQuery().trim();
      String likeQuery = "%" + query.toLowerCase() + "%";
      predicates.add(
          criteriaBuilder.or(
              criteriaBuilder.like(root.get(NAME).get(EN), likeQuery),
              criteriaBuilder.like(root.get(NAME).get(AR), likeQuery),
              criteriaBuilder.like(root.get(COLUMN_CODE), likeQuery)));
    }
    if (StringUtils.isNotBlank(filterDTO.getCode())) {
      predicates.add(criteriaBuilder.like(root.get(COLUMN_CODE), filterDTO.getCode()));
    }
    if (StringUtils.isNotBlank(filterDTO.getName())) {
      predicates.add(
          criteriaBuilder.or(
              criteriaBuilder.like(root.get(NAME).get(EN), filterDTO.getName()),
              criteriaBuilder.like(root.get(NAME).get(AR), filterDTO.getName())));
    }
    return criteriaBuilder.and(predicates.toArray(Predicate[]::new));
  }
}
