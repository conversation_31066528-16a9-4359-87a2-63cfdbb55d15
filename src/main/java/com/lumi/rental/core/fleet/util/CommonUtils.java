package com.lumi.rental.core.fleet.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import java.io.IOException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CommonUtils {

  private static final ObjectMapper mapper =
      JsonMapper.builder()
          .configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true)
          .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
          .build();

  public static String getStrFromObj(Object obj) {
    String data = null;
    try {
      data = mapper.writeValueAsString(obj);
    } catch (JsonProcessingException e) {
      // Swallow Exception
    }
    return data;
  }

  public static <T> T getObjFromStr(String strObj, Class<T> resClass) throws IOException {
    return mapper.readValue(strObj, resClass);
  }
}
