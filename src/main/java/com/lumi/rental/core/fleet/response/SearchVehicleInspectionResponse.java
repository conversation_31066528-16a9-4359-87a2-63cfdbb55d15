package com.lumi.rental.core.fleet.response;

import com.lumi.rental.core.fleet.dto.MultilingualDTO;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class SearchVehicleInspectionResponse implements Serializable {
  @Serial private static final long serialVersionUID = -1L;

  private String plateNo;
  private String plateNoAr;
  private MultilingualDTO model;
  private Long checkOutTime;
  private Boolean isAssigned;
  private Boolean isInspected;
  private Long lastInspected;
  private Integer inspectionId;
  private Long checkInTime;

  public SearchVehicleInspectionResponse(
      String plateNo,
      String plateNoAr,
      String modelEn,
      String modelAr,
      String time,
      Long isAssigned,
      Integer inspectionId,
      Long lastInspected,
      Long checkInTime) {
    this.plateNo = plateNo;
    this.plateNoAr = plateNoAr;
    this.model = new MultilingualDTO(modelEn, modelAr);
    this.checkOutTime = StringUtils.isBlank("0") ? null : Long.parseLong(time);
    this.isAssigned = isAssigned.equals(1L) ? Boolean.TRUE : Boolean.FALSE;
    this.isInspected = lastInspected != null && ((System.currentTimeMillis() - 5 * 60 * 60 * 1000) < lastInspected)
            ? Boolean.TRUE
            : Boolean.FALSE;
    this.lastInspected = lastInspected;
    this.inspectionId = inspectionId;
    this.checkInTime = checkInTime;
  }

  public SearchVehicleInspectionResponse(
          String plateNo,
          String plateNoAr,
          String modelEn,
          String modelAr,
          String checkInTime) {
    this.plateNo = plateNo;
    this.plateNoAr = plateNoAr;
    this.model = new MultilingualDTO(modelEn, modelAr);
    this.checkInTime = StringUtils.isBlank("0") ? null : Long.parseLong(checkInTime);
  }
}
