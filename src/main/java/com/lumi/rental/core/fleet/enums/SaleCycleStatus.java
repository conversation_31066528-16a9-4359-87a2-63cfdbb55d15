package com.lumi.rental.core.fleet.enums;

import lombok.Getter;

@Getter
public enum SaleCycleStatus {
  SALE_PREPARATION(1, "Sale Preparation"),
  SALE_IN_PROGRESS(2, "Sale In Progress");

  private final Integer id;
  private final String displayName;

  SaleCycleStatus(Integer id, String displayName) {
    this.id = id;
    this.displayName = displayName;
  }

  public static SaleCycleStatus fromId(Integer id) {
    for (SaleCycleStatus type : values()) {
      if (type.id.equals(id)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Unknown value for SaleCycleStatus: " + id);
  }
}
