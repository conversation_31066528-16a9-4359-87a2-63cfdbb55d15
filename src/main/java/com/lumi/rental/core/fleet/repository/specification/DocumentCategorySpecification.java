package com.lumi.rental.core.fleet.repository.specification;

import com.lumi.rental.core.fleet.entity.DocumentCategory;
import com.seera.lumi.core.fleet.dto.DocumentTypeCategorySearchRequestDTO;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

@AllArgsConstructor
public class DocumentCategorySpecification implements Specification<DocumentCategory> {

  private static final String COLUMN_CODE = "code";
  private final DocumentTypeCategorySearchRequestDTO filterDTO;

  @Override
  public Predicate toPredicate(
      Root<DocumentCategory> root,
      CriteriaQuery<?> criteriaQuery,
      CriteriaBuilder criteriaBuilder) {
    Predicate p = criteriaBuilder.conjunction();
    if (StringUtils.isNotBlank(filterDTO.getQuery())) {
      String query = filterDTO.getQuery().trim();
      String likeQuery = "%" + query.toLowerCase() + "%";
      return criteriaBuilder.or(criteriaBuilder.like(root.get(COLUMN_CODE), likeQuery));
    }
    return p;
  }
}
