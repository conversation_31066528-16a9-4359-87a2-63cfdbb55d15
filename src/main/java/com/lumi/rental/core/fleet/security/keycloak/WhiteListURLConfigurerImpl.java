package com.lumi.rental.core.fleet.security.keycloak;

import static java.util.Objects.isNull;

import com.seera.lumi.core.security.multitenant.WhiteListURLConfigurer;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.http.HttpMethod;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.stereotype.Component;

@Component
public class WhiteListURLConfigurerImpl implements WhiteListURLConfigurer {

  public static final Set<UrlData> WHITELIST_URLS =
      Set.of(
          new UrlData("/v1/admin/tenants/**", HttpMethod.GET.name()),
          UrlData.of("/public/**"),
          new UrlData("/**", HttpMethod.GET.name()),
          UrlData.of("/error/**"),
          UrlData.of(
              "/v1/vehicles/suggestions"), // Used in UCS can be removed when UCS team start sending
          // token in request
          UrlData.of(
              "/v2/vehicles/**"), // Used in UCS can be removed when UCS team start sending token in
          // request
          UrlData.of("/v3/api-docs/**"),
          UrlData.of("/swagger-resources/**"),
          UrlData.of("/swagger-ui.html"),
          UrlData.of("/swagger-ui/**"),
          UrlData.of("/webjars/**"),
          UrlData.of("/favicon.ico"),
          UrlData.of("/actuator/**"),
          UrlData.of("/csrf/**"));

  protected static final Set<AntPathRequestMatcher> WHITELIST_URLS_MATCHERS =
      WHITELIST_URLS.stream()
          .map(urlData -> new AntPathRequestMatcher(urlData.url, urlData.httpMethod))
          .collect(Collectors.toSet());

  protected static final Set<String> WHITELIST_URL_STRING =
      WHITELIST_URLS.stream().map(UrlData::getUrl).collect(Collectors.toSet());

  public static String[] getMethodBasedFilteredUrl(HttpMethod httpMethod) {
    return WHITELIST_URLS.stream()
        .filter(
            urlData ->
                isNull(urlData.getHttpMethod())
                    || urlData.getHttpMethod().equals(httpMethod.name()))
        .map(UrlData::getUrl)
        .toList()
        .toArray(new String[0]);
  }

  @Override
  public Set<AntPathRequestMatcher> getWhiteListURLMatchers() {
    return WHITELIST_URLS_MATCHERS;
  }

  @Override
  public Set<String> getWhiteListURLs() {
    return WHITELIST_URL_STRING;
  }

  public static class UrlData {

    String url;
    String httpMethod;

    public UrlData(String url, String httpMethod) {
      this.url = url;
      this.httpMethod = httpMethod;
    }

    public UrlData(String url) {
      this.url = url;
    }

    public static UrlData of(String url) {
      return new UrlData(url);
    }

    public String getUrl() {
      return url;
    }

    public String getHttpMethod() {
      return httpMethod;
    }
  }
}
