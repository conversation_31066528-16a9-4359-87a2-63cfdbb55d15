package com.lumi.rental.core.fleet.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Table(name = "reservation")
public class VehicleReservationData extends BaseEntity {

  @Column(name = "plate_no")
  private String plateNo;

  @Column(name = "checkout_date")
  private LocalDateTime checkOutDate;

  @Column(name = "checkin_date")
  private LocalDateTime checkInDate;

  @Column(name = "checkout_branch")
  private Integer checkOutBranch;

  @Column(name = "checkin_branch")
  private Integer checkInBranch;

  @Column(name = "reference_id")
  private String referenceId;
}
