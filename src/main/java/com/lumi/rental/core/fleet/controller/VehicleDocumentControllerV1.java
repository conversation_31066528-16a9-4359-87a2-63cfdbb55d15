package com.lumi.rental.core.fleet.controller;

import static org.springframework.http.ResponseEntity.ok;

import com.lumi.rental.core.fleet.dto.DocumentTypeDTO;
import com.lumi.rental.core.fleet.request.CreateUpdateVehicleDocument;
import com.lumi.rental.core.fleet.request.DocumentTypeSearchRequestDTO;
import com.lumi.rental.core.fleet.service.DocumentService;
import jakarta.validation.Valid;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/vehicles/documents")
public class VehicleDocumentControllerV1 {

  private final DocumentService documentService;

  @GetMapping("/type")
  public ResponseEntity<?> listDocumentTypes(DocumentTypeSearchRequestDTO requestDTO) {
    return ok(documentService.listDocumentTypes(requestDTO));
  }

  @PostMapping("/type")
  public ResponseEntity<DocumentTypeDTO> createDocumentType(
      @RequestBody DocumentTypeDTO requestDTO) {
    return ok(documentService.createDocumentType(requestDTO));
  }

  @PatchMapping(
      value = "/type/{id}",
      consumes = {"application/json", "application/merge-patch+json"})
  public ResponseEntity<DocumentTypeDTO> partialUpdateDocumentType(
      @PathVariable(value = "id") final Integer id, @RequestBody DocumentTypeDTO documentTypeDTO) {
    documentTypeDTO.setId(id);
    return ok(documentService.updateDocumentType(documentTypeDTO));
  }

  @GetMapping("/{plate-number}")
  public ResponseEntity<?> getVehicleDocumentsByPlateNumber(
      @PathVariable("plate-number") String plateNumber) {
    return ok(documentService.search(plateNumber));
  }

  @PostMapping
  public ResponseEntity<?> createDocument(
      @Valid @RequestBody CreateUpdateVehicleDocument documentDTO) {
    return ok(documentService.createDocument(documentDTO));
  }

  @PatchMapping(
      value = "/{id}",
      consumes = {"application/json", "application/merge-patch+json"})
  public ResponseEntity<?> partialUpdateDocument(
      @PathVariable(value = "id") final Integer id,
      @RequestBody CreateUpdateVehicleDocument documentDTO) {
    return ok(documentService.updateDocument(id, documentDTO));
  }

  @PostMapping(value = "/upload/{plate-number}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResponseEntity<?> upload(
      @PathVariable("plate-number") String plateNumber,
      @Valid @RequestParam("file") MultipartFile file)
      throws IOException {
    return ok(documentService.upload(plateNumber, file));
  }
}
