package com.lumi.rental.core.fleet.dto;

import static java.lang.Boolean.FALSE;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.lumi.rental.core.fleet.enums.StatusType;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.PositiveOrZero;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.*;

@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class InspectionMetadataDTO implements Serializable {
  @Serial private static final long serialVersionUID = -1L;

  @PositiveOrZero private Integer odometerReading;

  @Min(0)
  @Max(4)
  @PositiveOrZero
  private Integer fuelLevel;

  private Boolean newDamage = FALSE;
  private Boolean replacement = FALSE;
  private String remark;
  private StatusType vehicleStatus;
  private List<ImageDTO> images;
  private String completedBy;
}
