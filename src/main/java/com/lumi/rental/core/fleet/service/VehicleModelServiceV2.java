package com.lumi.rental.core.fleet.service;

import static com.seera.lumi.core.fleet.constants.CacheConstants.*;

import com.lumi.rental.core.fleet.entity.VehicleModel;
import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.exception.VehicleModelNotExistException;
import com.lumi.rental.core.fleet.mapper.VehicleModelMapper;
import com.lumi.rental.core.fleet.repository.VehicleGroupRepository;
import com.lumi.rental.core.fleet.repository.VehicleModelRepository;
import com.lumi.rental.core.fleet.request.CreateUpdateVehicleModelRequest;
import com.lumi.rental.core.fleet.response.VehicleModelBasicResponseV2;
import com.lumi.rental.core.fleet.response.VehicleModelResponseV2;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleModelServiceV2 {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final VehicleModelMapper vehicleModelMapper;
  private final VehicleModelRepository modelRepository;
  private final VehicleMakeService vehicleMakeService;
  private final VehicleGroupRepository groupRepository;

  @Cacheable(
      cacheNames = MODEL_BASIC_RESPONSE_V2_ALL,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public Page<VehicleModelBasicResponseV2> getAllVehicleModel(
      Integer makeId, PageRequest pageable) {
    return (makeId != null
            ? modelRepository.findAllModelsOfMake(makeId, pageable)
            : modelRepository.findAll(pageable))
        .map(vehicleModelMapper::buildVehicleModelBasicResponseV2);
  }

  @Cacheable(
      cacheNames = MODEL_BASIC_RESPONSE_V2_ALL_ALL,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public Page<VehicleModelBasicResponseV2> getAllVehicleModelAll(PageRequest pageable) {
    return modelRepository
        .findAllV2(pageable)
        .map(vehicleModelMapper::buildVehicleModelBasicResponseV2);
  }

  //    @Cacheable(cacheNames = MODEL_ALL, cacheManager = "oneDayCacheManager")
  public List<VehicleModel> getAllVehicleModel() {
    return modelRepository.findAllV2();
  }

  @Cacheable(
      cacheNames = MODEL_RESPONSE_V2_BY_ID,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public VehicleModelResponseV2 getVehicleModelResponseById(Integer vehicleModelId) {
    VehicleModel model = getVehicleModelById(vehicleModelId);
    List<VehicleModelBasicResponseV2> variantResponse =
        modelRepository.findByFaceModelId(model.getFaceModelId()).stream()
            .map(vehicleModelMapper::buildVehicleModelBasicResponseV2)
            .toList();
    VehicleModelResponseV2 response = vehicleModelMapper.buildVehicleModelResponseV2(model);
    response.setVariants(variantResponse);
    return response;
  }

  @Cacheable(
      cacheNames = MODEL_BASIC_RESPONSE_V2_BY_ID,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public VehicleModelBasicResponseV2 getVehicleModelBasicResponseById(Integer modelId) {
    return modelRepository
        .findById(modelId)
        .map(vehicleModelMapper::buildVehicleModelBasicResponseV2)
        .orElse(null);
  }

  //    @Cacheable(cacheNames = MODEL_BY_ID, keyGenerator = "customKeyGenerator", cacheManager =
  // "oneDayCacheManager")
  public VehicleModel getVehicleModelById(Integer modelId) {
    return modelRepository
        .findById(modelId)
        .orElseThrow(
            () ->
                new VehicleModelNotExistException(
                    String.format("Vehicle Model does not exist: %s", modelId)));
  }

  @Cacheable(
      cacheNames = MODEL_BY_FACE_MODEL_ID,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public List<Integer> findFaceModelIdsByIds(List<Integer> modelIds) {
    return modelRepository.findFaceModelIdsByIds(modelIds);
  }

  @Cacheable(
      cacheNames = MODEL_BY_FAMILY_MEMBER,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public List<Integer> getModelFamilyIds(List<Integer> modelIds) {
    return modelRepository.findByFaceModelIds(modelIds).stream()
        .distinct()
        .collect(Collectors.toList());
  }

  //    @Transactional
  //    public VehicleModelResponse createVehicleModel(CreateUpdateVehicleModelRequest request) {
  //        VehicleMake make = makeRepository.findById(request.getMakeId())
  //                .orElseThrow(() -> {
  //                    log.info("Make does not exist for {}", request);
  //                    return new VehicleMakeNotExistException("Vehicle make does not exist for
  // make-id " + request.getMakeId());
  //                });
  //        VehicleModel vehicleModel = mapper.buildVehicleModel(request, make);
  //        try {
  //            VehicleModel savedVehicleModel = modelRepository.saveAndFlush(vehicleModel);
  //            return mapper.buildVehicleModelResponse(savedVehicleModel);
  //        } catch (DataIntegrityViolationException ex) {
  //            log.error("VehicleModel with code {} already exists.", request.getName().getEn());
  //            throw new BusinessException(BaseError.ALREADY_EXISTS, "VehicleModel");
  //        }
  //    }

  public VehicleModelBasicResponseV2 updateVehicleModel(
      int id, final CreateUpdateVehicleModelRequest request) {
    log.info("Updating vehicle model with ID: {} and request: {}", id, request);
    return modelRepository
        .findById(id)
        .map(
            existingVehicleModel -> {
              VehicleModel updatedVehicleModel = vehicleModelMapper.buildVehicleModel(request);
              updatedVehicleModel.setId(id);
              if (request.getMakeId() != null) {
                updatedVehicleModel.setMake(vehicleMakeService.findById(request.getMakeId()));
              }
              if (request.getVehicleGroup() != null) {
                updatedVehicleModel.setVehicleGroup(
                    groupRepository
                        .findByCode(request.getVehicleGroup())
                        .orElseThrow(
                            () -> new BusinessException(BaseError.NOT_FOUND, "VehicleGroup")));
              }
              VehicleModel savedVehicleModel = modelRepository.saveAndFlush(updatedVehicleModel);
              return vehicleModelMapper.buildVehicleModelBasicResponseV2(savedVehicleModel);
            })
        .orElseThrow(() -> new BusinessException(BaseError.NOT_FOUND, "VehicleModelId"));
  }
}
