package com.lumi.rental.core.fleet.controller;

import com.lumi.rental.core.fleet.response.BaseResponse;
import com.lumi.rental.core.fleet.service.VehicleSyncService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/sync")
public class SyncController {

  private static final Logger log = LoggerFactory.getLogger("application");
  private final VehicleSyncService syncService;

  @GetMapping("/vehicle-metadata-carpro")
  @Scheduled(cron = "0 0 1 * * *")
  @Profile("prod")
  public ResponseEntity<BaseResponse> syncVehicleChassisNoAndPlateNoArFromCARPRO() {
    log.info("Syncing vehicle chassis_no and policy-no from car-pro");
    syncService.syncVehicleChassisNoAndPlateNoArFromCARPRO();
    return ResponseEntity.ok(new BaseResponse("Success"));
  }

  @GetMapping("/vehicle-model-year")
  @Scheduled(cron = "0 0 2 * * *")
  @Profile("prod")
  public ResponseEntity<BaseResponse> syncVehicleModelYear() {
    log.info("Populating vehicle model-year from chassis-no");
    syncService.syncVehicleModelYear();
    return ResponseEntity.ok(new BaseResponse("Success"));
  }

  @GetMapping("/material-carpro")
  @Scheduled(cron = "0 0 3 * * *")
  @Profile("prod")
  public ResponseEntity<BaseResponse> syncMaterialIdFromCARPRO() {
    log.info("Syncing new material-ids from car-pro");
    syncService.syncModelMetadata();
    return ResponseEntity.ok(new BaseResponse("Success"));
  }

  @GetMapping("/model-specifications")
  @Scheduled(cron = "0 0 1 * * *")
  @Profile("prod")
  public ResponseEntity<BaseResponse> syncSpecification() {
    log.info("Syncing vehicle specifications from material-name using AI");
    syncService.updateAllVehicleModelSpecifications();
    return ResponseEntity.ok(new BaseResponse("Success"));
  }

  @GetMapping("/model-fleet-count")
  @Scheduled(cron = "0 0 1 * * *")
  @Profile("prod")
  public ResponseEntity<BaseResponse> populateFleetCount() {
    log.info("Populating fleet count for all models");
    syncService.populateFleetCountForVehicleModel();
    return ResponseEntity.ok(new BaseResponse("Success"));
  }
}
