package com.lumi.rental.core.fleet.mapper;

import com.lumi.rental.core.fleet.entity.VehicleOperationalData;
import com.lumi.rental.core.fleet.response.VehicleOperationResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = "spring",
    uses = {FuelMapper.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OperationalMapper {

  @Mapping(target = "model", ignore = true)
  @Mapping(target = "plateNo", ignore = true)
  @Mapping(target = "fuelLevelInfo", source = "fuelLevel")
  void populate(
      @MappingTarget VehicleOperationResponse response,
      VehicleOperationalData vehicleOperationalData);
}
