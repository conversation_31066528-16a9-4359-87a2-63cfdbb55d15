package com.lumi.rental.core.fleet.mapper;

import com.lumi.rental.core.fleet.dto.ModelImageDTO;
import com.lumi.rental.core.fleet.dto.MultilingualDTO;
import com.lumi.rental.core.fleet.entity.VehicleGroup;
import com.lumi.rental.core.fleet.response.*;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = "spring",
    uses = {VehicleModelMapper.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VehicleGroupMapper {

  @Mapping(target = "id", source = "group.id")
  @Mapping(target = "description", source = "group")
  @Mapping(target = "faceModelId", source = "group.faceModelId")
  @Mapping(target = "vehicleModelBasicResponse", source = "faceModelResponse")
  @Mapping(target = "displayName", expression = "java(toGroupName(group, faceModelResponse))")
  @Mapping(target = "models", source = "models")
  VehicleGroupResponse buildVehicleGroupResponse(
      VehicleGroup group,
      VehicleModelBasicResponse faceModelResponse,
      List<VehicleModelBasicResponse> models);

  @Mapping(target = "id", source = "group.id")
  @Mapping(target = "description", source = "group")
  @Mapping(target = "faceModelId", source = "group.faceModelId")
  @Mapping(target = "vehicleModelBasicResponse", source = "faceModelResponse")
  @Mapping(target = "displayName", expression = "java(toGroupName(group, faceModelResponse))")
  @Mapping(target = "thumbnail", expression = "java(getThumbnail(faceModelResponse.getImages()))")
  VehicleGroupResponse buildVehicleGroupResponse(
      VehicleGroup group, VehicleModelBasicResponse faceModelResponse);

  default String getThumbnail(List<ModelImageDTO> images) {
    if (images != null && !images.isEmpty()) {
      return images.get(0).getUrl();
    }
    return null;
  }

  @Mapping(target = "description", source = "group")
  @Mapping(target = "faceModelId", source = "group.faceModelId")
  VehicleGroupBaseResponse buildVehicleGroupBaseResponse(VehicleGroup group);

  @Mapping(target = "id", source = "group.id")
  @Mapping(target = "description", source = "group")
  @Mapping(target = "faceModelId", source = "group.faceModelId")
  @Mapping(target = "faceModelResponse", source = "faceModel")
  VehicleGroupBaseResponse buildVehicleGroupBaseResponse(
      VehicleGroup group, VehicleModelBasicResponseV2 faceModel);

  @Mapping(target = "en", source = "descriptionEn")
  @Mapping(target = "ar", source = "descriptionAr")
  MultilingualDTO toMultilingualDTO(VehicleGroup vehicleGroup);

  @Named("toGroupName")
  default String toGroupName(VehicleGroup group, VehicleModelBasicResponse model) {
    try {
      return String.format(
          "%s_%s_%s",
          group.getCode(),
          model.getMake().getName().getEn(),
          model.getName().getEn().split(" ")[0]);
    } catch (Exception e) {
      return "";
    }
  }
}
