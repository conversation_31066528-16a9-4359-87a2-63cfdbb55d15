package com.lumi.rental.core.fleet.request;

import com.lumi.rental.core.fleet.dto.MultilingualDTO;
import com.lumi.rental.core.fleet.dto.SpecificationDTOV2;
import java.util.List;
import lombok.Data;

@Data
public class CreateUpdateVehicleModelRequest {

  private MultilingualDTO name;
  private Integer makeId;
  private String modelVersion;
  private String modelSeries;
  private String vehicleGroup;
  private String primaryImageUrl;
  private List<String> imageUrls;
  private Boolean enabled;
  private SpecificationDTOV2 specification;
}
