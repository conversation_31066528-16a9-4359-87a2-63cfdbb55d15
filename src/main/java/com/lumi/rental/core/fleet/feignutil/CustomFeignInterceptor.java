package com.lumi.rental.core.fleet.feignutil;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class CustomFeignInterceptor implements RequestInterceptor {

  @Value("${spring.application.name}")
  private String appName;

  @Override
  public void apply(RequestTemplate requestTemplate) {
    requestTemplate.header("client-id", appName);
  }
}
