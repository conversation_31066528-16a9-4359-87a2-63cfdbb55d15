package com.lumi.rental.core.fleet.entity;

import jakarta.persistence.*;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "feature")
@EqualsAndHashCode(callSuper = true)
public class VehicleFeature extends BaseEntity {

  @Column(name = "name_en", length = 64)
  private String nameEn;

  @Column(name = "name_ar", length = 64)
  private String nameAr;

  @OneToMany(mappedBy = "feature", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
  private List<VehicleFeatureValue> featureValue;

  @Column(name = "image_url", length = 128)
  private String imageUrl;

  @Column(name = "is_enabled")
  private Boolean enabled;
}
