package com.lumi.rental.core.fleet.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lumi.rental.core.fleet.dto.ModelFeatureDTO;
import com.lumi.rental.core.fleet.dto.ModelImageDTO;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VehicleModelResponseV2 extends VehicleModelBasicResponseV2 {

  private VehicleGroupBaseResponse groupResponse;

  @JsonProperty("features")
  private ModelFeatureDTO modelFeatures;

  private String primaryImageUrl;

  @JsonProperty("images")
  private List<ModelImageDTO> images;

  @JsonProperty("variants")
  private List<VehicleModelBasicResponseV2> variants;
}
