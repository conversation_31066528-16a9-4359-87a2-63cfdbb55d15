package com.lumi.rental.core.fleet.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.lumi.rental.core.fleet.dto.*;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@JsonInclude(Include.NON_NULL)
public class VehicleResponseV3 implements Serializable {

  @Serial private static final long serialVersionUID = -1L;

  private String plateNo;
  private String plateNoAr;
  private VehicleModelBasicResponseV2 model;
  private LocationDTO location;
  private LineOfBusinessDTO lineOfBusiness;
  private BookingStatusDTO bookingStatus;
  private VehicleStatusDTO vehicleStatus;
  private FuelLevelInfo fuelLevelInfo;
  private Integer fuelLevel;
  private String fuelLevelDisplay;
  private Integer odometerReading;
}
