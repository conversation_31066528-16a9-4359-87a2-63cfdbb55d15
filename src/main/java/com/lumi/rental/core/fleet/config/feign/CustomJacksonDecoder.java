package com.lumi.rental.core.fleet.config.feign;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import feign.Response;
import feign.codec.Decoder;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;

public class CustomJacksonDecoder implements Decoder {

  private final ObjectMapper objectMapper;
  private final Decoder springDecoder;

  public CustomJacksonDecoder(ObjectFactory<HttpMessageConverters> messageConverters) {
    this.springDecoder = new ResponseEntityDecoder(new SpringDecoder(messageConverters));
    this.objectMapper = new ObjectMapper();
  }

  @Override
  public Object decode(Response response, Type type) throws IOException {
    if (response.body() == null) {
      return null;
    }

    // Check if the content type is application/json
    String contentType =
        response.headers().getOrDefault("Content-Type", null).stream().findFirst().orElse("");

    if (contentType.contains("application/json")) {
      try {
        // Try using the standard decoder first
        return springDecoder.decode(response, type);
      } catch (Exception e) {
        // If that fails, try manual deserialization
        try (InputStream inputStream = response.body().asInputStream()) {
          JavaType javaType = TypeFactory.defaultInstance().constructType(type);
          return objectMapper.readValue(inputStream, javaType);
        }
      }
    }

    // For other content types, use the standard decoder
    return springDecoder.decode(response, type);
  }
}
