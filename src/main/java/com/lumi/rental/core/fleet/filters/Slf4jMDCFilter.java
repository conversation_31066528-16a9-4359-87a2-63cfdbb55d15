package com.lumi.rental.core.fleet.filters;

import static com.lumi.rental.core.fleet.constants.Constants.DEFAULT_MDC_CLIENT_ID_KEY;
import static com.lumi.rental.core.fleet.constants.Constants.DEFAULT_MDC_UUID_TOKEN_KEY;

import io.micrometer.tracing.Tracer;
import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

@Component
@RequiredArgsConstructor
public class Slf4jMDCFilter extends OncePerRequestFilter {
  private static final Logger log = LoggerFactory.getLogger("application");
  private final Tracer tracer;

  @Override
  protected void doFilterInternal(
      @NotNull HttpServletRequest request,
      @NotNull HttpServletResponse response,
      @NotNull FilterChain chain) {
    try {
      String traceId = getTraceId(request);
      String clientId = request.getHeader(DEFAULT_MDC_CLIENT_ID_KEY);

      MDC.put(DEFAULT_MDC_UUID_TOKEN_KEY, traceId);
      MDC.put(DEFAULT_MDC_CLIENT_ID_KEY, clientId);

      chain.doFilter(request, response);
    } catch (Exception e) {
      log.error("Error in MDC filter", e);
    }
  }

  private String getTraceId(HttpServletRequest request) {
    String headerTraceId = request.getHeader(DEFAULT_MDC_UUID_TOKEN_KEY);
    if (StringUtils.isNotBlank(headerTraceId)) {
      return headerTraceId;
    }

    if (!request.getRequestURI().contains("actuator")) {
      log.warn(
          "No trace-id received in request {} client-id {}",
          request.getRequestURI(),
          request.getHeader(DEFAULT_MDC_CLIENT_ID_KEY));
    }

    return tracer.currentSpan().context().traceId();
  }

  @Override
  protected boolean isAsyncDispatch(@NotNull final HttpServletRequest request) {
    return false;
  }

  @Override
  protected boolean shouldNotFilterErrorDispatch() {
    return false;
  }
}
