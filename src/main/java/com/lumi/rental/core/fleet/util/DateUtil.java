package com.lumi.rental.core.fleet.util;

import static lombok.AccessLevel.PRIVATE;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@NoArgsConstructor(access = PRIVATE)
public class DateUtil {
  private static final Logger log = LoggerFactory.getLogger("application");
  private static final ZoneId ZONE = ZoneId.of("UTC");

  public static LocalDateTime getCurrentDateTimeUTC() {
    return LocalDateTime.now(ZONE);
  }

  public static String getCurrentDateTimeInString() {
    LocalDateTime now = LocalDateTime.ofInstant(Instant.now(), ZONE);
    return now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
  }

  public static String getCurrentDateTimeV1AsString() {
    LocalDateTime now = LocalDateTime.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    return now.format(formatter);
  }

  public static LocalDateTime getCurrentDateTime() {
    return LocalDateTime.ofInstant(Instant.now(), ZONE);
  }

  public static Long getCurrentDateTimeInEpoch() {
    return convertLocalDateToEpoch(LocalDateTime.now());
  }

  public static Long convertLocalDateToEpoch(LocalDateTime dateTime) {
    if (dateTime != null) {
      ZonedDateTime zonedDateTime = dateTime.atZone(ZONE);
      return zonedDateTime.toInstant().toEpochMilli();
    }
    return null;
  }

  public static LocalDateTime convertDateAndTime(String date, String time) {
    try {
      if (StringUtils.isNotBlank(date)) {
        LocalDate localDate = localDate(date);
        LocalTime localTime = localTime(time);
        Objects.requireNonNull(localDate);
        Objects.requireNonNull(localTime);
        return LocalDateTime.of(localDate, localTime);
      }
    } catch (Exception e) {
      log.error("Error parsing vehicle document date and time", e);
    }
    return null;
  }

  public static LocalDate localDate(String date) {
    int year = Integer.parseInt(date.substring(0, 4));
    int month = Integer.parseInt(date.substring(4, 6));
    int day = Integer.parseInt(date.substring(6, 8));
    return LocalDate.of(year, month, day);
  }

  public static LocalTime localTime(String time) {
    if (StringUtils.isNotBlank(StringUtils.trim(time))) {
      int hours = Integer.parseInt(time.substring(0, 2));
      int minutes = Integer.parseInt(time.substring(2, 4));
      int seconds = Integer.parseInt(time.substring(4, 6));
      return LocalTime.of(hours, minutes, seconds);
    }
    return LocalTime.of(0, 0, 0);
  }
}
