package com.lumi.rental.core.fleet.controller;

import com.lumi.rental.core.fleet.request.BasePageRequest;
import com.lumi.rental.core.fleet.request.CreateUpdateVehicleModelRequest;
import com.lumi.rental.core.fleet.response.VehicleModelBasicResponseV2;
import com.lumi.rental.core.fleet.response.VehicleModelResponseV2;
import com.lumi.rental.core.fleet.service.VehicleModelServiceV2;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v2/vehicle-model")
public class VehicleModelControllerV2 {

  private final VehicleModelServiceV2 modelService;

  @GetMapping
  public Page<VehicleModelBasicResponseV2> getAllVehicleModel(
      @RequestParam(value = "makeId", required = false) final Integer makeId,
      final BasePageRequest request) {
    return modelService.getAllVehicleModel(
        makeId, PageRequest.of(request.getPageNumber(), request.getPageSize()));
  }

  @GetMapping("/all")
  public Page<VehicleModelBasicResponseV2> getAllVehicleModelAll(final BasePageRequest request) {
    return modelService.getAllVehicleModelAll(
        PageRequest.of(request.getPageNumber(), request.getPageSize()));
  }

  @GetMapping("/{id}")
  public VehicleModelResponseV2 getVehicleModelById(
      @PathVariable(value = "id") final Integer vehicleModelId) {
    return modelService.getVehicleModelResponseById(vehicleModelId);
  }

  //
  //    @PostMapping
  //    public ResponseEntity<VehicleModelResponse> createVehicleModel(@RequestBody
  // CreateUpdateVehicleModelRequest request) {
  //        VehicleModelResponse createdVehicleModel = modelService.createVehicleModel(request);
  //        return new ResponseEntity<>(createdVehicleModel, HttpStatus.CREATED);
  //    }
  //
  @PutMapping("/{id}")
  public ResponseEntity<VehicleModelBasicResponseV2> updateVehicleModel(
      @PathVariable int id, @RequestBody CreateUpdateVehicleModelRequest request) {
    VehicleModelBasicResponseV2 updatedVehicleModel = modelService.updateVehicleModel(id, request);
    return updatedVehicleModel != null
        ? new ResponseEntity<>(updatedVehicleModel, HttpStatus.OK)
        : new ResponseEntity<>(HttpStatus.NOT_FOUND);
  }
}
