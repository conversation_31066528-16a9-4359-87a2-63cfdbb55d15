package com.lumi.rental.core.fleet.util;

import static com.lumi.rental.core.fleet.constants.Constants.IOT_PLATE_NUMBER_PREFIX;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class VehicleUtil {

  /** this key is used for caching iot data for vehicle */
  public static String getPlateNumberKeyForIOT(String plateNumber) {
    return IOT_PLATE_NUMBER_PREFIX + plateNumber.replaceAll("\\s+", "");
  }

  public static boolean isValidPlateNumber(String plateNumber) {
    String pattern = "\\d{4} [a-zA-Z]{3}";
    Pattern regex = Pattern.compile(pattern);
    Matcher matcher = regex.matcher(plateNumber);
    return matcher.matches();
  }

  public static String getPlateNumber(String input) {
    String[] patterns = {
      "\\d{4}\\s[A-Za-z]{3}", // find first occurrence of "1111 aaa" pattern in string
      "\\d{4}[A-Za-z]{3}", // find first occurrence of "1111aaa" pattern in string
      "\\d{4}\\s+[A-Za-z]{3}" // find first occurrence of "1111   aaa" pattern in string
    };
    for (String pattern : patterns) {
      Matcher matcher = Pattern.compile(pattern).matcher(input);
      if (matcher.find()) {
        String plateNumber = matcher.group();
        if (plateNumber.matches("\\d{4}\\s[A-Za-z]{3}")) {
          return plateNumber.toUpperCase();
        } else if (plateNumber.matches("\\d{4}[A-Za-z]{3}")) {
          return plateNumber.substring(0, 4) + " " + plateNumber.substring(4).toUpperCase();
        } else if (plateNumber.matches("\\d{4}\\s+[A-Za-z]{3}")) {
          String[] parts = plateNumber.split("\\s+");
          return parts[0] + " " + parts[1].toUpperCase();
        }
      }
    }
    return input;
  }
}
