package com.lumi.rental.core.fleet.service;

import static org.apache.commons.collections4.ListUtils.partition;
import static org.apache.commons.lang3.StringUtils.trim;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumi.rental.core.fleet.api.carpro.CoreCarProClient;
import com.lumi.rental.core.fleet.api.carpro.resp.ModelCarproResponse;
import com.lumi.rental.core.fleet.api.carpro.resp.VehicleCarproResponse;
import com.lumi.rental.core.fleet.dto.*;
import com.lumi.rental.core.fleet.entity.*;
import com.lumi.rental.core.fleet.enums.*;
import com.lumi.rental.core.fleet.mapper.VehicleSyncMapper;
import com.lumi.rental.core.fleet.repository.*;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.utils.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleSyncService {
  private static final Logger log = LoggerFactory.getLogger("application");
  private final ObjectMapper objectMapper;
  private final CoreCarProClient carProClient;
  private final BranchInfoCache branchInfoCache;
  private final VehicleSyncMapper vehicleSyncMapper;

  private final VehicleSyncRepository vehicleSyncRepository;
  private final VehicleRepository vehicleRepository;
  private final VehicleModelRepository modelRepository;
  private final VehicleMakeRepository makeRepository;
  private final VehicleGroupRepository vehicleGroupRepository;
  private final AIService aiService;
  private final VehicleFinancialInfoRepository financialInfoRepository;
  private final VehicleOperationalDataRepository operationalDataRepository;
  private final VehicleReservationDataRepository reservationDataRepository;
  ExecutorService executorService = Executors.newFixedThreadPool(4);

  public void syncVehicleMetaDataFromSAP(List<Bytes> eventList) {
    List<VehicleSyncEntity> vehicleInfoList =
        eventList.stream()
            .map(
                event -> {
                  try {
                    VehicleDetailSyncEventDTO eventDTO =
                        objectMapper.readValue(event.get(), VehicleDetailSyncEventDTO.class);
                    return createOrUpdateVehicle(eventDTO);
                  } catch (Exception e) {
                    log.error(
                        "Failed to process event vehicle-data sap-sync-vehicle {} Exception ",
                        new String(event.get()),
                        e);
                    return null;
                  }
                })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    vehicleSyncRepository.saveAllAndFlush(vehicleInfoList);
  }

  private VehicleSyncEntity createOrUpdateVehicle(VehicleDetailSyncEventDTO eventDTO) {
    return vehicleSyncRepository
        .findByPlateNo(eventDTO.getPlateNo())
        .map(
            existingVehicle -> {
              existingVehicle.setActive(isActive(eventDTO.getDeactivationOn()));
              existingVehicle.setSapModelName(eventDTO.getModel());
              return existingVehicle;
            })
        .orElseGet(
            () -> {
              log.info(
                  "Vehicle does not exist in database, Creating new.. plate-no:{}",
                  eventDTO.getPlateNo());
              eventDTO.setColor(trim(eventDTO.getColor()));
              VehicleSyncEntity newVehicle = vehicleSyncMapper.buildVehicleSyncInfo(eventDTO);
              newVehicle.setModelId(getVehicleModel(eventDTO.getModel()));
              newVehicle.setActive(isActive(eventDTO.getDeactivationOn()));
              newVehicle.setSapModelName(eventDTO.getModel());
              return newVehicle;
            });
  }

  private Boolean isActive(String deactivationOn) {
    return "2001-01-01".equals(deactivationOn) ? Boolean.TRUE : Boolean.FALSE;
  }

  private Integer getVehicleModel(String sapModelName) {
    List<VehicleModel> model = modelRepository.findByMaterialName(sapModelName);
    if (!model.isEmpty()) {
      return model.get(0).getId();
    } else {
      Set<String> allModels = new HashSet<>(modelRepository.findAllModelString());
      String bestMatch = findBestModelMatch(allModels, sapModelName);
      log.info("Best matching model for '{}' is '{}'", sapModelName, bestMatch);
      if (bestMatch != null) {
        return modelRepository.findByMaterialName(bestMatch).get(0).getId();
      } else {
        log.error("No matching model found for '{}'", sapModelName);
        return null;
      }
    }
  }

  public String findBestModelMatch(Set<String> modelList, String modelName1) {
    String normalizedInput = modelName1.toLowerCase().replaceAll("[^a-z0-9\\s]", "");

    return modelList.stream()
        .collect(
            Collectors.toMap(
                model -> model,
                model -> {
                  String normalizedModel = model.toLowerCase().replaceAll("[^a-z0-9\\s]", "");
                  // Score based on:
                  // 1. Exact match (highest priority)
                  if (normalizedModel.equals(normalizedInput)) {
                    return 1000;
                  }
                  // 2. Contains all words in input (e.g., "Hilux DC 4x4" → "Hilux DC 4x4 Diesel")
                  if (containsAllWords(normalizedModel, normalizedInput)) {
                    return 500 + countMatchingWords(normalizedModel, normalizedInput);
                  }
                  // 3. Partial word matches (e.g., "RAV-4 LE" → "RAV4 LE 4X2")
                  return countMatchingWords(normalizedModel, normalizedInput);
                }))
        .entrySet()
        .stream()
        .max((e1, e2) -> Integer.compare(e1.getValue(), e2.getValue()))
        .map(e -> e.getKey())
        .orElse(null);
  }

  private int countMatchingWords(String model, String input) {
    String[] inputWords = input.split("\\s+");
    int count = 0;
    for (String word : inputWords) {
      if (model.contains(word)) {
        count++;
      }
    }
    return count;
  }

  private boolean containsAllWords(String model, String input) {
    String[] inputWords = input.split("\\s+");
    for (String word : inputWords) {
      if (!model.contains(word)) {
        return false;
      }
    }
    return true;
  }

  public void syncVehicleFinancialInfoFromSAP(List<Bytes> eventList) {
    List<VehicleFinancialInfo> vehicleFinancialInfoList =
        eventList.stream()
            .map(
                event -> {
                  try {
                    VehicleDetailSyncEventDTO eventDTO =
                        objectMapper.readValue(event.get(), VehicleDetailSyncEventDTO.class);
                    return createOrUpdateVehicleFinancialInfo(eventDTO);
                  } catch (Exception e) {
                    log.error(
                        "Failed to process event vehicle-financial-data sap-sync-vehicle {} Exception ",
                        new String(event.get()),
                        e);
                    return null;
                  }
                })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    financialInfoRepository.saveAllAndFlush(vehicleFinancialInfoList);
  }

  private VehicleFinancialInfo createOrUpdateVehicleFinancialInfo(
      VehicleDetailSyncEventDTO eventDTO) {
    logWarningsForFinancialInfo(eventDTO);
    return financialInfoRepository
        .findByPlateNo(eventDTO.getPlateNo())
        .map(
            existingVehicle ->
                vehicleSyncMapper.updateVehicleFinancialInfo(existingVehicle, eventDTO))
        .orElseGet(
            () -> {
              log.info(
                  "Vehicle Financial Info does not exist in database, Creating new.. plate-no:{}",
                  eventDTO.getPlateNo());
              return vehicleSyncMapper.buildVehicleFinancialInfo(eventDTO);
            });
  }

  private void logWarningsForFinancialInfo(VehicleDetailSyncEventDTO eventDTO) {
    Double adv = eventDTO.getAdv();
    Double nbv = eventDTO.getNbv();

    // Check ADV is non-null and >= 0 using Double.compare
    if (adv != null && Double.compare(adv, 0.0) >= 0 && isActive(eventDTO.getDeactivationOn())) {
      log.warn(
          "Vehicle ADV is zero or positive for plate-no:{}, adv:{}", eventDTO.getPlateNo(), adv);
    }

    // Check NBV is non-null and <= 0 using Double.compare
    if (nbv != null && Double.compare(nbv, 0.0) <= 0 && isActive(eventDTO.getDeactivationOn())) {
      log.warn(
          "Vehicle NBV is zero or negative for plate-no:{}, nbv:{}", eventDTO.getPlateNo(), nbv);
    }
  }

  @Async // tested
  public void syncVehicleChassisNoAndPlateNoArFromCARPRO() {
    try {
      // Remove duplicates by using a Set, then convert to a List for partitioning
      List<String> orphanVehicles =
          new ArrayList<>(
              new HashSet<>(vehicleRepository.findVehiclesWithoutChassisNoOrPolicyNo()));
      log.info("Found {} vehicles without chassis-no|policy-no", orphanVehicles.size());

      // Partition the list into batches of 100
      List<List<String>> partitions = partition(orphanVehicles, 100);
      log.info("Breaking into {} batches of 100", partitions.size());
      // Submit each partition as a separate task to the executorService
      partitions.forEach(
          vehicleList -> executorService.submit(() -> syncVehicleMetadataFromCarpro(vehicleList)));
    } catch (Exception e) {
      log.error("Error while processing batch of vehicles", e);
    }
  }

  @Async // tested
  public void syncModelMetadata() {
    List<ModelCarproResponse> models = carProClient.getAllModels();
    log.info("Found {} models from car-pro", models.size());
    for (ModelCarproResponse model : models) {
      VehicleModel vehicleModel =
          modelRepository.findBySapMaterialId(model.getSapMaterialId().trim());
      if (vehicleModel == null) {
        log.info("Model not found in lumi-database, creating new model: {}", model.getModelName());
        vehicleModel = new VehicleModel();
        vehicleModel.setMake(makeRepository.findByCarproMakeId(model.getMakeId()).orElse(null));
        vehicleModel.setNameEn(StringUtils.trim(model.getModelName()));
        String modelNameAr = aiService.translateModelNameToArabic(vehicleModel.getNameEn());
        if (StringUtils.isNotBlank(modelNameAr)) {
          vehicleModel.setNameAr(modelNameAr);
        }
        vehicleModel.setSapMaterialId(StringUtils.trim(model.getSapMaterialId()));
        vehicleModel.setModelVersion(StringUtils.trim(model.getModelVersion()));
        vehicleModel.setModelSeries("1");
        vehicleModel.setVehicleGroup(
            vehicleGroupRepository.findByCode(model.getVehicleGroup()).orElse(null));
        vehicleModel.setSpecification(buildSpecification(model));
        vehicleModel.setEnabled(false);
        modelRepository.saveAndFlush(vehicleModel);
      }
    }
  }

  private Specification buildSpecification(ModelCarproResponse model) {
    Specification specification = new Specification();
    specification.setFuelType(model.getFuelType());
    return specification;
  }

  private void syncVehicleMetadataFromCarpro(List<String> assetIds) {
    try {
      // Create a map from trimmed plate numbers to their corresponding CARPRO
      // response
      Map<String, VehicleCarproResponse> carproResponseMap =
          carProClient.getVehicleInfo(assetIds).stream()
              .collect(
                  Collectors.toMap(
                      vehicle -> getPlateNumber(vehicle.getPlateNo()), Function.identity()));

      // Retrieve vehicles by plate numbers from the repository
      List<Vehicle> vehicleList = vehicleRepository.findAllByAssetIdIn(assetIds);
      vehicleList.forEach(
          vehicle -> {
            // Use the trimmed plate number for lookup
            String trimmedPlateNo = vehicle.getPlateNo() != null ? vehicle.getPlateNo().trim() : "";
            VehicleCarproResponse response = carproResponseMap.get(trimmedPlateNo);
            if (response != null) {
              if (StringUtils.isEmpty(vehicle.getChassisNo())
                  && StringUtils.isNotEmpty(response.getChassisNo())) {
                vehicle.setChassisNo(response.getChassisNo().trim());
                Integer modelYear = getVehicleModelYear(vehicle.getChassisNo().trim());
                if (Objects.nonNull(modelYear)) {
                  vehicle.setYear(modelYear);
                }
              }
              if (StringUtils.isNotBlank(response.getInsurancePolicyNo())) {
                vehicle.setPolicyNo(response.getInsurancePolicyNo().trim());
              }
            } else {
              log.error(
                  "Vehicle response not found in CARPRO for plate-no: {}", vehicle.getPlateNo());
            }
          });
      vehicleRepository.saveAllAndFlush(vehicleList);
    } catch (Exception exception) {
      log.error("Error while processing batch of vehicles plate-nos:", exception);
    }
  }

  private String getPlateNumber(String plateNo) {
    String trimmedPlateNo = plateNo != null ? plateNo.trim() : "";
    if (StringUtils.isNotEmpty(trimmedPlateNo)) {
      String[] parts = trimmedPlateNo.split("\\s+");
      if (parts[0].length() == 3) {
        return "0" + parts[0] + " " + parts[1].toUpperCase();
      }
      return trimmedPlateNo;
    } else return trimmedPlateNo;
  }

  @Async // tested
  public void syncVehicleModelYear() {
    try {
      List<VehicleSyncEntity> vehiclesWithOutYear =
          vehicleSyncRepository.findVehiclesWithChassisNoButNoModelYear();
      log.info(
          "Found {} vehicles with chassis number but missing year that need updates",
          vehiclesWithOutYear.size());
      for (VehicleSyncEntity vehicle : vehiclesWithOutYear) {
        try {
          Integer modelYear = getVehicleModelYear(vehicle.getChassisNo());
          if (Objects.nonNull(modelYear)) {
            vehicle.setYear(modelYear);
          }
        } catch (Exception e) {
          log.error(
              "Error processing vehicle with plate number {}: {}",
              vehicle.getPlateNo(),
              e.getMessage());
        }
      }
      if (!vehiclesWithOutYear.isEmpty()) {
        vehicleSyncRepository.saveAllAndFlush(vehiclesWithOutYear);
        log.info("Successfully updated model year for {} vehicles", vehiclesWithOutYear.size());
      } else {
        log.info("No vehicles needed model year updates");
      }
    } catch (Exception e) {
      log.error("Error while syncing vehicle model years", e);
    }
  }

  private Integer getVehicleModelYear(String chassisNo) {
    // Typically, model year information is in the 10th position of VIN/chassis
    // numbers in most formats
    // This is a simplified implementation - you may need to adjust based on your
    // specific format
    if (chassisNo.length() >= 10) {
      char modelYearChar = chassisNo.charAt(9);
      return extractModelYearFromChar(modelYearChar);
    } else {
      log.warn("Chassis number {} is too short for year extraction", chassisNo);
      return null;
    }
  }

  private Integer extractModelYearFromChar(char modelYearChar) {
    // Standard VIN model year mapping (used in most regions)
    // This needs to be adjusted based on your specific requirements
    Map<Character, Integer> yearMap = new HashMap<>();
    yearMap.put('B', 2011);
    yearMap.put('C', 2012);
    yearMap.put('D', 2013);
    yearMap.put('E', 2014);
    yearMap.put('F', 2015);
    yearMap.put('G', 2016);
    yearMap.put('H', 2017);
    yearMap.put('J', 2018);
    yearMap.put('K', 2019);
    // Years 2020-2029
    yearMap.put('L', 2020);
    yearMap.put('M', 2021);
    yearMap.put('N', 2022);
    yearMap.put('P', 2023);
    yearMap.put('R', 2024);
    yearMap.put('S', 2025);
    yearMap.put('T', 2026);
    yearMap.put('V', 2027);
    yearMap.put('W', 2028);
    yearMap.put('X', 2029);
    yearMap.put('Y', 2030);
    yearMap.put('1', 2031);
    yearMap.put('2', 2032);
    yearMap.put('3', 2033);
    yearMap.put('4', 2034);
    yearMap.put('5', 2035);
    yearMap.put('6', 2036);
    yearMap.put('7', 2037);
    yearMap.put('8', 2038);
    yearMap.put('9', 2039);
    yearMap.put('A', 2040);

    return yearMap.getOrDefault(modelYearChar, null);
  }

  public void syncVehicleOperationalData(VehicleOperationalDataSyncEventDTO eventDTO) {
    String plateNo = getPlateNumber(eventDTO.getPlateNo());
    Optional<Vehicle> vehicleOptional = vehicleRepository.findByPlateNo(plateNo);
    if (vehicleOptional.isEmpty()) {
      log.warn(
          "skipping vehicle-operational-data, vehicle with plate-no:{} not found in lumi-database",
          plateNo);
      return;
    }
    if (vehicleOptional.get().getModel() == null) {
      log.warn(
          "skipping vehicle-operational-data, vehicle with plate-no:{} has no model in lumi-database",
          plateNo);
      return;
    }
    var serviceType = ServiceType.fromCarproCode(eventDTO.getCarproServiceTypeCode());
    if (Objects.isNull(serviceType))
      log.warn(
          "vehicle-operational-data, no mapping found for service-type:{}",
          eventDTO.getCarproServiceTypeCode());
    var subServiceType = SubServiceType.fromId(eventDTO.getCarproSubServiceTypeId());
    if (Objects.isNull(subServiceType))
      log.warn(
          "vehicle-operational-data, no mapping found for sub-service-type:{}",
          eventDTO.getCarproSubServiceTypeId());
    var status = StatusType.fromId(eventDTO.getCarproStatusId());
    if (Objects.isNull(status))
      log.warn(
          "vehicle-operational-data, no mapping found for vehicle-status:{}",
          eventDTO.getCarproStatusId());
    var vehicle = vehicleOptional.get();
    // Do not overwrite in case of yaqeen managed vehicle
    if (!isYaqeenManagedBranch(eventDTO.getCurrentLocationId())) {
      VehicleOperationalData vehicleOperationalData =
          operationalDataRepository
              .findByPlateNo(plateNo)
              .map(
                  existingOpsData -> {
                    VehicleOperationalData updatedOpsData =
                        vehicleSyncMapper.updateVehicleOperationalData(
                            existingOpsData,
                            eventDTO,
                            vehicle,
                            serviceType,
                            subServiceType,
                            status);
                    updatedOpsData.setCurrentLocationId(
                        branchInfoCache
                            .getBranchCarproCode(eventDTO.getCurrentLocationId())
                            .getLumiBranchId());
                    updatedOpsData.setOwnerBranchId(
                        branchInfoCache
                            .getBranchCarproCode(eventDTO.getCurrentLocationId())
                            .getLumiBranchId());
                    updateVehicleStatusInfo(updatedOpsData, status, eventDTO);
                    return updatedOpsData;
                  })
              .orElseGet(
                  () -> {
                    VehicleOperationalData opsData =
                        vehicleSyncMapper.buildVehicleOperationalData(
                            eventDTO, vehicle, serviceType, subServiceType, status);
                    opsData.setCurrentLocationId(
                        branchInfoCache
                            .getBranchCarproCode(eventDTO.getCurrentLocationId())
                            .getLumiBranchId());
                    opsData.setOwnerBranchId(
                        branchInfoCache
                            .getBranchCarproCode(eventDTO.getCurrentLocationId())
                            .getLumiBranchId());
                    updateVehicleStatusInfo(opsData, status, eventDTO);
                    return opsData;
                  });

      operationalDataRepository.saveAndFlush(vehicleOperationalData);
      log.info("vehicle-operational-data for plate-no:{} saved successfully to database", plateNo);
    } else {
      log.info(
          "skipping vehicle-operational-data for plate-no:{} as it belongs to yaqeen branch:{}",
          plateNo,
          eventDTO.getCurrentLocationId());
    }
  }

  private boolean isYaqeenManagedBranch(String carproBranchId) {
    LocationDTO locationDTO = branchInfoCache.getBranchCarproCode(carproBranchId);
    if (locationDTO.getYaqeenMigrated() != null) {
      log.info(
          "carpro-branch-id:{} found in branch-service, yaqeen-migrated:{}",
          carproBranchId,
          locationDTO.getYaqeenMigrated());
      return locationDTO.getYaqeenMigrated();
    }
    log.warn(
        "carpro-branch-id:{} not found in branch-service, validating in hardcoded yaqeen-managed-branch-list",
        carproBranchId);
    return getYaqeenManagedBranch().contains(Integer.parseInt(carproBranchId));
  }

  private List<Integer> getYaqeenManagedBranch() {
    // Based on environment, return list of Yaqeen managed branch IDs
    String activeProfile = System.getenv("ACTIVE_PROFILE");
    if (activeProfile == null) {
      activeProfile = "local";
    }
    switch (activeProfile.toLowerCase()) {
      case "prod":
        return List.of(75, 97); // Production Yaqeen branch IDs
      case "preprod":
        return List.of(75, 97); // Preprod Yaqeen branch IDs
      case "staging":
        return List.of(1, 4, 9, 35, 78, 101); // Staging/Preprod Yaqeen branch IDs
      case "dev":
        return List.of(1, 4, 9, 35); // Dev Yaqeen branch IDs
      default:
        return List.of(); // Local environment - no Yaqeen branches
    }
  }

  private void updateVehicleStatusInfo(
      VehicleOperationalData existingOpsData,
      StatusType carproStatus,
      VehicleOperationalDataSyncEventDTO eventDTO) {
    VehicleStatusInfo currentStatusInfo = existingOpsData.getVehicleStatus();
    if (currentStatusInfo == null
        || currentStatusInfo.getStatusId() == null
        || !currentStatusInfo.getStatusId().equals(carproStatus.getId())) {
      VehicleStatusInfo newStatusInfo =
          convertCarProStatusToVehicleStatusInfo(carproStatus, eventDTO);
      existingOpsData.setVehicleStatus(newStatusInfo);
    }
  }

  private VehicleStatusInfo convertCarProStatusToVehicleStatusInfo(
      StatusType carproStatus, VehicleOperationalDataSyncEventDTO eventDTO) {
    if (carproStatus.equals(StatusType.READY)) {
      return new VehicleStatusInfo(StatusType.READY.getId());
    } else if (carproStatus.equals(StatusType.RENTED)) {
      return new VehicleStatusInfo(StatusType.RENTED.getId());
    } else if (carproStatus.equals(StatusType.OUT_OF_SERVICE)) {
      if (eventDTO.getOosReason().equals(CarProOutOfServiceReason.TRANSFER.getId())) {
        return new VehicleStatusInfo(
            StatusType.NRM_OPENED.getId(), NonRevenueMovementReason.TRANSFER.getId());
      } else {
        return new VehicleStatusInfo(StatusType.OUT_OF_SERVICE.getId());
      }
    } else if (carproStatus.equals(StatusType.PRE_CHECKED_IN)) {
      return new VehicleStatusInfo(StatusType.DISPUTED.getId());
    } else if (carproStatus.equals(StatusType.FOREIGN_CAR_RETURNED)) {
      return new VehicleStatusInfo(StatusType.FOREIGN_CAR_RETURNED.getId());
    } else if (carproStatus.equals(StatusType.STOLEN)) {
      return new VehicleStatusInfo(StatusType.STOLEN.getId());
    } else if (carproStatus.equals(StatusType.TOTAL_LOSS)) {
      return new VehicleStatusInfo(StatusType.TOTAL_LOSS.getId());
    } else if (carproStatus.equals(StatusType.IN_SALE_CYCLE)) {
      if (eventDTO.getSaleCycle().equals(1)) {
        return new VehicleStatusInfo(
            StatusType.IN_SALE_CYCLE.getId(), SaleCycleStatus.SALE_PREPARATION.getId());
      } else if (eventDTO.getSaleCycle().equals(3)) {
        return new VehicleStatusInfo(
            StatusType.IN_SALE_CYCLE.getId(), SaleCycleStatus.SALE_IN_PROGRESS.getId());
      }
    } else if (carproStatus.equals(StatusType.SOLD)) {
      return new VehicleStatusInfo(StatusType.SOLD.getId());
    }
    return null;
  }

  @Async
  public void updateAllVehicleModelSpecifications() {
    log.info("Starting scheduled job to update all vehicle model specifications");

    List<VehicleModel> allModels = modelRepository.findAllV2();
    int total = allModels.size();
    int success = 0;
    int failed = 0;

    for (VehicleModel model : allModels) {
      try {
        if (model.getSpecification() != null) {
          log.info(
              "Specifications already exist for model: {} {}", model.getId(), model.getNameEn());
          continue;
        }
        log.info(
            "Updating specifications for model: {} {} {}",
            model.getId(),
            model.getNameEn(),
            model.getModelVersion());
        Specification spec =
            aiService.fetchAndMapGccSpecifications(
                model.getMake().getNameEn() + " " + model.getMaterialName());

        if (spec != null) {
          success++;
          model.setSpecification(spec);
          modelRepository.saveAndFlush(model);
          log.info("Successfully updated specifications for model ID: {}", model.getId());
        } else {
          failed++;
          log.warn("Failed to update specifications for model ID: {}", model.getId());
        }

        // Add a small delay to avoid overwhelming the API
        Thread.sleep(7000);

      } catch (Exception e) {
        failed++;
        log.error("Error updating specifications for model ID: {}", model.getId(), e);
      }
    }

    log.info(
        "Completed specifications update job. Total: {}, Success: {}, Failed: {}",
        total,
        success,
        failed);
  }

  @Async
  public void populateFleetCountForVehicleModel() {
    List<VehicleModel> allModels = modelRepository.findAllV2();
    for (VehicleModel model : allModels) {
      int fleetCount = vehicleSyncRepository.countByModelId(model.getId());
      model.setFleetCount(fleetCount);
      if (fleetCount == 0) {
        model.setEnabled(false);
      }
      log.info("Updated fleet count for model ID: {} to {}", model.getId(), fleetCount);
      modelRepository.saveAndFlush(model);
    }
  }
}
