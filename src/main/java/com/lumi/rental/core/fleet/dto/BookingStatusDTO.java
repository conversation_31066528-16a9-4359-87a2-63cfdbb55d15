package com.lumi.rental.core.fleet.dto;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode
@NoArgsConstructor
public class BookingStatusDTO implements Serializable {
  private Boolean booked;
  private String bookingId;

  public BookingStatusDTO(Boolean booked, String bookingId) {
    this.booked = booked;
    this.bookingId = bookingId;
  }
}
