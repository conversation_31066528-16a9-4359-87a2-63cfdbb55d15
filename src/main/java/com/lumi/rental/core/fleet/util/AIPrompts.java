package com.lumi.rental.core.fleet.util;

/** Contains prompt templates for AI operations. */
public class AIPrompts {

  /** Prompt for Arabic translation. */
  public static String translateModelNameToArabicPrompt(String text) {
    return String.format(
        "translate the car model name into Arabic: '%s'. Return only the Arabic translation with no additional commentary.",
        text);
  }

  public static String translateMakeNameToArabicPrompt(String text) {
    return String.format(
        "translate the car make name into Arabic: '%s'. Return only the Arabic translation with no additional commentary.",
        text);
  }

  /** Prompt for plate number translation. */
  public static String getPlateTranslationPrompt(String plateNumber) {
    return String.format(
        """
        Convert the Latin letters in this license plate '%1$s' to their equivalent Arabic letters.
        The format should be Arabic letters first (right-to-left) followed by the numbers.
        For example, '4963 ZHD' should become 'د ه م 4963'.
        Only return the converted text with no additional commentary or explanation.
        """,
        plateNumber);
  }

  /** Prompt for splitting vehicle information. */
  public static String getVehicleInfoSplitPrompt(String input) {
    return String.format(
        "Split the string '%s' into a reasonable format (e.g., model, trim, engine, drivetrain, fuel type, transmission, give response in key value pair and remove extra commentary",
        input);
  }

  /** Prompt for finding vehicle specifications. */
  public static String buildPromptForFindingSpecs(String materialName) {
    return String.format(
        """
        Give me the full GCC specifications in JSON format for the %s. i want given below format, No commentary required
        public class Specification {
            private String version;
            private Integer seatingCapacity;
            private Integer doors;
            private Integer bootSpace;
            private Integer luggageCountBig;
            private Integer luggageCountMedium;
            private Integer luggageCountSmall;
            private String transmission;
            private String transmissionType;
            private Integer engineSize;
            private Integer horsepower;
            private DimensionDTO dimensions;
            private String fuelType;
            private Integer fuelCapacity;
            private SuspensionDTO suspension;
            private BrakeDTO brakes;
            private TyreDetailDTO tyres;
            private Map<String, Boolean> interiorFeatures;
            private Map<String, Boolean> exteriorFeatures;
            private Map<String, Boolean> safetyFeatures;

            @Data
            private class DimensionDTO {
                private Integer length;
                private Integer width;
                private Integer height;
                private Integer wheelbase;
            }

            @Data
            private class SuspensionDTO {
                private String front;
                private String rear;
            }

            private class BrakeDTO {
                private String front;
                private String rear;
            }

            @Data
            private class TyreDetailDTO {
                private String front;
                private String rear;
            }
        }
        """,
        materialName);
  }
}
