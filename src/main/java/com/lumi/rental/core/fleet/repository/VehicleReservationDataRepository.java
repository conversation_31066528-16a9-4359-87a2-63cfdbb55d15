package com.lumi.rental.core.fleet.repository;

import com.lumi.rental.core.fleet.entity.VehicleReservationData;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface VehicleReservationDataRepository
    extends JpaRepository<VehicleReservationData, Integer> {

  Optional<VehicleReservationData> findByPlateNo(String name);

  @Query(
      value =
          """
            SELECT DISTINCT plate_no
            FROM   reservation r
            WHERE  plate_no IN (SELECT r1.plate_no
                                FROM   reservation r1
                                       INNER JOIN (SELECT plate_no,
                                                          Max(checkin_date) AS
                                                          latest_to_date
                                                   FROM   reservation
                                                   WHERE  checkin_date < :fromDate
                                                   GROUP  BY plate_no) r2
                                               ON r1.plate_no = r2.plate_no
                                                  AND r1.checkin_date = r2.latest_to_date
                                WHERE  r1.checkin_branch = :checkinBranch)
                   AND ( :toDate < r.checkout_date
                          OR r.checkin_date < :fromDate )
          """,
      nativeQuery = true)
  Set<String> getAvailablePlateNosAtBranch(
      @Param("fromDate") LocalDateTime fromDate,
      @Param("toDate") LocalDateTime toDate,
      @Param("checkinBranch") Integer checkinBranch);

  //  // get Map<plateNo, max reservation date>
  @Query(
      value =
          """
              SELECT plate_no, UNIX_TIMESTAMP(MAX(checkin_date)) AS max_checkin_date
              FROM reservation
              WHERE plate_no IN :plateNos
              GROUP BY plate_no;
          """,
      nativeQuery = true)
  List<Map<String, Object>> findLatestReservationDate(@Param("plateNos") Set<String> plateNos);

  default Map<String, Long> findLatestReservationDateByPlateNos(Set<String> plateNos) {
    if (plateNos == null || plateNos.isEmpty()) {
      return Collections.emptyMap();
    }
    return findLatestReservationDate(plateNos).stream()
        .collect(
            Collectors.toMap(
                map -> (String) map.get("plate_no"),
                map -> ((Number) map.get("max_checkin_date")).longValue(),
                (v1, v2) -> v1,
                LinkedHashMap::new));
  }

  Optional<VehicleReservationData> findByPlateNoAndReferenceId(String plateNo, String referenceId);
}
