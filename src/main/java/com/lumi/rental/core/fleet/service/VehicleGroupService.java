package com.lumi.rental.core.fleet.service;

import static com.seera.lumi.core.fleet.constants.CacheConstants.*;

import com.lumi.rental.core.fleet.dto.MultilingualDTO;
import com.lumi.rental.core.fleet.entity.ModelFeature;
import com.lumi.rental.core.fleet.entity.VehicleGroup;
import com.lumi.rental.core.fleet.entity.VehicleModel;
import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.mapper.VehicleGroupMapper;
import com.lumi.rental.core.fleet.mapper.VehicleModelMapper;
import com.lumi.rental.core.fleet.repository.ModelFeatureRepository;
import com.lumi.rental.core.fleet.repository.VehicleGroupRepository;
import com.lumi.rental.core.fleet.response.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleGroupService {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final VehicleGroupMapper vehicleGroupMapper;
  private final VehicleModelMapper vehicleModelMapper;
  private final VehicleGroupRepository repository;
  private final ModelFeatureRepository modelFeatureRepository;
  private final VehicleModelServiceV2 modelService;

  @Cacheable(
      cacheNames = GROUP_RESPONSE_ALL_DETAIL,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public Page<VehicleGroupResponse> vehicleGroupDetailResponse(Pageable pageable, Boolean enabled) {
    List<VehicleModel> vehicleModelList = modelService.getAllVehicleModel();
    List<ModelFeature> modelFeatureList = modelFeatureRepository.findAll();
    log.info("In VehicleGroupService, fetching model and model-features from DB");
    Map<Integer, List<ModelFeature>> modelFeatureMap =
        groupFeatureByModel(modelFeatureList); // <model_id, list<model_feature>>

    log.info("In VehicleGroupService, preparing models against group-code");
    Map<String, List<VehicleModelBasicResponse>> groupModelMap =
        groupModelsByGroup(vehicleModelList, modelFeatureMap);
    Map<Integer, VehicleModelBasicResponse> modelMap =
        getModelMap(vehicleModelList, modelFeatureMap);

    Page<VehicleGroup> vehicleGroups = repository.findAllByEnabled(enabled, pageable);
    List<VehicleGroupResponse> groupList =
        vehicleGroups.stream()
            .map(
                vehicleGroup -> {
                  return vehicleGroupMapper.buildVehicleGroupResponse(
                      vehicleGroup,
                      modelMap.get(vehicleGroup.getFaceModelId()),
                      groupModelMap.get(vehicleGroup.getCode()));
                })
            .collect(Collectors.toList());
    log.info("In VehicleGroupService, request completed total groups found:{}", groupList.size());
    return new PageImpl<>(groupList, pageable, vehicleGroups.getTotalElements());
  }

  @Cacheable(
      cacheNames = GROUP_RESPONSE_ALL,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public Page<VehicleGroupBaseResponse> vehicleGroupBaseResponse(
      Pageable pageable, Boolean enabled) {
    return repository
        .findAllByEnabled(enabled, pageable)
        .map(vehicleGroupMapper::buildVehicleGroupBaseResponse);
  }

  @Cacheable(
      cacheNames = GROUP_RESPONSE_BY_CODE,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public VehicleGroupBaseResponse vehicleGroupBaseResponseByCode(String code) {
    VehicleGroup vehicleGroup = findByCode(code);
    VehicleGroupBaseResponse groupResponse =
        vehicleGroupMapper.buildVehicleGroupBaseResponse(
            vehicleGroup,
            modelService.getVehicleModelBasicResponseById(vehicleGroup.getFaceModelId()));
    groupResponse.setDisplayName(getDisplayName(groupResponse));
    return groupResponse;
  }

  public VehicleGroup findByCode(String code) {
    return repository
        .findByCode(code)
        .orElseThrow(() -> new BusinessException(BaseError.NOT_FOUND, "VehicleGroup"));
  }

  @Cacheable(
      cacheNames = GROUP_RESPONSE_BY_ID,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public VehicleGroupBaseResponse vehicleGroupBaseResponseById(Integer id) {
    VehicleGroup vehicleGroup =
        repository
            .findById(id)
            .orElseThrow(() -> new BusinessException(BaseError.NOT_FOUND, "VehicleGroup"));
    VehicleGroupBaseResponse groupResponse =
        vehicleGroupMapper.buildVehicleGroupBaseResponse(
            vehicleGroup,
            modelService.getVehicleModelBasicResponseById(vehicleGroup.getFaceModelId()));
    groupResponse.setDisplayName(getDisplayName(groupResponse));
    return groupResponse;
  }

  @Cacheable(
      cacheNames = GROUP_RESPONSE_BY_IDS,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public Page<VehicleGroupBaseResponse> vehicleGroupBaseResponseByIds(
      List<Integer> ids, Pageable pageable, Boolean enabled) {
    return repository
        .findByIdInAndEnabled(ids, enabled, pageable)
        .map(vehicleGroupMapper::buildVehicleGroupBaseResponse);
  }

  //    @Cacheable(cacheNames = GROUP_MAP_BY_CODE, keyGenerator = "customKeyGenerator", cacheManager
  // = "oneDayCacheManager")
  public Map<String, VehicleGroup> getVehicleGroupMap() {
    return repository.findAll().stream()
        .collect(Collectors.toMap(VehicleGroup::getCode, Function.identity()));
  }

  private Map<String, List<VehicleModelBasicResponse>> groupModelsByGroup(
      List<VehicleModel> vehicleModelList, Map<Integer, List<ModelFeature>> modelFeatureMap) {
    Map<String, List<VehicleModelBasicResponse>> result = new HashMap<>();
    for (VehicleModel model : vehicleModelList) {
      if (ObjectUtils.isNotEmpty(model.getVehicleGroup())) {
        result.putIfAbsent(model.getVehicleGroup().getCode(), new ArrayList<>());
        result
            .get(model.getVehicleGroup().getCode())
            .add(
                vehicleModelMapper.buildVehicleModelBasicResponse(
                    model, modelFeatureMap.get(model.getId())));
      }
    }
    return result;
  }

  private Map<Integer, VehicleModelBasicResponse> getModelMap(
      List<VehicleModel> vehicleModelList, Map<Integer, List<ModelFeature>> modelFeatureMap) {
    Map<Integer, VehicleModelBasicResponse> result = new HashMap<>();
    for (VehicleModel model : vehicleModelList) {
      result.put(
          model.getId(),
          vehicleModelMapper.buildVehicleModelBasicResponse(
              model, modelFeatureMap.get(model.getId())));
    }
    return result;
  }

  private VehicleModelBasicResponse getModelBasicResponse(
      VehicleModel model, Map<Integer, List<ModelFeature>> modelFeatureMap) {
    return vehicleModelMapper.buildVehicleModelBasicResponse(
        model, modelFeatureMap.get(model.getId()));
  }

  private Map<Integer, List<ModelFeature>> groupFeatureByModel(
      List<ModelFeature> modelFeatureList) {
    return modelFeatureList.stream().collect(Collectors.groupingBy(ModelFeature::getModelId));
  }

  private String getDisplayName(VehicleGroupBaseResponse groupResponse) {
    if (groupResponse == null || groupResponse.getFaceModelResponse() == null) {
      return "";
    }
    MultilingualDTO modelName = groupResponse.getFaceModelResponse().getName();
    MultilingualDTO makeName = groupResponse.getFaceModelResponse().getMake().getName();
    return (makeName != null && modelName != null)
        ? String.format("%s_%s_%s", groupResponse.getCode(), makeName.getEn(), modelName.getEn())
        : StringUtils.EMPTY;
  }

  public VehicleGroupResponse getVehicleGroupDetailById(Integer vehicleGroupId) {
    VehicleGroup vehicleGroup =
        repository
            .findById(vehicleGroupId)
            .orElseThrow(() -> new BusinessException(BaseError.NOT_FOUND, "VehicleGroup"));

    VehicleModel model = modelService.getVehicleModelById(vehicleGroup.getFaceModelId());
    List<ModelFeature> modelFeatureList =
        modelFeatureRepository.findAllByModelId(vehicleGroup.getFaceModelId());
    Map<Integer, List<ModelFeature>> modelFeatureMap = groupFeatureByModel(modelFeatureList);

    VehicleModelBasicResponse vehicleModelBasicResponse =
        getModelBasicResponse(model, modelFeatureMap);

    return vehicleGroupMapper.buildVehicleGroupResponse(vehicleGroup, vehicleModelBasicResponse);
  }
}
