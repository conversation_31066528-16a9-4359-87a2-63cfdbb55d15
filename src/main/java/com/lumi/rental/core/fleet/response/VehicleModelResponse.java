package com.lumi.rental.core.fleet.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VehicleModelResponse extends VehicleModelBasicResponse {

  private String modelVersion;
  private String modelSeries;
  private String vehicleGroup;
}
