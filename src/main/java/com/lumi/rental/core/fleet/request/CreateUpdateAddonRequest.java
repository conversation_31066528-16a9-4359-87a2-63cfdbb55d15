package com.lumi.rental.core.fleet.request;

import com.lumi.rental.core.fleet.dto.MultilingualDTO;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CreateUpdateAddonRequest {

  @NotNull @NotEmpty private String code;
  private MultilingualDTO name;
  private MultilingualDTO description;
  private String imageUrl;
  private Boolean enabled;
}
