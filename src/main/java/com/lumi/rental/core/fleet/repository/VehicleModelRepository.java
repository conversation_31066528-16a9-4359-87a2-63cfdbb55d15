package com.lumi.rental.core.fleet.repository;

import com.lumi.rental.core.fleet.entity.VehicleModel;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface VehicleModelRepository extends JpaRepository<VehicleModel, Integer> {

  @EntityGraph("NEG_VEHICLE_MODEL_MAKE")
  @Query(
      "SELECT vm FROM VehicleModel vm where vm.id = vm.faceModelId and vehicleGroup.code = :groupCode")
  List<VehicleModel> findByVehicleGroup(String groupCode);

  @EntityGraph("NEG_VEHICLE_MODEL_MAKE")
  @Query("SELECT vm FROM VehicleModel vm where vm.id = vm.faceModelId")
  List<VehicleModel> findAll();

  @EntityGraph("NEG_VEHICLE_MODEL_MAKE")
  @Query("SELECT vm.materialName FROM VehicleModel vm")
  List<String> findAllModelString();

  @EntityGraph("NEG_VEHICLE_MODEL_MAKE")
  @Query("SELECT vm FROM VehicleModel vm where vm.id = vm.faceModelId")
  Page<VehicleModel> findAll(Pageable pageable);

  @EntityGraph("NEG_VEHICLE_MODEL_MAKE")
  @Query("SELECT vm FROM VehicleModel vm where vm.id = vm.faceModelId and vm.make.id = :makeId")
  Page<VehicleModel> findAllModelsOfMake(Integer makeId, Pageable pageable);

  @EntityGraph("NEG_VEHICLE_MODEL_MAKE")
  @Query("SELECT vm FROM VehicleModel vm")
  Page<VehicleModel> findAllV2(Pageable pageable);

  @EntityGraph("NEG_VEHICLE_MODEL_MAKE")
  @Query("SELECT vm FROM VehicleModel vm")
  List<VehicleModel> findAllV2();

  @EntityGraph("NEG_VEHICLE_MODEL_MAKE")
  @Query("SELECT vm FROM VehicleModel vm where vm.nameAr is null and vm.nameEn is not null")
  List<VehicleModel> findAllModelsWithEnglishNameAndWithoutArabicName();

  @EntityGraph("NEG_VEHICLE_MODEL_MAKE")
  List<VehicleModel> findByFaceModelId(Integer faceModelId);

  @EntityGraph("NEG_VEHICLE_MODEL_MAKE")
  @Query("SELECT vm.id FROM VehicleModel vm WHERE vm.faceModelId IN :faceModelIds")
  List<Integer> findByFaceModelIds(List<Integer> faceModelIds);

  @EntityGraph("NEG_VEHICLE_MODEL_MAKE")
  @Query("SELECT vm.faceModelId FROM VehicleModel vm WHERE vm.id IN :ids")
  List<Integer> findFaceModelIdsByIds(List<Integer> ids);

  @EntityGraph("NEG_VEHICLE_MODEL_MAKE")
  @Query("SELECT vm FROM VehicleModel vm WHERE vm.materialName = :modelName")
  List<VehicleModel> findByMaterialName(String modelName);

  @EntityGraph("NEG_VEHICLE_MODEL_MAKE")
  @Query("SELECT vm FROM VehicleModel vm WHERE vm.sapMaterialId = :sapMaterialId")
  VehicleModel findBySapMaterialId(String sapMaterialId);
}
