package com.lumi.rental.core.fleet.mapper;

import com.lumi.rental.core.fleet.dto.VehicleDetailSyncEventDTO;
import com.lumi.rental.core.fleet.dto.VehicleOperationalDataSyncEventDTO;
import com.lumi.rental.core.fleet.dto.VehicleReservationSyncEventDTO;
import com.lumi.rental.core.fleet.entity.Vehicle;
import com.lumi.rental.core.fleet.entity.VehicleFinancialInfo;
import com.lumi.rental.core.fleet.entity.VehicleOperationalData;
import com.lumi.rental.core.fleet.entity.VehicleReservationData;
import com.lumi.rental.core.fleet.entity.VehicleSyncEntity;
import com.lumi.rental.core.fleet.enums.ServiceType;
import com.lumi.rental.core.fleet.enums.StatusType;
import com.lumi.rental.core.fleet.enums.SubServiceType;
import org.mapstruct.*;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VehicleSyncMapper {

  @Mapping(target = "model", ignore = true)
  @Mapping(target = "year", source = "modelYear")
  Vehicle buildVehicleInfo(VehicleDetailSyncEventDTO eventDTO);

  @Mapping(target = "assetId", source = "assetId", qualifiedByName = "convertAssetId")
  VehicleSyncEntity buildVehicleSyncInfo(VehicleDetailSyncEventDTO eventDTO);

  VehicleFinancialInfo buildVehicleFinancialInfo(VehicleDetailSyncEventDTO eventDTO);

  VehicleFinancialInfo updateVehicleFinancialInfo(
      @MappingTarget VehicleFinancialInfo existingVehicle, VehicleDetailSyncEventDTO eventDTO);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "plateNo", source = "vehicle.plateNo")
  @Mapping(target = "model", source = "vehicle.model")
  @Mapping(target = "serviceTypeId", source = "serviceType.id")
  @Mapping(target = "subServiceTypeId", source = "subServiceType.id")
  //    @Mapping(target = "vehicleStatusId", source = "statusType.id")
  @Mapping(target = "ownerBranchId", source = "eventDTO.currentLocationId")
  @Mapping(
      target = "fuelLevel",
      source = "eventDTO.fuelLevel",
      qualifiedByName = "convertFuelLevel")
  VehicleOperationalData buildVehicleOperationalData(
      VehicleOperationalDataSyncEventDTO eventDTO,
      Vehicle vehicle,
      ServiceType serviceType,
      SubServiceType subServiceType,
      StatusType statusType);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "plateNo", source = "vehicle.plateNo")
  @Mapping(target = "model", source = "vehicle.model")
  @Mapping(target = "serviceTypeId", source = "serviceType.id")
  @Mapping(target = "subServiceTypeId", source = "subServiceType.id")
  //    @Mapping(target = "vehicleStatusId", source = "statusType.id")
  @Mapping(target = "ownerBranchId", source = "eventDTO.currentLocationId")
  @Mapping(
      target = "fuelLevel",
      source = "eventDTO.fuelLevel",
      qualifiedByName = "convertFuelLevel")
  VehicleOperationalData updateVehicleOperationalData(
      @MappingTarget VehicleOperationalData vehicleOperationalData,
      VehicleOperationalDataSyncEventDTO eventDTO,
      Vehicle vehicle,
      ServiceType serviceType,
      SubServiceType subServiceType,
      StatusType statusType);

  @Named("convertFuelLevel")
  default Integer convertFuelLevel(Integer fuelLevel) {
    var fuel = (fuelLevel % 2 != 0) ? fuelLevel + 1 : fuelLevel;
    return switch (fuel) {
      case 2 -> 1;
      case 4 -> 2;
      case 6 -> 3;
      case 8 -> 4;
      default -> 0;
    };
  }

  @Named("convertAssetId")
  default String convertAssetId(String assetId) {
    if (assetId == null || assetId.isEmpty()) {
      return assetId;
    }
    return assetId.replaceFirst("^0+(?!$)", "");
  }

  VehicleReservationData buildVehicleReservation(VehicleReservationSyncEventDTO event);

  VehicleReservationData updateVehicleReservation(
      @MappingTarget VehicleReservationData reservation, VehicleReservationSyncEventDTO event);
}
