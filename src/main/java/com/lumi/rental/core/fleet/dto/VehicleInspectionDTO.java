package com.lumi.rental.core.fleet.dto;

import com.lumi.rental.core.fleet.util.DateUtil;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleInspectionDTO implements Serializable {

  @Serial private static final long serialVersionUID = -1L;
  private LocalDateTime lastInspected;

  public Long getLastInspected() {
    return DateUtil.convertLocalDateToEpoch(lastInspected);
  }
}
