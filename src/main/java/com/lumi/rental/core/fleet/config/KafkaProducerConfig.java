package com.lumi.rental.core.fleet.config;

import java.util.HashMap;
import java.util.Map;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.BytesSerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.JsonSerializer;

@EnableKafka
@Configuration
public class KafkaProducerConfig {

  private static final Logger log = LoggerFactory.getLogger("application");

  @Value("${spring.kafka.bootstrap-servers}")
  private String kafkaBootstrapAddress;

  @Value("${spring.default.kafka.bootstrap-servers}")
  private String defaultKafkaBootstrapAddress;

  @Value("${spring.kafka.consumer.group-id}")
  private String groupId;

  @Bean("coreKafkaTemplate")
  public KafkaTemplate<Object, Object> coreKafkaTemplate() {
    log.info("========= Connecting core kafka default: {} =========", kafkaBootstrapAddress);
    return new KafkaTemplate<>(producerFactory(kafkaBootstrapAddress));
  }

  @Bean("coreKafkaDLTTemplate")
  public KafkaTemplate<String, Object> coreKafkaDLTTemplate() {
    log.info("========= Connecting core kafka default DLT: {} =========", kafkaBootstrapAddress);
    Map<String, Object> configProps = getKafkaConfig(kafkaBootstrapAddress);
    configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
    return new KafkaTemplate<>(new DefaultKafkaProducerFactory<>(configProps));
  }

  @Bean("coreKafkaBatchDLTTemplate")
  public KafkaTemplate<String, Object> coreKafkaBatchDLTTemplate() {
    log.info("========= Connecting core kafka Batch DLT : {} =========", kafkaBootstrapAddress);
    Map<String, Object> configProps = getKafkaConfig(kafkaBootstrapAddress);
    configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, BytesSerializer.class);
    return new KafkaTemplate<>(new DefaultKafkaProducerFactory<>(configProps));
  }

  @Bean("defaultKafkaTemplate")
  public KafkaTemplate<Object, Object> defaultKafkaTemplate() {
    log.info("========= Connecting default kafka : {} =========", defaultKafkaBootstrapAddress);
    return new KafkaTemplate<>(producerFactory(defaultKafkaBootstrapAddress));
  }

  @Bean("defaultKafkaDLTTemplate")
  public KafkaTemplate<String, Object> defaultKafkaDLTTemplate() {
    Map<String, Object> configProps = getKafkaConfig(defaultKafkaBootstrapAddress);
    configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
    return new KafkaTemplate<>(new DefaultKafkaProducerFactory<>(configProps));
  }

  private ProducerFactory<Object, Object> producerFactory(String bootstrapAddress) {
    Map<String, Object> configProps = getKafkaConfig(bootstrapAddress);
    return new DefaultKafkaProducerFactory<>(configProps);
  }

  private Map<String, Object> getKafkaConfig(String bootstrapAddress) {
    Map<String, Object> configProps = new HashMap<>();
    configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapAddress);
    configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
    configProps.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
    return configProps;
  }

  @Bean("defaultRetryTopicKafkaTemplate")
  public KafkaTemplate<String, Object> defaultRetryTopicKafkaTemplate() {
    return coreKafkaDLTTemplate();
  }
}
