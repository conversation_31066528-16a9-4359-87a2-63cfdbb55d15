package com.lumi.rental.core.fleet.listener;

import com.lumi.rental.core.fleet.dto.VehicleOperationalDataSyncEventDTO;
import com.lumi.rental.core.fleet.service.VehicleSyncService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleOperationalDataSyncEventListener {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final VehicleSyncService syncService;

  @KafkaListener(
      topics = {"${kafka.topic.sync.vehicle.operational.data}"},
      groupId = "sync-vehicle-operational-data",
      concurrency = "${kafka.listen.concurrency}",
      containerFactory = "kafkaListenerContainerFactory",
      autoStartup = "${kafka.listen.auto.start:true}")
  public void listen(VehicleOperationalDataSyncEventDTO eventDTO) {
    try {
      log.info(
          "[LISTENER] message received for vehicle-operational-data sync-vehicle-operational-data {}",
          eventDTO);
      syncService.syncVehicleOperationalData(eventDTO);
    } catch (Exception ex) {
      log.error(
          "[LISTENER] error while processing vehicle-operational-data sync-vehicle-operational-data",
          ex);
    }
  }
}
