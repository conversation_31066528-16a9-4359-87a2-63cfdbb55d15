package com.lumi.rental.core.fleet.listener;

import com.lumi.rental.core.fleet.service.MaintenanceService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.common.utils.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleMaintenanceSQLEventListener {

  private static final Logger log = LoggerFactory.getLogger("application");
  private final MaintenanceService maintenanceService;

  @KafkaListener(
      topics = {"${kafka.topic.vehicle.maintenance.data}"},
      groupId = "sync-vehicle-maintenance",
      concurrency = "${kafka.listen.concurrency}",
      containerFactory = "kafkaBatchListenerContainerFactory",
      autoStartup = "${kafka.listen.auto.start:true}")
  public void listen(@Payload List<Bytes> eventList) {
    try {
      log.info(
          "[LISTENER] message received for vehicle maintenance data sync-vehicle-maintenance batch-size {}",
          eventList.size());
      maintenanceService.syncVehicleMaintenanceDataInSQLDataSource(eventList);
    } catch (Exception ex) {
      log.error(
          "[LISTENER] error while processing vehicle maintenance data sync-vehicle-maintenance batch-event",
          ex);
    }
  }

  //  @RetryableTopic(attempts = "1", dltStrategy = DltStrategy.NO_DLT)
  //  @KafkaListener(
  //      topics = {"${kafka.topic.vehicle.maintenance.data}" + DEFAULT_DLT_SUFFIX},
  //      groupId = "sync-maintenance-data-dlt",
  //      containerFactory = "kafkaListenerContainerFactory",
  //      autoStartup = "${kafka.listen.auto.start:true}")
  //  public void listenTODLT(@Header(DLT_ORIGINAL_TOPIC) String originalTopic,
  //      @Header(DLT_EXCEPTION_MESSAGE) String exceptionMessage,
  //      @Header(DLT_EXCEPTION_STACKTRACE) String exceptionStackTrace) {
  //    log.info(
  //        "Unable to process message on topic:{} , errorMessage:{}, exceptionStacktrace:{}",
  //        originalTopic, exceptionMessage, exceptionStackTrace);
  //  }
}
