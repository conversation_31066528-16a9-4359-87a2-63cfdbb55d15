package com.lumi.rental.core.fleet.listener;

import com.seera.lumi.core.fleet.dto.VehicleDocumentMigrationDTO;
import com.seera.lumi.core.fleet.service.VehicleDocumentMigrationService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class VehicleDocumentMigrationListener {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final VehicleDocumentMigrationService documentMigrationService;

  @KafkaListener(
      topics = {"${kafka.topic.sync.vehicle.document}"},
      groupId = "sync-vehicle-document",
      concurrency = "${kafka.listen.single.concurrency}",
      containerFactory = "kafkaListenerContainerFactory",
      autoStartup = "${kafka.listen.auto.start:true}")
  public void listen(@Payload VehicleDocumentMigrationDTO eventDTO) {
    try {
      log.info(
          "[LISTENER] message received for vehicle document data sync-vehicle-document {}",
          eventDTO);
      documentMigrationService.migrateVehicleDocuments(eventDTO);
    } catch (Exception ex) {
      log.error(
          "[LISTENER] error while processing vehicle document data sync-vehicle-document event {} exception {}",
          eventDTO,
          ex.getMessage());
    }
  }

  //  @KafkaListener(
  //      topics = {"${kafka.topic.vehicle.document.migration}"},
  //      groupId = "document-migration",
  //      concurrency = "${kafka.listen.single.concurrency}",
  //      containerFactory = "kafkaListenerContainerFactory",
  //      autoStartup = "${kafka.listen.auto.start:true}")
  //  public void listen(@Payload VehicleDocumentMigrationEventDTO eventDTO) {
  //    try {
  //      documentMigrationService.migrateVehicleDocuments(eventDTO);
  //    } catch (Exception ex) {
  //      log.error("Error while processing event {} ", eventDTO, ex);
  //    }
  //  }

  //  @KafkaListener(
  //      topics = {"${kafka.topic.vehicle.document.metadata.data}"},
  //      groupId = "document-migration",
  //      concurrency = "${kafka.listen.concurrency}",
  //      containerFactory = "kafkaListenerContainerFactory",
  //      autoStartup = "${kafka.listen.auto.start:true}")
  //  public void listen(@Payload VehicleDocumentMetadataEvent event) {
  //    try {
  //      log.info("Message received, Value:{}", event);
  //      documentMigrationService.processVehicleDocumentEvent(event);
  //    } catch (Exception ex) {
  //      log.error("Error while listening VehicleDocumentMetadataEvent ", ex);
  //    }
  //  }
}
