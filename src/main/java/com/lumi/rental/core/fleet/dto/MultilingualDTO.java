package com.lumi.rental.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MultilingualDTO implements Serializable {

  @Serial private static final long serialVersionUID = -1L;

  private String en;
  private String ar;

  public String getEn() {
    return StringUtils.isEmpty(en) ? null : en;
  }

  public void setEn(String en) {
    this.en = en;
  }

  public String getAr() {
    return StringUtils.isEmpty(ar) ? null : ar;
  }

  public void setAr(String ar) {
    this.ar = ar;
  }
}
