package com.lumi.rental.core.fleet.repository.es;

import com.seera.lumi.core.fleet.dto.ESDeepSearchRequestDTO;
import com.seera.lumi.core.fleet.dto.ESDeepSearchResponse;
import com.seera.lumi.core.fleet.dto.SearchRequestDTO;
import com.seera.lumi.core.fleet.dto.SearchResponse;
import com.seera.lumi.core.fleet.dto.VehicleBasicResponseDTO;
import com.seera.lumi.core.fleet.dto.VehicleSearchRequestDTO;
import com.seera.lumi.core.fleet.entity.opensearch.ESVehicleData;
import com.seera.lumi.core.fleet.enums.VehicleSplitKeyName;
import com.seera.lumi.core.fleet.vo.GroupedData;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;
import org.opensearch.search.SearchHit;
import org.springframework.data.domain.Page;

public interface ESVehicleRepositoryCustom {

  GroupedData fetchVehicleGroupByKey(Set<String> platNos, VehicleSplitKeyName keyName);

  List<ESVehicleData> findByPlateNoIn(Set<String> plateNos);

  Page<VehicleBasicResponseDTO> searchVehicleData(VehicleSearchRequestDTO searchRequest);

  Stream<SearchHit> findByPlateNoIncludeKeys(Set<String> plateNumbers, List<String> includeKeys);

  SearchResponse<String> getVehicleSuggestions(SearchRequestDTO searchRequestDTO);

  ESDeepSearchResponse<ESVehicleData> scrollOverAllVehiclesWithPagination(
      ESDeepSearchRequestDTO requestDTO);
}
