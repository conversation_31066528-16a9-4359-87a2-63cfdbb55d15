package com.lumi.rental.core.fleet.request;

import java.io.Serial;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SearchRequestBase extends BasePageRequest {

  @Serial private static final long serialVersionUID = -600126160341757685L;

  private String query;
  private LocalDateTime from;
  private LocalDateTime to;

  @Override
  public String toString() {
    return "SearchRequest{" + "query='" + query + '\'' + ", from=" + from + ", to=" + to + '}';
  }
}
