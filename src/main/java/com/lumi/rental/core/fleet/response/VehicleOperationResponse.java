package com.lumi.rental.core.fleet.response;

import static lombok.AccessLevel.PRIVATE;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.lumi.rental.core.fleet.dto.FuelLevelInfo;
import java.time.Instant;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VehicleOperationResponse extends VehicleBasicResponseV3 {

  Integer fuelLevel;
  String fuelLevelDisplay;
  FuelLevelInfo fuelLevelInfo;
  Integer odometerReading;
  Long lastMaintenance = Instant.now().minusSeconds(60 * 60 * 24 * 10).toEpochMilli();
  Long lastCleaned =
      Instant.now().minusSeconds(60 * 60 * 4).toEpochMilli(); // TODO : set correct value
  Long lastInspected = Instant.now().minusSeconds(60 * 60 * 5).toEpochMilli();
}
