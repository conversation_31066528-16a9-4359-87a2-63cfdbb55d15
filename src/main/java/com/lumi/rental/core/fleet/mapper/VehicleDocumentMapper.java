package com.lumi.rental.core.fleet.mapper;

import com.lumi.rental.core.fleet.entity.DocumentType;
import com.lumi.rental.core.fleet.entity.VehicleDocument;
import com.lumi.rental.core.fleet.request.CreateUpdateVehicleDocument;
import com.lumi.rental.core.fleet.response.DocumentTypeResponse;
import com.lumi.rental.core.fleet.response.VehicleDocumentResponse;
import com.seera.lumi.core.fleet.dto.VehicleDocumentMigrationDTO;
import java.time.LocalDateTime;
import org.apache.commons.io.FilenameUtils;
import org.mapstruct.*;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    uses = VehicleDocumentTypeMapper.class)
public interface VehicleDocumentMapper {

  VehicleDocumentResponse buildVehicleDocumentResponse(VehicleDocument vehicleDocument);

  DocumentTypeResponse toDocumentTypeResponse(VehicleDocument vehicle);

  VehicleDocument toEntity(CreateUpdateVehicleDocument vehicle);

  @Mapping(source = "dto.pageNo", target = "pageNo")
  @Mapping(source = "dto.licenceNo", target = "plateNo")
  @Mapping(source = "documentType", target = "type")
  @Mapping(source = "documentType.id", target = "id", ignore = true)
  @Mapping(source = "dto.fileName", target = "extension", qualifiedByName = "getExtension")
  VehicleDocument toEntity(
      VehicleDocumentMigrationDTO dto,
      DocumentType documentType,
      LocalDateTime transactionDateTime);

  @Named("getExtension")
  default String getExtension(String fileName) {
    return FilenameUtils.getExtension(fileName);
  }

  @Named("partialUpdate")
  @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
  void partialUpdate(@MappingTarget VehicleDocument entity, VehicleDocument dto);

  @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
  void partialUpdate(@MappingTarget VehicleDocument entity, CreateUpdateVehicleDocument dto);
}
