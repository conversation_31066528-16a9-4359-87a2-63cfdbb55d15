package com.lumi.rental.core.fleet.repository.es.impl;

import static com.seera.lumi.core.fleet.opensearch.OpenSearchUtil.DEFAULT_OS_FETCH_SIZE;
import static com.seera.lumi.core.fleet.opensearch.OpenSearchUtil.searchData;
import static com.seera.lumi.core.fleet.utils.IndexNames.VEHICLE_MAINTENANCE_DATA;
import static java.util.List.of;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;
import static org.opensearch.search.aggregations.AggregationBuilders.*;
import static org.opensearch.search.aggregations.PipelineAggregatorBuilders.bucketSort;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumi.rental.core.fleet.entity.es.ESMaintenanceLog;
import com.lumi.rental.core.fleet.repository.es.ESMaintenanceLogRepositoryCustom;
import com.seera.lumi.core.fleet.opensearch.OpenSearchFetchRequest;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.index.query.*;
import org.opensearch.search.SearchHits;
import org.opensearch.search.aggregations.AggregationBuilder;
import org.opensearch.search.aggregations.BucketOrder;
import org.opensearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.opensearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.opensearch.search.aggregations.metrics.ParsedCardinality;
import org.opensearch.search.aggregations.metrics.ParsedTopHits;
import org.opensearch.search.sort.FieldSortBuilder;
import org.opensearch.search.sort.SortOrder;

@RequiredArgsConstructor
public class ESMaintenanceLogRepositoryImpl implements ESMaintenanceLogRepositoryCustom {

  private static final String GROUP_AGG = "GROUP_AGG";
  private static final String CARDINAL_AGG = "CARDINAL_AGG";
  private static final String MAX_DATE_AGG = "MAX_DATE_AGG";
  private static final String SORTED_BUCKET_AGG = "SORTED_BUCKET_AGG";
  private static final String DATE_GROUP_AGG = "DATE_GROUP_AGG";
  private static final String TOP_HIT_DATA = "TOP_HIT_DATA";
  private static final String PLATE_NO_FIELD = "plateNo.keyword";
  private static final String SERVICE_TYPE = "serviceType.keyword";
  private static final String DATE_FIELD = "date";
  private final RestHighLevelClient highLevelClient;
  private final ObjectMapper objectMapper;

  @Override
  public List<ESMaintenanceLog> findByPlateNo(String plateNo) {
    QueryBuilder queryBuilder = new TermQueryBuilder(PLATE_NO_FIELD, plateNo);
    OpenSearchFetchRequest fetchRequest =
        new OpenSearchFetchRequest(highLevelClient, VEHICLE_MAINTENANCE_DATA)
            .setFetchSource(true)
            .setSize(DEFAULT_OS_FETCH_SIZE)
            .setQueryBuilder(queryBuilder);
    SearchResponse searchResponse = searchData(fetchRequest);
    SearchHits searchHits = searchResponse.getHits();
    return Arrays.stream(searchHits.getHits())
        .map(hit -> objectMapper.convertValue(hit.getSourceAsMap(), ESMaintenanceLog.class))
        .collect(toList());
  }

  @Override
  public long countDistinctPlateNoHasMaintenanceHistory(Set<String> platNos) {
    QueryBuilder queryBuilder = new TermsQueryBuilder(PLATE_NO_FIELD, platNos);
    AggregationBuilder aggregationBuilder =
        cardinality(CARDINAL_AGG).field(PLATE_NO_FIELD).precisionThreshold(Integer.MAX_VALUE);
    SearchResponse searchResponse =
        searchData(
            VEHICLE_MAINTENANCE_DATA,
            queryBuilder,
            of(aggregationBuilder),
            of(),
            false,
            highLevelClient);
    return ((ParsedCardinality) searchResponse.getAggregations().get(CARDINAL_AGG)).getValue();
  }

  @Override
  public long countVehicleServicedInDateRange(
      Set<String> plateNumbers, LocalDate fromDate, LocalDate toDate) {
    BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
    boolQueryBuilder.filter(new TermsQueryBuilder(PLATE_NO_FIELD, plateNumbers));
    boolQueryBuilder.filter(new ExistsQueryBuilder(DATE_FIELD));
    boolQueryBuilder.filter(new RangeQueryBuilder(DATE_FIELD).gte(fromDate).lt(toDate));
    AggregationBuilder aggregationBuilder =
        cardinality(CARDINAL_AGG).field(PLATE_NO_FIELD).precisionThreshold(Integer.MAX_VALUE);
    SearchResponse searchResponse =
        searchData(
            VEHICLE_MAINTENANCE_DATA,
            boolQueryBuilder,
            of(aggregationBuilder),
            of(),
            false,
            highLevelClient);
    return ((ParsedCardinality) searchResponse.getAggregations().get(CARDINAL_AGG)).getValue();
  }

  @Override
  public List<ESMaintenanceLog> getLastMaintenanceLogs(
      Set<String> plateNumbers, String serviceType) {
    BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
    boolQueryBuilder.filter(new TermsQueryBuilder(PLATE_NO_FIELD, plateNumbers));
    boolQueryBuilder.filter(new ExistsQueryBuilder(DATE_FIELD));
    if (nonNull(serviceType)) {
      boolQueryBuilder.filter(new TermQueryBuilder(SERVICE_TYPE, serviceType));
    }
    AggregationBuilder aggregationBuilder =
        terms(GROUP_AGG)
            .field(PLATE_NO_FIELD)
            .size(Integer.MAX_VALUE)
            .subAggregation(max(MAX_DATE_AGG).field(DATE_FIELD))
            .subAggregation(
                bucketSort(
                    SORTED_BUCKET_AGG,
                    List.of(new FieldSortBuilder(MAX_DATE_AGG + ".value").order(SortOrder.DESC))))
            .subAggregation(
                terms(DATE_GROUP_AGG)
                    .field(DATE_FIELD)
                    .size(1)
                    .order(BucketOrder.aggregation("_key", false))
                    .subAggregation(topHits(TOP_HIT_DATA).size(100)));
    SearchResponse searchResponse =
        searchData(
            VEHICLE_MAINTENANCE_DATA,
            boolQueryBuilder,
            of(aggregationBuilder),
            of(),
            false,
            highLevelClient);
    ParsedStringTerms parentTerms = searchResponse.getAggregations().get(GROUP_AGG);
    return parentTerms.getBuckets().stream()
        .map(parentBucket -> (ParsedLongTerms) parentBucket.getAggregations().get(DATE_GROUP_AGG))
        .flatMap((ParsedLongTerms childTerm) -> childTerm.getBuckets().stream())
        .flatMap(
            childBucket ->
                Arrays.stream(
                    ((ParsedTopHits) childBucket.getAggregations().get(TOP_HIT_DATA))
                        .getHits()
                        .getHits()))
        .map(hit -> objectMapper.convertValue(hit.getSourceAsMap(), ESMaintenanceLog.class))
        .toList();
  }

  @Override
  public long findByPlateNoAndDateAndServiceType(
      String plateNo, LocalDate date, String serviceType) {
    BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
    boolQueryBuilder.filter(new TermsQueryBuilder(PLATE_NO_FIELD, plateNo));
    boolQueryBuilder.filter(new TermQueryBuilder(DATE_FIELD, date));
    boolQueryBuilder.filter(new TermQueryBuilder(SERVICE_TYPE, serviceType));
    SearchResponse searchResponse =
        searchData(
            new OpenSearchFetchRequest(highLevelClient, VEHICLE_MAINTENANCE_DATA)
                .setFetchSource(false)
                .setIncludeTotalCount(true)
                .setQueryBuilder(boolQueryBuilder));
    return Objects.requireNonNull(searchResponse.getHits().getTotalHits()).value;
  }
}
