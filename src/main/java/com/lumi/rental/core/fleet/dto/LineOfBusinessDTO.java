package com.lumi.rental.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.lumi.rental.core.fleet.enums.ServiceType;
import com.lumi.rental.core.fleet.enums.SubServiceType;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LineOfBusinessDTO implements Serializable {
  private ServiceType serviceType;
  private SubServiceType subServiceType;
}
