package com.lumi.rental.core.fleet.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import java.io.Serializable;
import lombok.Data;

@Data
public class CloseNRMRequest implements Serializable {

  @NotNull private Integer nrmId;

  @NotNull
  @Min(0)
  @Max(4)
  private Integer fuelLevel;

  @NotNull @PositiveOrZero private Integer odometerReading;

  private String remarks;
}
