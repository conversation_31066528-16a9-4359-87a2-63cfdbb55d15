package com.lumi.rental.core.fleet.controller;

import com.lumi.rental.core.fleet.enums.*;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/** Controller for filter operations. */
@RestController
@RequestMapping("/v1/filter")
@RequiredArgsConstructor
public class FilterController {

  private <T> List<Map<String, Object>> getEnumValues(T[] values, EnumMapper<T> mapper) {
    return Arrays.stream(values).map(mapper::map).collect(Collectors.toList());
  }

  @GetMapping("/service-types")
  public ResponseEntity<List<Map<String, Object>>> getServiceTypes() {
    return ResponseEntity.ok(
        getEnumValues(
            ServiceType.values(), type -> Map.of("id", type.getId(), "name", type.getCode())));
  }

  @GetMapping("/status-types")
  public ResponseEntity<List<Map<String, Object>>> getStatusTypes() {
    return ResponseEntity.ok(
        getEnumValues(
            StatusType.values(), type -> Map.of("id", type.getId(), "name", type.getCode())));
  }

  @GetMapping("/need-preparation-reasons")
  public ResponseEntity<List<Map<String, Object>>> getNeedPreparationReasons() {
    return ResponseEntity.ok(
        getEnumValues(
            NeedPreparationReason.values(),
            reason -> Map.of("id", reason.getId(), "name", reason.getDisplayName())));
  }

  @GetMapping("/fuel-levels")
  public ResponseEntity<List<Map<String, Object>>> getFuelLevels() {
    return ResponseEntity.ok(
        getEnumValues(
            FuelLevel.values(),
            level -> Map.of("id", level.getValue(), "name", level.getDescription())));
  }

  @GetMapping("/fuel-types")
  public ResponseEntity<List<Map<String, Object>>> getFuelTypes() {
    return ResponseEntity.ok(
        getEnumValues(
            FuelType.values(),
            type -> Map.of("id", type.getCode(), "name", type.getDescription())));
  }

  @GetMapping("/yaqeen-out-of-service-reasons")
  public ResponseEntity<List<Map<String, Object>>> getYaqeenOutOfServiceReasons() {
    return ResponseEntity.ok(
        getEnumValues(
            YaqeenOutOfServiceReason.values(),
            reason -> Map.of("id", reason.getId(), "name", reason.getDisplayName())));
  }

  @GetMapping("/non-revenue-movement-reasons")
  public ResponseEntity<List<Map<String, Object>>> getNonRevenueMovementReasons() {
    return ResponseEntity.ok(
        getEnumValues(
            NonRevenueMovementReason.values(),
            reason -> Map.of("id", reason.getId(), "name", reason.getName())));
  }

  private interface EnumMapper<T> {
    Map<String, Object> map(T value);
  }
}
