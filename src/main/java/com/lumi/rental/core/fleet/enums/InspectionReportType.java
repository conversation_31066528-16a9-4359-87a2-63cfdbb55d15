package com.lumi.rental.core.fleet.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.io.IOException;
import lombok.Getter;

@Getter
@JsonDeserialize(using = InspectionReportTypeDeserializer.class)
public enum InspectionReportType {
  CHECK_IN(1),
  CHECK_OUT(2);

  private final Integer id;

  InspectionReportType(Integer id) {
    this.id = id;
  }

  @JsonCreator
  public static InspectionReportType fromId(Integer id) {
    for (InspectionReportType type : values()) {
      if (type.getId().equals(id)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Unknown enum value: " + id);
  }

  public Integer getId() {
    return id;
  }

  @Override
  public String toString() {
    return id.toString();
  }
}

class InspectionReportTypeDeserializer extends JsonDeserializer<InspectionReportType> {

  @Override
  public InspectionReportType deserialize(JsonParser p, DeserializationContext ctxt)
      throws IOException {
    int value = p.getIntValue();
    for (InspectionReportType type : InspectionReportType.values()) {
      if (type.getId() == value) {
        return type;
      }
    }
    throw new IllegalArgumentException("Unknown value for InspectionReportType: " + value);
  }
}
