package com.lumi.rental.core.fleet.repository.specification;

import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

import com.lumi.rental.core.fleet.entity.Vehicle;
import com.seera.lumi.core.fleet.dto.VehicleSearchRequestDTO;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@AllArgsConstructor
public class VehicleSpecification implements Specification<Vehicle> {

  private static final String YEAR_COLUMN = "year";
  private static final String PLATE_NO_COLUMN = "plateNo";
  private static final String MAKE_COLUMN = "make";
  private static final String MODEL_COLUMN = "model";
  private static final String NAME_COLUMN = "nameEn";
  private final VehicleSearchRequestDTO filter;

  @Override
  public Predicate toPredicate(
      Root<Vehicle> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    List<Predicate> predicate = new ArrayList<>();
    if (isNotEmpty(filter.getPlateNumbers())) {
      predicate.add(root.get(PLATE_NO_COLUMN).in(filter.getPlateNumbers()));
    }
    if (isNotEmpty(filter.getMakes())) {
      predicate.add(root.get(MODEL_COLUMN).get(MAKE_COLUMN).get(NAME_COLUMN).in(filter.getMakes()));
    }
    if (isNotEmpty(filter.getModels())) {
      predicate.add(root.get(MODEL_COLUMN).get(NAME_COLUMN).in(filter.getModels()));
    }
    if (isNotEmpty(filter.getModelYears())) {
      predicate.add(root.get(YEAR_COLUMN).in(filter.getModelYears()));
    }
    //    if (isNotEmpty(filter.getCarGroups())) {
    //      predicate.add(root.get(CAR_GROUP_COLUMN).in(filter.getCarGroups()));
    //    }
    return criteriaBuilder.and(predicate.toArray(Predicate[]::new));
  }
}
