package com.lumi.rental.core.fleet.mapper;

import com.lumi.rental.core.fleet.dto.DocumentTypeDTO;
import com.lumi.rental.core.fleet.entity.DocumentType;
import org.mapstruct.*;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VehicleDocumentTypeMapper {

  DocumentType toEntity(DocumentTypeDTO vehicle);

  DocumentTypeDTO buildDocumentTypeDTO(DocumentType vehicle);

  @Named("partialUpdate")
  @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
  void partialUpdateDocumentType(@MappingTarget DocumentType entity, DocumentType dto);
}
