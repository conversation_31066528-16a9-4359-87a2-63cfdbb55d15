package com.lumi.rental.core.fleet.enums;

public enum FuelType {
  DIESEL("D", "DIESEL"),
  PETROL("P", "PETROL"),
  PETROL_91("1", "PETROL_91"),
  PETROL_95("5", "PETROL_95");

  private final String code;
  private final String description;

  FuelType(String code, String description) {
    this.code = code;
    this.description = description;
  }

  public static FuelType fromCode(String code) {
    for (FuelType type : values()) {
      if (type.code.equalsIgnoreCase(code)) {
        return type;
      }
    }
    return null;
  }

  public String getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }
}
