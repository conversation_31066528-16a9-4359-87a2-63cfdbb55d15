package com.lumi.rental.core.fleet.enums;

import lombok.Getter;

@Getter
public enum NeedPreparationReason {
  FUELING_CLEANING(1, "Fueling/Cleaning"),
  WORKSHOP_TRANSFER(2, "Workshop Transfer"),
  FlEET_PREP(3, "FlEET PREP");

  private final Integer id;
  private final String displayName;

  NeedPreparationReason(Integer id, String displayName) {
    this.id = id;
    this.displayName = displayName;
  }

  public static NeedPreparationReason fromId(Integer id) {
    for (NeedPreparationReason type : values()) {
      if (type.id.equals(id)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Unknown value for NeedPreparationReason: " + id);
  }
}
