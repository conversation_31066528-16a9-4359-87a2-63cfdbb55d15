package com.lumi.rental.core.fleet.api.user;

import com.lumi.rental.core.fleet.api.user.resp.UserResponseDTO;
import com.lumi.rental.core.fleet.config.feign.DownstreamErrorDecoder;
import com.seera.lumi.core.fleet.dto.SearchResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(
    name = "user-service",
    url = "${api.user.service.base.url}",
    dismiss404 = true,
    configuration = {DownstreamErrorDecoder.class})
public interface UserServiceClient {

  @GetMapping("/v2/users")
  SearchResponse<UserResponseDTO> getUserList();

  @GetMapping("/v2/users")
  SearchResponse<UserResponseDTO> getUserBySuccessFactorId(
      @RequestParam("successFactorId") String successFactorId);
}
