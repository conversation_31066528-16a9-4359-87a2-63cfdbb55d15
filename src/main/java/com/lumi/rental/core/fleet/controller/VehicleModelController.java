package com.lumi.rental.core.fleet.controller;

import com.lumi.rental.core.fleet.request.BasePageRequest;
import com.lumi.rental.core.fleet.response.VehicleModelBasicResponse;
import com.lumi.rental.core.fleet.response.VehicleModelResponseV2;
import com.lumi.rental.core.fleet.service.VehicleModelService;
import com.lumi.rental.core.fleet.service.VehicleModelServiceV2;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/vehicle-model")
public class VehicleModelController {

  private final VehicleModelService modelService;
  private final VehicleModelServiceV2 modelServiceV2;

  // TODO : remove this endpoint
  @GetMapping("/{id}")
  public VehicleModelResponseV2 getVehicleModelById(
      @PathVariable(value = "id") final Integer vehicleModelId) {
    return modelServiceV2.getVehicleModelResponseById(vehicleModelId);
  }

  // TODO : remove this endpoint
  @GetMapping
  public Page<VehicleModelBasicResponse> getAllVehicleModel(final BasePageRequest request) {
    return modelService.getAllVehicleModel(
        PageRequest.of(request.getPageNumber(), request.getPageSize()));
  }
}
