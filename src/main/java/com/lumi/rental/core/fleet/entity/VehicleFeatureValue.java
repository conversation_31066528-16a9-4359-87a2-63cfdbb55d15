package com.lumi.rental.core.fleet.entity;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "feature_value")
@EqualsAndHashCode(callSuper = true)
public class VehicleFeatureValue extends BaseEntity {

  @Column(name = "name_en", length = 64)
  private String nameEn;

  @Column(name = "name_ar", length = 64)
  private String nameAr;

  @ManyToOne
  @JoinColumn(name = "feature_id", nullable = false)
  private VehicleFeature feature;

  @Column(name = "is_enabled")
  private Boolean enabled;
}
