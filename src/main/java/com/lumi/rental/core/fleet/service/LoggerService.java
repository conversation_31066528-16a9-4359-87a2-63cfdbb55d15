package com.lumi.rental.core.fleet.service;

import static com.lumi.rental.core.fleet.constants.Constants.LogConstants.*;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumi.rental.core.fleet.config.UpstreamLoggingConfig;
import com.lumi.rental.core.fleet.dto.RequestResponseLoggerObject;
import com.lumi.rental.core.fleet.enums.LogType;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

@Service
@RequiredArgsConstructor
public class LoggerService {

  private static final Logger logger = LoggerFactory.getLogger("reqResLogger");
  private static final Logger appLogger = LoggerFactory.getLogger("application");
  private final UpstreamLoggingConfig upstreamLoggingConfig;
  private final ObjectMapper mapper;

  public void logRequest(ContentCachingRequestWrapper requestWrapper, String requestURI) {
    RequestResponseLoggerObject requestObject =
        RequestResponseLoggerObject.builder()
            .logType(LogType.UPSTREAM_REQRESP.name())
            .eventType(REQUEST)
            .resource(requestURI)
            .methodType(requestWrapper.getMethod())
            .headers(getRequestHeaders(requestWrapper))
            .request(readEntity(requestWrapper.getContentAsByteArray(), 500))
            .build();
    if (logger.isInfoEnabled()) {
      logger.info(requestObject.toString());
    }
  }

  public void logResponse(ContentCachingResponseWrapper responseWrapper, String requestUri) {
    RequestResponseLoggerObject responseObject =
        RequestResponseLoggerObject.builder()
            .logType(LogType.UPSTREAM_REQRESP.name())
            .eventType(RESPONSE)
            .resource(requestUri)
            .status(String.valueOf(responseWrapper.getStatus()))
            .totalDuration(calculateLatency(START_TIME))
            .headers(getResponseHeader(responseWrapper))
            .response(
                readEntity(responseWrapper.getContentAsByteArray(), responseWrapper.getStatus()))
            .build();
    if (logger.isInfoEnabled()) {
      logger.info(responseObject.toString());
    }
    MDC.clear();
  }

  private Object readEntity(byte[] content, int status) {
    try {
      return mapper.readTree(content);
    } catch (Exception ex) {
      logger.error("Failed to parse body for method: ", ex);
      return ObjectUtils.NULL;
    }
  }

  private Map<String, String> getRequestHeaders(HttpServletRequest httpRequest) {
    try {
      return Collections.list(httpRequest.getHeaderNames()).stream()
          //          .filter(key -> upstreamLoggingConfig.getHeader().contains(key))
          .collect(Collectors.toMap(name -> name, httpRequest::getHeader));
    } catch (Exception ex) {
      appLogger.error("Error while fetching request header for request exception ()", ex);
      return Collections.emptyMap();
    }
  }

  private Map<String, String> getResponseHeader(HttpServletResponse httpResponse) {
    try {
      return httpResponse.getHeaderNames().stream()
          //          .filter(key -> upstreamLoggingConfig.getHeader().contains(key))
          .collect(Collectors.toMap(key -> key, httpResponse::getHeader, (key1, key2) -> key1));
    } catch (Exception ex) {
      appLogger.error("Error while fetching response header for request exception ()", ex);
      return Collections.emptyMap();
    }
  }

  private long calculateLatency(String startTimeKey) {
    long executionTime = -1;
    try {
      String stTime = MDC.get(startTimeKey);
      if (isNotEmpty(stTime)) {
        executionTime = System.currentTimeMillis() - Long.parseLong(stTime);
      }
    } catch (Exception ex) {
      appLogger.error("Error while calculating latency for request exception ()", ex);
    }
    return executionTime;
  }
}
