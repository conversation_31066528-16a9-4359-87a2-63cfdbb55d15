package com.lumi.rental.core.fleet.mapper;

import com.lumi.rental.core.fleet.dto.FuelLevelInfo;
import com.lumi.rental.core.fleet.enums.FuelLevel;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FuelMapper {

  default FuelLevelInfo toFuelLevelInfo(Integer fuelLevel) {
    return new FuelLevelInfo(fuelLevel, FuelLevel.fromId(fuelLevel).getDescription());
  }
}
