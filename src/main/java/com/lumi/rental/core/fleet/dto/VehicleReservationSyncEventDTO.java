package com.lumi.rental.core.fleet.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lumi.rental.core.fleet.util.LocalDateTimeDeserializer;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class VehicleReservationSyncEventDTO {

  private String assetId;
  private String plateNo;

  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  private LocalDateTime checkInDate;

  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  private LocalDateTime checkOutDate;

  private Integer checkOutBranch;
  private Integer checkInBranch;
}
