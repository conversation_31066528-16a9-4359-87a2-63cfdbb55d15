package com.lumi.rental.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LocationDTO implements Serializable {

  private Integer id;
  private Integer lumiBranchId;
  private MultilingualDTO name;
  private Boolean yaqeenMigrated;
}
