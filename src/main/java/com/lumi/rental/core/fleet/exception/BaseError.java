package com.lumi.rental.core.fleet.exception;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.springframework.http.HttpStatus;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BaseError {
  public static final BaseError INTERNAL_SERVER_BASE_ERROR =
      new BaseError(1001, "error.internal.server.error", HttpStatus.INTERNAL_SERVER_ERROR);
  public static final BaseError BAD_REQUEST_WITH_REASON =
      new BaseError(1002, "{0}", HttpStatus.BAD_REQUEST);
  public static final BaseError UNAUTHORIZED =
      new BaseError(1003, "error.unauthorized", HttpStatus.UNAUTHORIZED);
  public static final BaseError MAX_UPLOAD_SIZE_EXCEEDED =
      new BaseError(1004, "error.max.upload.size.exceeded", HttpStatus.BAD_REQUEST);
  public static final BaseError HTTP_METHOD_NOT_SUPPORT =
      new BaseError(1005, "Http Method Not Supported", HttpStatus.BAD_REQUEST);
  public static final BaseError ARG_TYPE_MISMATCH =
      new BaseError(1006, "Arg type missmatch", HttpStatus.BAD_REQUEST);
  public static final BaseError MALFORMED_BODY =
      new BaseError(1007, "Malformed Request Body", HttpStatus.BAD_REQUEST);
  public static final BaseError UNSUPPORTED_MEDIA_TYPE =
      new BaseError(1008, "Unsupported MediaType", HttpStatus.BAD_REQUEST);
  public static final BaseError ALREADY_EXISTS =
      new BaseError(1009, "Entity {0} already exists ", HttpStatus.CONFLICT);
  public static final BaseError INVALID_ARG_VALUE =
      new BaseError(1010, "Invalid argument value {0}", HttpStatus.BAD_REQUEST);
  public static final BaseError NOT_FOUND =
      new BaseError(1011, "{0} Not found", HttpStatus.NOT_FOUND);
  public static final BaseError EMPTY_ARG_VALUE =
      new BaseError(1012, "Empty argument value : {0}", HttpStatus.BAD_REQUEST);
  public static final BaseError ACCESS_DENIED =
      new BaseError(1013, "error.access.denied", HttpStatus.FORBIDDEN);

  protected String code;
  protected String desc;
  protected String reqId;
  protected String path;
  protected String st;

  @JsonIgnore private HttpStatus httpStatus;

  protected BaseError(int code, String desc, HttpStatus httpStatus) {
    this.code = "FLEET-" + code;
    this.desc = desc;
    this.httpStatus = httpStatus;
  }

  protected BaseError(String code, String desc, HttpStatus httpStatus) {
    this.code = code;
    this.desc = desc;
    this.httpStatus = httpStatus;
  }

  public static BaseError validationError(String msg) {
    return new BaseError(1002, msg, HttpStatus.BAD_REQUEST);
  }

  public LumiCoreErrResponseMdl httpResponseMdl() {
    LumiCoreErrResponseMdl lumiCoreErrResponseMdl = new LumiCoreErrResponseMdl();
    lumiCoreErrResponseMdl.setCode(this.code);
    lumiCoreErrResponseMdl.setDesc(this.desc);
    return lumiCoreErrResponseMdl;
  }
}
