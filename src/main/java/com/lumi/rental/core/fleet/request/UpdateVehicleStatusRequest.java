package com.lumi.rental.core.fleet.request;

import com.lumi.rental.core.fleet.enums.ServiceType;
import com.lumi.rental.core.fleet.enums.StatusType;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;

@Data
public class UpdateVehicleStatusRequest {

  @NotNull @NotEmpty private String plateNo;
  @PositiveOrZero private Integer odometerReading;

  @Min(0)
  @Max(4)
  @PositiveOrZero
  private Integer fuelLevel;

  private Integer branchId;
  private Integer ownerBranchId;
  private ServiceType serviceType;
  private StatusType statusType;
  private Integer statusReason;
}
