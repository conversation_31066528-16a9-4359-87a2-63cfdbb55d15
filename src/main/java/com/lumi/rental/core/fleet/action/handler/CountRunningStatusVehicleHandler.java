package com.lumi.rental.core.fleet.action.handler;

import static com.lumi.rental.core.fleet.enums.VehicleCountType.RUNNING_STATUS_COUNT;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumi.rental.core.fleet.action.CountVehicleHandler;
import com.lumi.rental.core.fleet.enums.VehicleCountType;
import com.lumi.rental.core.fleet.request.VehicleCountRequest;
import com.lumi.rental.core.fleet.response.VehicleCountResponse;
import com.lumi.rental.core.fleet.util.VehicleUtil;
import com.seera.lumi.core.cache.service.CacheService;
import com.seera.lumi.core.fleet.dto.VehicleLiveTrackingEventDTO;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CountRunningStatusVehicleHandler implements CountVehicleHandler {

  private final ObjectMapper mapper;
  private final CacheService cacheService;

  @Override
  public VehicleCountResponse execute(VehicleCountRequest request) {
    var cacheKeys =
        request.getPlateNumbers().stream().map(VehicleUtil::getPlateNumberKeyForIOT).toList();

    var engineStatusData =
        cacheService.get(cacheKeys).stream()
            .filter(Objects::nonNull)
            .map(object -> mapper.convertValue(object, VehicleLiveTrackingEventDTO.class))
            .collect(
                Collectors.groupingBy(
                    VehicleLiveTrackingEventDTO::getEngineStatus, Collectors.counting()));

    return new VehicleCountResponse()
        .setRunning(engineStatusData.getOrDefault(true, 0L))
        .setIdle(engineStatusData.getOrDefault(false, 0L));
  }

  @Override
  public VehicleCountType countType() {
    return RUNNING_STATUS_COUNT;
  }
}
