package com.lumi.rental.core.fleet.service;

import static com.seera.lumi.core.fleet.constants.CacheConstants.*;

import com.lumi.rental.core.fleet.entity.Addon;
import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.mapper.AddonMapper;
import com.lumi.rental.core.fleet.repository.AddonRepository;
import com.lumi.rental.core.fleet.request.CreateUpdateAddonRequest;
import com.lumi.rental.core.fleet.response.AddonResponse;
import com.seera.lumi.core.cache.service.CacheService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

/**
 * Service class for managing addons. Provides methods for retrieving, creating, and updating
 * addons, with caching and exception handling for improved performance and reliability.
 */
@Service
@RequiredArgsConstructor
public class AddonService {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final CacheService cacheService;
  private final AddonMapper addonMapper;
  private final AddonRepository repository;

  /**
   * Retrieves a paginated list of all addons. Results are cached for improved performance.
   *
   * @param pageable Pagination information (page number and size).
   * @return A paginated list of {@link AddonResponse}.
   */
  @Cacheable(
      cacheNames = ADDON_RESPONSE_ALL,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public Page<AddonResponse> getAllAddons(PageRequest pageable) {
    log.info("Fetching all addons with pagination: {}", pageable);
    return repository.findAll(pageable).map(addonMapper::buildAddonResponse);
  }

  /**
   * Retrieves a specific addon by its ID. Results are cached for improved performance.
   *
   * @param addonId The ID of the addon to retrieve.
   * @return The {@link AddonResponse} for the specified ID.
   * @throws BusinessException If the addon is not found.
   */
  @Cacheable(
      cacheNames = ADDON_RESPONSE_BY_ID,
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public AddonResponse getAddonById(Integer addonId) {
    log.info("Fetching addon by ID: {}", addonId);
    return repository
        .findById(addonId)
        .map(addonMapper::buildAddonResponse)
        .orElseThrow(() -> new BusinessException(BaseError.NOT_FOUND, "AddonId"));
  }

  /**
   * Creates a new addon with the provided details.
   *
   * @param request The request containing details for the new addon.
   * @return The created {@link AddonResponse}.
   * @throws BusinessException If an addon with the same code already exists.
   */
  public AddonResponse createAddon(CreateUpdateAddonRequest request) {
    log.info("Creating a new addon with request: {}", request);
    Addon addon = addonMapper.buildAddon(request);
    try {
      Addon savedAddon = repository.saveAndFlush(addon);
      clearCache();
      return addonMapper.buildAddonResponse(savedAddon);
    } catch (DataIntegrityViolationException ex) {
      log.error("Addon with code {} already exists.", request.getCode());
      throw new BusinessException(BaseError.ALREADY_EXISTS, "Addon-Code");
    }
  }

  /**
   * Updates an existing addon with the provided details.
   *
   * @param id The ID of the addon to update.
   * @param request The request containing updated details for the addon.
   * @return The updated {@link AddonResponse}.
   * @throws BusinessException If the addon is not found.
   */
  public AddonResponse updateAddon(int id, CreateUpdateAddonRequest request) {
    log.info("Updating addon with ID: {} and request: {}", id, request);
    try {
      return repository
          .findById(id)
          .map(
              existingAddon -> {
                Addon updatedAddon = addonMapper.buildAddon(request);
                updatedAddon.setId(id);
                Addon savedAddon = repository.saveAndFlush(updatedAddon);
                clearCache();
                return addonMapper.buildAddonResponse(savedAddon);
              })
          .orElseThrow(() -> new BusinessException(BaseError.NOT_FOUND, "AddonId"));
    } catch (DataIntegrityViolationException ex) {
      log.error("Addon with code {} already exists.", request.getCode());
      throw new BusinessException(BaseError.ALREADY_EXISTS, "Addon-Code");
    }
  }

  private void clearCache() {
    cacheService.clearAllKeys(MAKE_CACHE_PREFIX + "*");
  }
}
