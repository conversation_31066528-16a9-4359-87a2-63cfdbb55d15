package com.lumi.rental.core.fleet.entity;

import com.seera.lumi.core.fleet.entity.listener.AuditedEntityListener;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;
import java.io.Serial;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Getter
@Setter
@MappedSuperclass
@EntityListeners(AuditedEntityListener.class)
public class AuditedBaseEntity extends BaseEntity {

  @Serial private static final long serialVersionUID = -4635974310968315539L;

  @CreationTimestamp private LocalDateTime createdOn;
  @UpdateTimestamp private LocalDateTime updatedOn;

  private String createdBy;

  private String updatedBy;
}
