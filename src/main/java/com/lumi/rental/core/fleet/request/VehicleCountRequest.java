package com.lumi.rental.core.fleet.request;

import static java.util.Objects.isNull;

import com.lumi.rental.core.fleet.enums.VehicleCountType;
import com.seera.lumi.core.fleet.dto.VehiclePlateNoRequestDTO;
import jakarta.validation.constraints.NotNull;
import java.util.HashSet;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
public class VehicleCountRequest extends VehiclePlateNoRequestDTO {

  @NotNull(message = "error.count.type.not.found")
  VehicleCountType countType;

  Integer noOfMonths = 1;

  public VehicleCountRequest(List<String> plateNumbers) {
    this.setPlateNumbers(new HashSet<>(plateNumbers));
  }

  public Integer getNoOfMonths() {
    return isNull(noOfMonths) ? 1 : noOfMonths;
  }
}
