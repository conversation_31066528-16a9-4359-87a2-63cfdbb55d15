package com.lumi.rental.core.fleet.request;

import com.seera.lumi.core.fleet.dto.SearchRequestDTO;
import java.io.Serial;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode(callSuper = true)
public class DocumentTypeSearchRequestDTO extends SearchRequestDTO {

  @Serial private static final long serialVersionUID = -650126160341757685L;
  String code;
  String name;

  public DocumentTypeSearchRequestDTO setCode(String code) {
    this.code = code;
    return this;
  }

  public DocumentTypeSearchRequestDTO setName(String name) {
    this.name = name;
    return this;
  }

  @Override
  public String toString() {
    String sb =
        "DocumentTypeSearchRequestDTO{"
            + "code='"
            + code
            + '\''
            + ", name='"
            + name
            + '\''
            + '}'
            + super.toString();
    return sb;
  }
}
