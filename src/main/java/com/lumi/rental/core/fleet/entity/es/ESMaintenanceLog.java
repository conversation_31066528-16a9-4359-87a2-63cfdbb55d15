package com.lumi.rental.core.fleet.entity.es;

import java.time.LocalDate;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

@Getter
@Setter
@Accessors(chain = true)
@Document(indexName = "vehiclemaintenancedata")
public class ESMaintenanceLog {

  private String id;
  private String plateNo;
  private Long glNo;
  private Long km;
  private String remarks;
  private String serviceType;

  @Field(type = FieldType.Date, format = DateFormat.date, pattern = "yyyy-MM-dd")
  private LocalDate date;
}
