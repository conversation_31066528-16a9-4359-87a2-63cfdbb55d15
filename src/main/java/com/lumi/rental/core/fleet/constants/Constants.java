package com.lumi.rental.core.fleet.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Constants {

  public static final String IOT_PLATE_NUMBER_PREFIX = "FLEET::IOT::";
  public static final String AUTH_TOKEN_PREFIX = "Bearer ";
  public static final String BUCKET_FLEET_DOCUMENTS = "fleet-documents";
  public static final String PIPE = "|";

  public static final String TIME_ZONE = "Asia/Riyadh";
  public static final String OPEN_SEARCH_DATE_FORMAT = "yyyy-MM-dd";
  public static final String SERVICE_MAINTENANCE_DUE_NOTIFICATION = "MAINTENANCE_DUE_NOTIFICATION";
  public static final String TABLEAU_DASHBOARD_SUBSCRIPTION = "TABLEAU_DASHBOARD_SUBSCRIPTION";
  public static final String OPEN_STATUS = "open";

  // Logging Constants
  public static final String DEFAULT_MDC_UUID_TOKEN_KEY = "x-b3-traceid";
  public static final String DEFAULT_MDC_CLIENT_ID_KEY = "client-id";
  public static final String DEFAULT_MDC_CLIENT_ID_VALUE = "defaultClientId";

  @NoArgsConstructor(access = AccessLevel.PRIVATE)
  public static final class LogConstants {
    public static final String REQUEST = "RequestMessage";
    public static final String RESPONSE = "ResponseMessage";
    public static final String METHOD_TYPE = "MethodType";
    public static final String RESOURCE = "Resource";
    public static final String START_TIME = "start-time";
    public static final String START_TIMESTAMP = "start-timestamp";
  }
}
