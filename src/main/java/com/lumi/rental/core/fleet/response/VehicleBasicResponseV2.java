package com.lumi.rental.core.fleet.response;

import static lombok.AccessLevel.PRIVATE;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.lumi.rental.core.fleet.dto.VehicleFinancialDTO;
import com.lumi.rental.core.fleet.dto.VehicleInspectionDTO;
import com.lumi.rental.core.fleet.dto.VehicleOperationDTO;
import java.io.Serial;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VehicleBasicResponseV2 implements Serializable {

  @Serial private static final long serialVersionUID = -1L;

  String plateNo;
  String plateNoAr;
  VehicleModelResponseV2 model;
  Integer modelYear;
  String color;
  String chassisNo;
  String assetId;
  String policyNo;
  Boolean active;
  VehicleFinancialDTO vehicleFinancialDTO;
  VehicleOperationDTO vehicleOperationDTO;
  VehicleInspectionDTO vehicleInspectionDTO;
}
