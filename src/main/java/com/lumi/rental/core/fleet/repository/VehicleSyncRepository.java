package com.lumi.rental.core.fleet.repository;

import com.lumi.rental.core.fleet.entity.VehicleSyncEntity;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface VehicleSyncRepository extends JpaRepository<VehicleSyncEntity, Integer> {

  Optional<VehicleSyncEntity> findByPlateNo(String plateNo);

  List<VehicleSyncEntity> findByPlateNoArIsNull();

  @Query("SELECT v FROM VehicleSyncEntity v WHERE v.chassisNo IS NOT NULL AND v.year IS NULL")
  List<VehicleSyncEntity> findVehiclesWithChassisNoButNoModelYear();

  @Query("SELECT COUNT(v) FROM VehicleSyncEntity v WHERE v.modelId = :id")
  int countByModelId(Integer id);
}
