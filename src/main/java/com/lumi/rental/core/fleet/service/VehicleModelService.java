package com.lumi.rental.core.fleet.service;

import com.lumi.rental.core.fleet.mapper.VehicleModelMapper;
import com.lumi.rental.core.fleet.repository.VehicleModelRepository;
import com.lumi.rental.core.fleet.response.VehicleModelBasicResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleModelService {

  private final VehicleModelMapper vehicleModelMapper;
  private final VehicleModelRepository modelRepository;

  public Page<VehicleModelBasicResponse> getAllVehicleModel(PageRequest pageable) {
    return modelRepository
        .findAll(pageable)
        .map(model -> vehicleModelMapper.buildVehicleModelBasicResponse(model, null));
  }
}
