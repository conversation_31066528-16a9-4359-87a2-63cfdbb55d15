package com.lumi.rental.core.fleet.controller;

import static org.springframework.http.ResponseEntity.ok;

import com.lumi.rental.core.fleet.enums.FuelLevel;
import com.lumi.rental.core.fleet.service.MaintenanceLogService;
import com.seera.lumi.core.fleet.dto.MaintenanceLogDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller for managing vehicle maintenance records. Provides endpoints for accessing
 * maintenance history and related operations.
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/v2/fleet/maintenance")
public class MaintenanceControllerV2 {
  private static final Logger log = LoggerFactory.getLogger("application");
  private final MaintenanceLogService maintenanceLogService;

  @GetMapping("/fuel-level")
  public ResponseEntity<?> fuelLevelReasons() {
    log.info("Received request to fetch fuel level");
    List<Map<String, Object>> levels =
        Arrays.stream(FuelLevel.values())
            .map(
                level ->
                    Map.<String, Object>of(
                        "value", level.getValue(), "desc", level.getDescription()))
            .collect(Collectors.toList());
    return ok(levels);
  }

  /**
   * Retrieves all maintenance logs for a specific vehicle
   *
   * @param plateNo Vehicle license plate number (case-insensitive)
   * @return List of maintenance logs with 200 OK status
   */
  @Operation(
      summary = "Get maintenance logs by plate number",
      description =
          "Retrieves complete maintenance history for a vehicle using its license plate number",
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved maintenance records"),
        @ApiResponse(responseCode = "400", description = "Invalid plate number format"),
        @ApiResponse(responseCode = "404", description = "Vehicle not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
      })
  @GetMapping("/logs")
  public ResponseEntity<List<MaintenanceLogDTO>> getMaintenanceLogsByPlate(
      @Parameter(
              description = "Vehicle license plate number",
              example = "1234 ABC",
              required = true)
          @RequestParam
          String plateNo) {

    log.info("Received request for maintenance logs - Plate: {}", plateNo);
    List<MaintenanceLogDTO> logs = maintenanceLogService.findAllMaintenanceLogs(plateNo);
    log.debug("Found {} maintenance records for plate: {}", logs.size(), plateNo);

    return ResponseEntity.ok(logs);
  }
}
