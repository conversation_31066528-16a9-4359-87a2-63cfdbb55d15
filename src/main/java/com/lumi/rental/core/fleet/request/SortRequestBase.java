package com.lumi.rental.core.fleet.request;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
public class SortRequestBase implements Serializable {
  @Serial private static final long serialVersionUID = -635323273079722183L;

  private List<String> sort = List.of("id");
  private String order = "desc";

  public String[] getSort() {
    return sort == null ? new String[] {"id"} : sort.toArray(String[]::new);
  }

  public List<String> getSortInList() {
    return sort;
  }

  @Override
  public String toString() {
    String sb =
        super.toString()
            + "SortRequestDTO{"
            + "sort='"
            + sort
            + '\''
            + ", order='"
            + order
            + '\''
            + '}'
            + super.toString();
    return sb;
  }
}
