package com.lumi.rental.core.fleet.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Inheritance(strategy = InheritanceType.JOINED)
@Table(name = "vehicle_financial_info")
public class VehicleFinancialInfo extends BaseEntity {

  @Column(name = "plate_no")
  private String plateNo;

  @Column(name = "nbv")
  private Double nbv;

  @Column(name = "adv")
  private Double adv;

  @Column private Double purchasePrice;

  @Column private LocalDateTime purchaseDate;
}
