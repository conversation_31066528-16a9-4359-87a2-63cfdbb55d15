package com.lumi.rental.core.fleet.enums;

public enum VehicleCountType {
  RUNNING_STATUS_COUNT("Running Status Count"),
  MAINTENANCE_HISTORY_COUNT("Vehicle Has Maintenance History Count"),
  HAS_KM_READING_COUNT("Has Km Reading Count"),
  SERVICED_COUNT("Serviced Count"),
  CROSSED_SPEED_LIMIT_COUNT("Crossed Speed Limit Count");

  @SuppressWarnings("unused")
  private final String value;

  VehicleCountType(String value) {
    this.value = value;
  }
}
