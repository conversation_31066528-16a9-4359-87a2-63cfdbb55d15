package com.lumi.rental.core.fleet.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VehicleGroupResponse extends VehicleGroupBaseResponse {
  private VehicleModelBasicResponse vehicleModelBasicResponse;
  private List<VehicleModelBasicResponse> models;
  private String thumbnail;
  private Boolean enabled;
}
