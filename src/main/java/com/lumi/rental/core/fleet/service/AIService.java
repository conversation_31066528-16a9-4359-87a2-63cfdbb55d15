package com.lumi.rental.core.fleet.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumi.rental.core.fleet.api.AIClient;
import com.lumi.rental.core.fleet.dto.AIGenerateResponse;
import com.lumi.rental.core.fleet.dto.Specification;
import com.lumi.rental.core.fleet.util.AIPrompts;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AIService {
  private static final Logger log = LoggerFactory.getLogger("application");
  private static final String GEMINI_MODEL = "gemini-1.5-flash";
  private final AIClient aiClient;
  private final ObjectMapper objectMapper;

  public String translateModelNameToArabic(String nameEn) {
    String prompt = AIPrompts.translateModelNameToArabicPrompt(nameEn);
    AIGenerateResponse response = aiClient.generateAIResponse(prompt);
    return response.getMessage();
  }

  public String translateMakeNameToArabic(String nameEn) {
    String prompt = AIPrompts.translateMakeNameToArabicPrompt(nameEn);
    AIGenerateResponse response = aiClient.generateAIResponse(prompt);
    return response.getMessage();
  }

  public Specification fetchAndMapGccSpecifications(String s) {
    try {
      String prompt = AIPrompts.buildPromptForFindingSpecs(s);
      AIGenerateResponse response = aiClient.generateAIResponse(prompt);
      return objectMapper.readValue(response.getMessage(), Specification.class);
    } catch (Exception e) {
      log.error("Failed to fetch and map GCC specifications", e);
    }
    return null;
  }
}
