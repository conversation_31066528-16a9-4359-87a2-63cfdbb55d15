package com.lumi.rental.core.fleet.api.carpro;

import com.lumi.rental.core.fleet.api.carpro.resp.ModelCarproResponse;
import com.lumi.rental.core.fleet.api.carpro.resp.VehicleCarproResponse;
import com.lumi.rental.core.fleet.config.feign.FeignConfig;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

@Component
@FeignClient(
    name = "CoreCarProClient",
    url = "${api.core.carpro.service.base.url}",
    dismiss404 = true,
    configuration = FeignConfig.class)
public interface CoreCarProClient {

  @GetMapping("/vehicles")
  List<VehicleCarproResponse> getVehicleInfo(@RequestParam("assetIds") List<String> assetIds);

  @GetMapping("/models")
  List<ModelCarproResponse> getAllModels();
}
