package com.lumi.rental.core.fleet.repository.es;

import com.seera.lumi.core.fleet.vo.GroupedData;
import java.time.LocalDate;
import java.util.Set;

public interface ESVehicleTrackerHistoryRepositoryCustom {

  long getVehicleStatsCrossedSpeedLimit(Set<String> plateNumbers, LocalDate fromDate);

  GroupedData getVehiclesBreachSpeedLimitNTimeFromDate(
      Set<String> plateNumbers, LocalDate fromDate, int breachLimit);
}
