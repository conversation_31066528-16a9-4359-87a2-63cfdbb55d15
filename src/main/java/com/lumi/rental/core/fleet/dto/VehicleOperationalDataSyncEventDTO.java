package com.lumi.rental.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class VehicleOperationalDataSyncEventDTO {

  private String plateNo;

  @JsonProperty("serviceType")
  private String carproServiceTypeCode;

  @JsonProperty("subServiceType")
  private Integer carproSubServiceTypeId;

  @JsonProperty("vehicleStatus")
  private Integer carproStatusId;

  @JsonProperty("currentBranch")
  private String currentLocationId;

  @JsonProperty("oosReason")
  private Integer oosReason;

  @JsonProperty("saleCycle")
  private Integer saleCycle;

  private Integer odometerReading;

  private Integer fuelLevel;
}
