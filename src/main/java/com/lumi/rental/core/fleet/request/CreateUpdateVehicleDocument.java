package com.lumi.rental.core.fleet.request;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lumi.rental.core.fleet.util.LocalDateTimeDeserializer;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class CreateUpdateVehicleDocument implements Serializable {

  private String plateNo;
  private String url;
  private Integer typeId;
  private Integer pageNo = 1;

  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  private LocalDateTime issuingDate;

  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  private LocalDateTime expiryDate;

  private Boolean internal = Boolean.FALSE;
}
