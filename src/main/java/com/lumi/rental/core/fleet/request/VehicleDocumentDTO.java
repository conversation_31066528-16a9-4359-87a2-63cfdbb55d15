package com.lumi.rental.core.fleet.request;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lumi.rental.core.fleet.util.LocalDateTimeDeserializer;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.NonNull;

@Data
public class VehicleDocumentDTO implements Serializable {

  @NonNull @NotEmpty private String plateNo;
  @NonNull @NotEmpty private String url;
  @NonNull private Integer typeId;
  private Integer pageNo = 1;

  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  private LocalDateTime issuingDate;

  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  private LocalDateTime expiryDate;

  private Boolean internal = Boolean.FALSE;
}
