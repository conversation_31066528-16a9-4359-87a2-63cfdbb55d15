package com.lumi.rental.core.fleet.service;

import static com.seera.lumi.core.fleet.constants.CacheConstants.FLEET_CACHE_PREFIX;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.PrivateKey;
import java.security.Security;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.concurrent.atomic.AtomicReference;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.io.pem.PemReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.cloudfront.CloudFrontUtilities;
import software.amazon.awssdk.services.cloudfront.model.CannedSignerRequest;

@Service
public class DocumentUrlSigner {

  private static final Logger log = LoggerFactory.getLogger("application");
  private static final AtomicReference<PrivateKey> cachedPrivateKey = new AtomicReference<>();
  private static final Object keyLock = new Object();
  //  @Value("${aws.url.expiry}")
  private final Integer urlExpirySeconds = 86400; // one day in seconds;
  @Value("${aws.pem.file.path}")
  private String pemKeyPath;
  @Value("${aws.key.pair.id}")
  private String keyPairId;

  @Cacheable(
      cacheNames = FLEET_CACHE_PREFIX + "DocumentUrlSigner",
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager",
      unless = "#result == null")
  public String sign(String url) {
    if (StringUtils.isBlank(url)) {
      log.warn("Cannot sign empty URL");
      return url;
    }
    log.debug("Starting URL signing process for URL: {}", url);
    long startTime = System.currentTimeMillis();

    String signedUrl = generate(url);

    if (StringUtils.isBlank(signedUrl)) {
      log.error("Failed to sign URL: {}", url);
      return url;
    }

    long duration = System.currentTimeMillis() - startTime;
    log.info("URL signed successfully. Original URL: {}, Duration: {}ms", url, duration);
    return signedUrl;
  }

  private String generate(String url) {
    String signedUrl = url;
    if (hasValidExpiry() && isNotEmpty(keyPairId) && isNotEmpty(pemKeyPath)) {
      try {
        log.debug("Attempting to get private key for URL signing");
        PrivateKey privateKey = getPrivateKey();
        if (privateKey == null) {
          log.error("Failed to get private key. PEM path: {}", pemKeyPath);
          return url;
        }

        Instant expirationDate =
            ZonedDateTime.now(ZoneOffset.UTC)
                .toLocalDateTime()
                .plusSeconds(urlExpirySeconds)
                .toInstant(ZoneOffset.UTC);
        log.debug(
            "Generated expiration date: {} for URL: {} with url-expiry-window: {}",
            expirationDate,
            url,
            urlExpirySeconds);

        signedUrl =
            CloudFrontUtilities.create()
                .getSignedUrlWithCannedPolicy(
                    CannedSignerRequest.builder()
                        .resourceUrl(url)
                        .privateKey(privateKey)
                        .keyPairId(keyPairId)
                        .expirationDate(expirationDate)
                        .build())
                .url();
        log.info(
            "Successfully generated signed URL with expiration: {}, url-expiry-window: {}",
            expirationDate,
            urlExpirySeconds);
      } catch (Exception e) {
        log.error("Error generating signed URL for: {}. Error: {}", url, e.getMessage(), e);
      }
    } else {
      log.warn(
          "URL signing configuration incomplete. Expiry: {}, KeyPairId: {}, PemPath: {}",
          urlExpirySeconds,
          keyPairId,
          pemKeyPath);
    }
    return signedUrl;
  }

  private PrivateKey getPrivateKey() {
    PrivateKey key = cachedPrivateKey.get();
    if (key != null) {
      log.debug("Using cached private key");
      return key;
    }

    synchronized (keyLock) {
      key = cachedPrivateKey.get();
      if (key != null) {
        return key;
      }

      log.info("Loading private key from PEM file: {}", pemKeyPath);
      long startTime = System.currentTimeMillis();

      try (InputStream inputStream = new FileInputStream(pemKeyPath);
          PemReader pemReader = new PemReader(new InputStreamReader(inputStream))) {

        byte[] content = pemReader.readPemObject().getContent();
        Security.addProvider(new BouncyCastleProvider());
        KeyFactory factory = KeyFactory.getInstance("RSA", "BC");
        key = factory.generatePrivate(new PKCS8EncodedKeySpec(content));

        cachedPrivateKey.set(key);

        long duration = System.currentTimeMillis() - startTime;
        log.info("Successfully loaded private key in {}ms", duration);
        return key;

      } catch (IOException
          | NoSuchAlgorithmException
          | NoSuchProviderException
          | InvalidKeySpecException e) {
        log.error("Failed to load private key from: {}. Error: {}", pemKeyPath, e.getMessage(), e);
        return null;
      }
    }
  }

  private Boolean hasValidExpiry() {
    boolean isValid = urlExpirySeconds > 0;
    if (!isValid) {
      log.warn("Invalid URL expiry configuration: {} seconds", urlExpirySeconds);
    }
    return isValid;
  }
}
