package com.lumi.rental.core.fleet.config;

import static org.springframework.kafka.retrytopic.RetryTopicConstants.DEFAULT_DLT_SUFFIX;

import java.util.HashMap;
import java.util.Map;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.KafkaAdmin;

@EnableKafka
@Configuration
public class KafkaTopicConfig {

  @Value("${spring.kafka.bootstrap-servers}")
  private String bootstrapAddress;

  @Value("${spring.cloud.stream.kafka.binder.minPartitionCount}")
  private Integer partition;

  @Value("${spring.cloud.stream.kafka.binder.replicationFactor}")
  private Integer replication;

  //  @Value("${kafka.topic.vehicle.document.migration}")
  //  private String vehicleDocumentMigrationTopic;
  //
  //  @Value("${kafka.topic.vehicle.tracking.raw.data}")
  //  private String vehicleLiveTrackingRawDataTopic;

  @Value("${kafka.topic.vehicle.tracking.data}")
  private String vehicleLiveTrackingDataTopic;

  //  @Value("${kafka.topic.vehicle.data}")
  //  private String vehicleDataTopic;

  @Value("${kafka.topic.vehicle.maintenance.data}")
  private String vehicleMaintenanceDataTopic;

  //  @Value("${kafka.topic.vehicle.document.metadata.data}")
  //  private String vehicleDocumentMetadataTopic;

  //  @Value("${kafka.topic.sync.vehicle.make.data}")
  //  private String synVehicleMakeDataTopic;
  //
  //  @Value("${kafka.topic.sync.vehicle.model.data}")
  //  private String synVehicleModelDataTopic;

  @Value("${kafka.topic.sync.vehicle.metadata}")
  private String syncVehicleMetaDataTopic;

  @Value("${kafka.topic.sync.vehicle.operational.data}")
  private String syncVehicleOperationalDataTopic;

  //  @Value("${kafka.topic.sync.vehicle.reservation.data}")
  //  private String syncVehicleReservationDataTopic;

  @Value("${kafka.topic.sync.vehicle.document}")
  private String syncVehicleDocumentDataTopic;

  @Value("${kafka.topic.sync.vehicle.tracking.data}")
  private String syncVehicleTrackingDataTopic;

  @Bean
  public KafkaAdmin kafkaAdmin() {
    Map<String, Object> configs = new HashMap<>();
    configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapAddress);
    return new KafkaAdmin(configs);
  }

  //  @Bean
  //  public NewTopic vehicleLiveTrackingRawDataTopic() {
  //    return createTopic(vehicleLiveTrackingRawDataTopic);
  //  }
  //
  //  @Bean
  //  public NewTopic vehicleLiveTrackingRawDataDLTTopic() {
  //    return createDltTopic(vehicleLiveTrackingRawDataTopic);
  //  }

  @Bean
  public NewTopic vehicleLiveTrackingDataTopic() {
    return createTopic(vehicleLiveTrackingDataTopic);
  }

  @Bean
  public NewTopic vehicleLiveTrackingDataDLTTopic() {
    return createDltTopic(vehicleLiveTrackingDataTopic);
  }

  //  @Bean
  //  public NewTopic vehicleDataTopic() {
  //    return createTopic(vehicleDataTopic);
  //  }

  //  @Bean
  //  public NewTopic vehicleDocumentMigrationTopic() {
  //    return createTopic(vehicleDocumentMigrationTopic);
  //  }
  //
  //  @Bean
  //  public NewTopic vehicleDocumentMigrationDLTTopic() {
  //    return createDltTopic(vehicleDocumentMigrationTopic);
  //  }

  @Bean
  public NewTopic vehicleMaintenanceDataTopic() {
    return createTopic(vehicleMaintenanceDataTopic);
  }

  //  @Bean
  //  public NewTopic vehicleDocumentMetadataTopic() {
  //    return createTopic(vehicleDocumentMetadataTopic);
  //  }
  //
  //  @Bean
  //  public NewTopic vehicleDocumentMetadataDLTTopic() {
  //    return createDltTopic(vehicleDocumentMetadataTopic);
  //  }
  //
  //  @Bean
  //  public NewTopic synVehicleMakeDataTopic() {
  //    return createTopic(synVehicleMakeDataTopic);
  //  }
  //
  //  @Bean
  //  public NewTopic synVehicleMakeDataDLTTopic() {
  //    return createDltTopic(synVehicleMakeDataTopic);
  //  }
  //
  //  @Bean
  //  public NewTopic synVehicleModelDataTopic() {
  //    return createTopic(synVehicleModelDataTopic);
  //  }
  //
  //  @Bean
  //  public NewTopic synVehicleModelDataDLTTopic() {
  //    return createDltTopic(synVehicleModelDataTopic);
  //  }

  @Bean
  public NewTopic syncVehicleMetaDataTopic() {
    return createTopic(syncVehicleMetaDataTopic);
  }

  @Bean
  public NewTopic syncVehicleMetaDataDLTTopic() {
    return createDltTopic(syncVehicleMetaDataTopic);
  }

  @Bean
  public NewTopic syncVehicleOperationalDataTopic() {
    return createTopic(syncVehicleOperationalDataTopic);
  }

  @Bean
  public NewTopic syncVehicleOperationalDataDLTTopic() {
    return createDltTopic(syncVehicleOperationalDataTopic);
  }

  //  @Bean
  //  public NewTopic syncVehicleReservationDataTopic() {
  //    return createTopic(syncVehicleReservationDataTopic);
  //  }
  //
  //  @Bean
  //  public NewTopic syncVehicleReservationDataDLTTopic() {
  //    return createDltTopic(syncVehicleReservationDataTopic);
  //  }

  @Bean
  public NewTopic syncVehicleDocumentDataTopic() {
    return createTopic(syncVehicleDocumentDataTopic);
  }

  @Bean
  public NewTopic syncVehicleDocumentDataDLTTopic() {
    return createDltTopic(syncVehicleDocumentDataTopic);
  }

  @Bean
  public NewTopic syncVehicleTrackingDataTopic() {
    return createTopic(syncVehicleTrackingDataTopic);
  }

  @Bean
  public NewTopic syncVehicleTrackingDataDLTTopic() {
    return createDltTopic(syncVehicleTrackingDataTopic);
  }

  private NewTopic createTopic(String name) {
    return TopicBuilder.name(name).partitions(partition).replicas(replication).build();
  }

  private NewTopic createDltTopic(String name) {
    return createTopic(name + DEFAULT_DLT_SUFFIX);
  }
}
