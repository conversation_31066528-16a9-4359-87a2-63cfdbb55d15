package com.lumi.rental.core.fleet.repository;

import com.lumi.rental.core.fleet.entity.DocumentType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface DocumentTypeRepository
    extends JpaRepository<DocumentType, Integer>, JpaSpecificationExecutor<DocumentType> {

  @EntityGraph("document-type-all-relation")
  Page<DocumentType> findAll(Specification<DocumentType> spec, Pageable pageable);
}
