package com.lumi.rental.core.fleet.service;

import static com.seera.lumi.core.fleet.constants.CacheConstants.FLEET_CACHE_PREFIX;

import com.lumi.rental.core.fleet.dto.VehicleOperationDTO;
import com.lumi.rental.core.fleet.entity.Vehicle;
import com.lumi.rental.core.fleet.entity.VehicleFinancialInfo;
import com.lumi.rental.core.fleet.entity.VehicleOperationalData;
import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.mapper.OperationalMapper;
import com.lumi.rental.core.fleet.mapper.VehicleMetaDataMapper;
import com.lumi.rental.core.fleet.repository.InspectionRepository;
import com.lumi.rental.core.fleet.repository.VehicleFinancialInfoRepository;
import com.lumi.rental.core.fleet.repository.VehicleOperationalDataRepository;
import com.lumi.rental.core.fleet.repository.VehicleRepository;
import com.lumi.rental.core.fleet.response.VehicleBasicResponseV2;
import com.lumi.rental.core.fleet.response.VehicleGroupBaseResponse;
import com.lumi.rental.core.fleet.response.VehicleOperationResponse;
import com.lumi.rental.core.fleet.response.VehicleResponseV2;
import com.lumi.rental.core.fleet.util.DateUtil;
import com.seera.lumi.core.fleet.api.CarProClient;
import com.seera.lumi.core.fleet.dto.VehicleLiveTrackingEventDTO;
import com.seera.lumi.core.fleet.dto.VehicleStatusResponse;
import com.seera.lumi.core.fleet.exception.VehicleErrors;
import com.seera.lumi.core.fleet.mapper.VehicleResponseMapper;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleMetadataService {

  private static final Logger log = LoggerFactory.getLogger("application");
  private final VehicleResponseMapper vehicleResponseMapper;
  private final VehicleMetaDataMapper vehicleMetaDataMapper;
  private final OperationalMapper operationalMapper;

  private final CarProClient carProClient;
  private final BranchInfoCache branchInfoCache;
  private final VehicleTrackerService vehicleTrackingService;
  private final VehicleGroupService groupService;
  private final InspectionRepository inspectionRepository;

  private final VehicleRepository vehicleRepository;
  private final VehicleFinancialInfoRepository vehicleFinancialInfoRepository;
  private final VehicleOperationalDataRepository operationalDataRepository;

  public VehicleResponseV2 getVehicleByPlateNumberWithAgreementStatus(
      String plateNumber, boolean requireAgreementDetails) {
    Vehicle vehicle =
        vehicleRepository
            .findByPlateNo(plateNumber)
            .orElseThrow(() -> new BusinessException(VehicleErrors.VEHICLE_NOT_FOUND));

    VehicleFinancialInfo vehicleFinancialInfo =
        vehicleFinancialInfoRepository.findByPlateNo(plateNumber).orElse(null);
    VehicleLiveTrackingEventDTO liveTrackingEventDTO = null;
    //        vehicleTrackingService.getLatestVehicleTrackingInfo(plateNumber);
    VehicleStatusResponse vehicleStatusResponse = null;
    if (requireAgreementDetails) {
      vehicleStatusResponse = carProClient.getVehicleStatusData(plateNumber);
    }
    VehicleResponseV2 responseV2 =
        vehicleResponseMapper.toResponseV2(
            vehicle, liveTrackingEventDTO, vehicleFinancialInfo, vehicleStatusResponse);
    operationalDataRepository
        .findByPlateNo(plateNumber)
        .ifPresent(
            vehicleOperationalData ->
                responseV2.setCurrentBranchId(vehicleOperationalData.getCurrentLocationId()));
    return responseV2;
  }

  //  public VehicleOperationResponse getVehicle(
  //      String plateNo, boolean requireOpsData, boolean requireMaintenanceData) {
  //    Vehicle vehicle =
  //        vehicleRepository
  //            .findByPlateNo(plateNo)
  //            .orElseThrow(() -> new BusinessException(BaseError.NOT_FOUND, "Vehicle"));
  //
  //    VehicleOperationResponse response = vehicleMetaDataMapper.buildVehicleResponse(vehicle);
  //    String groupCode = vehicle.getModel().getVehicleGroup().getCode();
  //    VehicleGroupBaseResponse groupResponse =
  // groupService.vehicleGroupBaseResponseByCode(groupCode);
  //    response.getModel().setGroupResponse(groupResponse);
  //
  //    if (requireOpsData) {
  //      operationalDataRepository
  //          .findByPlateNo(plateNo)
  //          .ifPresent(
  //              vehicleOperationalData ->
  //                  operationalMapper.populate(response, vehicleOperationalData));
  //    }
  //    return response;
  //  }

  public VehicleBasicResponseV2 getVehicleV2(
      String plateNo,
      boolean requireOpsData,
      boolean requireFinancialData,
      boolean requireInspectionData) {
    VehicleBasicResponseV2 response = getVehicleBasicResponse(plateNo);

    if (requireFinancialData) {
      vehicleFinancialInfoRepository
          .findByPlateNo(plateNo)
          .ifPresent(
              record -> {
                response.setVehicleFinancialDTO(
                    vehicleMetaDataMapper.buildVehicleFinancialDTO(record));
              });
    }
    if (requireOpsData) {
      operationalDataRepository
          .findByPlateNo(plateNo)
          .ifPresent(
              record -> {
                VehicleOperationDTO operationDTO =
                    vehicleMetaDataMapper.buildVehicleOperationDTO(record);
                if (ObjectUtils.isNotEmpty(operationDTO.getOwnerBranchId()))
                  operationDTO.setOwnerBranch(
                      branchInfoCache.getBranchCode(operationDTO.getOwnerBranchId()));
                if (ObjectUtils.isNotEmpty(operationDTO.getCurrentLocationId()))
                  operationDTO.setCurrentLocation(
                      branchInfoCache.getBranchCode(operationDTO.getCurrentLocationId()));
                response.setVehicleOperationDTO(operationDTO);
              });
    }
    if (requireInspectionData) {
      inspectionRepository
          .findFirstByPlateNoOrderByCreatedOnDesc(plateNo)
          .ifPresent(
              inspectionReport ->
                  response.setVehicleInspectionDTO(
                      vehicleMetaDataMapper.buildVehicleInspectionDTO(inspectionReport)));
    }
    return response;
  }

  @Cacheable(
      cacheNames = FLEET_CACHE_PREFIX + "VEHICLE::getVehicleBasicResponse",
      keyGenerator = "customKeyGenerator",
      cacheManager = "oneDayCacheManager")
  public VehicleBasicResponseV2 getVehicleBasicResponse(String plateNo) {
    Vehicle vehicle =
        vehicleRepository
            .findByPlateNo(plateNo)
            .orElseThrow(() -> new BusinessException(VehicleErrors.VEHICLE_NOT_FOUND));
    return vehicleMetaDataMapper.buildVehicleBasicResponseV2(vehicle);
  }

  public List<VehicleBasicResponseV2> getVehiclesV2(
      List<String> plateNo, boolean requireOpsData, boolean requireFinancialData) {
    List<VehicleBasicResponseV2> vehicleResponse =
        vehicleRepository.findAllByPlateNoIn(plateNo).stream()
            .map(vehicleMetaDataMapper::buildVehicleBasicResponseV2)
            .toList();

    if (requireFinancialData) {
      Map<String, VehicleFinancialInfo> vehicleFinancialInfoMap =
          vehicleFinancialInfoRepository.findAllByPlateNoIn(plateNo).stream()
              .collect(Collectors.toMap(VehicleFinancialInfo::getPlateNo, Function.identity()));
      vehicleResponse.forEach(
          response -> {
            VehicleFinancialInfo financialInfo = vehicleFinancialInfoMap.get(response.getPlateNo());
            response.setVehicleFinancialDTO(
                vehicleMetaDataMapper.buildVehicleFinancialDTO(financialInfo));
          });
    }

    if (requireOpsData) {
      Map<String, VehicleOperationalData> vehicleOperationalDataMap =
          operationalDataRepository.findAllByPlateNoIn(plateNo).stream()
              .collect(Collectors.toMap(VehicleOperationalData::getPlateNo, Function.identity()));
      vehicleResponse.forEach(
          response -> {
            VehicleOperationalData operationalData =
                vehicleOperationalDataMap.get(response.getPlateNo());
            response.setVehicleOperationDTO(
                vehicleMetaDataMapper.buildVehicleOperationDTO(operationalData));
          });
    }
    return vehicleResponse;
  }

  public VehicleOperationResponse getVehicleV3(
      String plateNo, boolean requireOpsData, boolean requireMaintenanceData) {
    Vehicle vehicle =
        vehicleRepository
            .findByPlateNo(plateNo)
            .orElseThrow(() -> new BusinessException(BaseError.NOT_FOUND, "Vehicle"));

    VehicleOperationResponse response = vehicleMetaDataMapper.buildVehicleResponse(vehicle);
    populateGroupResponse(response, vehicle);
    inspectionRepository
        .findFirstByPlateNoOrderByCreatedOnDesc(plateNo)
        .ifPresent(
            inspectionReport -> {
              response.setLastInspected(
                  DateUtil.convertLocalDateToEpoch(inspectionReport.getCreatedOn()));
            });
    if (requireOpsData) {
      operationalDataRepository
          .findByPlateNo(plateNo)
          .ifPresent(
              vehicleOperationalData ->
                  operationalMapper.populate(response, vehicleOperationalData));
    }
    return response;
  }

  private void populateGroupResponse(VehicleOperationResponse response, Vehicle vehicle) {
    try {
      String groupCode = vehicle.getModel().getVehicleGroup().getCode();
      VehicleGroupBaseResponse groupResponse =
          groupService.vehicleGroupBaseResponseByCode(groupCode);
      response.getModel().setGroupResponse(groupResponse);
    } catch (Exception ex) {
      log.error(
          "Error while populating vehicle group for vehicle {}",
          vehicle.getPlateNo() + ex.getMessage());
    }
  }

  public Map<String, Vehicle> getVehicleMap(Set<String> plateNos) {
    return vehicleRepository.findByPlateNos(plateNos).stream()
        .collect(Collectors.toMap(Vehicle::getPlateNo, Function.identity()));
  }
}
