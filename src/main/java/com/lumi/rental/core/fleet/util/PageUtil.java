package com.lumi.rental.core.fleet.util;

import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

import com.lumi.rental.core.fleet.request.BasePageRequest;
import com.seera.lumi.core.fleet.dto.SortRequestDTO;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

public final class PageUtil {

  private PageUtil() {}

  public static Pageable getPageable(SortRequestDTO searchReq) {
    int pageNumber = searchReq.getPageNumber() == null ? 0 : searchReq.getPageNumber();
    Sort.Direction direction =
        nonNull(searchReq.getOrder()) && searchReq.getOrder().equalsIgnoreCase("asc")
            ? Sort.Direction.ASC
            : Sort.Direction.DESC;
    Sort sort =
        isNotEmpty(searchReq.getSort())
            ? Sort.by(direction, searchReq.getSort())
            : Sort.by(direction, "id");
    return PageRequest.of(pageNumber, searchReq.getPageSize(), sort);
  }

  public static Pageable getPageableWithoutSort(SortRequestDTO searchReq) {
    int pageNumber = searchReq.getPageNumber() == null ? 0 : searchReq.getPageNumber();
    return PageRequest.of(pageNumber, searchReq.getPageSize(), Sort.unsorted());
  }

  public static Pageable getPageable(BasePageRequest searchReq) {
    int pageNumber = searchReq.getPageNumber() == null ? 0 : searchReq.getPageNumber();
    Sort.Direction direction =
        nonNull(searchReq.getOrder()) && searchReq.getOrder().equalsIgnoreCase("asc")
            ? Sort.Direction.ASC
            : Sort.Direction.DESC;
    Sort sort =
        isNotEmpty(searchReq.getSort())
            ? Sort.by(direction, searchReq.getSort())
            : Sort.by(direction, "id");
    return PageRequest.of(pageNumber, searchReq.getPageSize(), sort);
  }

  public static Pageable getPageableWithoutSort(BasePageRequest request) {
    int pageNumber = request.getPageNumber() == null ? 0 : request.getPageNumber();
    return PageRequest.of(pageNumber, request.getPageSize(), Sort.unsorted());
  }
}
