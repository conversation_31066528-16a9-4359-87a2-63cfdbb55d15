package com.lumi.rental.core.fleet.repository;

import com.lumi.rental.core.fleet.entity.Vehicle;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface VehicleRepository extends JpaRepository<Vehicle, Integer> {

  @EntityGraph("vehicle-all-relation-graph")
  @Query("SELECT p FROM Vehicle p where p.plateNo in (:plateNos%)")
  List<Vehicle> findByPlateNos(Set<String> plateNos);

  @Query(
      """
        SELECT v.assetId FROM Vehicle v
        WHERE v.chassisNo IS NULL OR v.policyNo IS NULL
    """)
  List<String> findVehiclesWithoutChassisNoOrPolicyNo();

  @Query(
      """
            SELECT v FROM Vehicle v
            LEFT JOIN v.model m
            WHERE m.id IS NULL
        """)
  List<Vehicle> findVehiclesWithoutModel();

  Optional<Vehicle> findByPlateNo(String plateNo);

  @Query(
      "SELECT DISTINCT p.plateNo FROM Vehicle p where p.plateNo like :plateNo% order by p.plateNo")
  Page<String> getVehicleSuggestions(@Param("plateNo") String plateNo, Pageable pageable);

  @EntityGraph("vehicle-all-relation-graph")
  Page<Vehicle> findAll(Specification<Vehicle> spec, Pageable pageable);

  @EntityGraph("vehicle-all-relation-graph")
  List<Vehicle> findAll(Specification<Vehicle> spec, Sort sort);

  @EntityGraph("vehicle-all-relation-graph")
  List<Vehicle> findAllByPlateNoIn(List<String> plateNos);

  @EntityGraph("vehicle-all-relation-graph")
  List<Vehicle> findAllByAssetIdIn(List<String> assetIds);
}
