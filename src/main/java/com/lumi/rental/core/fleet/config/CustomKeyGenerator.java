package com.lumi.rental.core.fleet.config;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;

public class CustomKeyGenerator implements KeyGenerator {

  private static final Logger log = LoggerFactory.getLogger("application");

  public Object generate(Object target, Method method, Object... params) {
    String s =
        "v3"
            + target.getClass().getSimpleName()
            + ":"
            + method.getName()
            + ":"
            + StringUtils.arrayToDelimitedString(params, ":");
    log.info(s);
    return s;
  }
}
