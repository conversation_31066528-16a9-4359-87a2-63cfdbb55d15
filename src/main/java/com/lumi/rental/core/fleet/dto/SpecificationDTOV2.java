package com.lumi.rental.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SpecificationDTOV2 implements Serializable {

  @Serial private static final long serialVersionUID = -1L;

  private Integer seatingCapacity;
  private Integer doors;
  private Integer bootSpace;
  private Integer luggageCountBig;
  private Integer luggageCountMedium;
  private Integer luggageCountSmall;
  private String transmission;
  private String transmissionType;
  private Integer engineSize;
  private Integer horsepower;
  private String fuelType;
  private Integer fuelCapacity;
}
