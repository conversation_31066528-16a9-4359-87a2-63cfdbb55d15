package com.lumi.rental.core.fleet.listener.es;

import static org.springframework.kafka.support.KafkaHeaders.*;

import com.lumi.rental.core.fleet.service.MaintenanceLogService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.common.utils.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.DltHandler;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleMaintenanceDataEventListener {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final MaintenanceLogService maintenanceLogService;

  @KafkaListener(
      topics = {"${kafka.topic.vehicle.maintenance.data}"},
      groupId = "sync-vehicle-maintenance-es",
      concurrency = "${kafka.listen.concurrency}",
      containerFactory = "kafkaBatchListenerContainerFactory",
      autoStartup = "${kafka.listen.auto.start:true}")
  public void listen(@Payload List<Bytes> eventList) {
    try {
      log.info(
          "Total {} Message received for SyncVehicleMaintenanceData sync-vehicle-maintenance-es",
          eventList.size());
      maintenanceLogService.syncVehicleMaintenanceDataInOpenSearchDataSource(eventList);
    } catch (Exception ex) {
      log.error(
          "Error while listening SyncVehicleMaintenanceData sync-vehicle-maintenance-es batch-event",
          ex);
    }
  }

  @DltHandler
  public void listenToDLT(
      @Header(ORIGINAL_TOPIC) String originalTopic,
      @Header(RECEIVED_KEY) String receivedKey,
      @Header(EXCEPTION_MESSAGE) String exceptionMessage,
      @Header(EXCEPTION_STACKTRACE) String exceptionStackTrace) {
    log.info(
        "Unable to process message on topic:{} for messageKey:{} , errorMessage:{}, exceptionStacktrace:{}",
        originalTopic,
        receivedKey,
        exceptionMessage,
        exceptionStackTrace);
  }
}
