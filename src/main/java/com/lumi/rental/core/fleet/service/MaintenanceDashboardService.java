package com.lumi.rental.core.fleet.service;

import com.lumi.rental.core.fleet.repository.es.ESMaintenanceLogRepository;
import java.time.LocalDate;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MaintenanceDashboardService {

  private final ESMaintenanceLogRepository esRepository;

  public long countVehicleHasMaintenanceHistory(Set<String> plateNumbers) {
    return esRepository.countDistinctPlateNoHasMaintenanceHistory(plateNumbers);
  }

  public long countVehicleServicedInNoOfMonth(Set<String> plateNumbers, int noOfMonth) {
    LocalDate currentDate = LocalDate.now();
    LocalDate fromDate =
        LocalDate.of(currentDate.getYear(), currentDate.getMonth(), 1).minusMonths(noOfMonth);
    LocalDate toDate = fromDate.plusMonths(noOfMonth);
    return esRepository.countVehicleServicedInDateRange(plateNumbers, fromDate, toDate);
  }
}
