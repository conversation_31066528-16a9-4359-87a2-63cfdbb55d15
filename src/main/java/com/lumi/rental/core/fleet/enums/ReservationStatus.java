package com.lumi.rental.core.fleet.enums;

import lombok.Getter;

@Getter
public enum ReservationStatus {
  HOLD(1, "HOLD"),
  BOOKED(2, "BOOKED"),
  EXTENDED(3, "EXTENDED"),
  COMPLETED(4, "COMPLETED"),
  CANCELLED(5, "CANCELLED");

  private final Integer id;
  private final String code;

  ReservationStatus(Integer id, String code) {
    this.id = id;
    this.code = code;
  }

  public static ReservationStatus fromId(Integer id) {
    for (ReservationStatus type : values()) {
      if (type.id.equals(id)) {
        return type;
      }
    }
    return null;
  }

  public static ReservationStatus fromCode(String code) {
    for (ReservationStatus type : values()) {
      if (type.code.equalsIgnoreCase(code)) {
        return type;
      }
    }
    return null;
  }
}
