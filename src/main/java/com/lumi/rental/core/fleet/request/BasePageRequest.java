package com.lumi.rental.core.fleet.request;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BasePageRequest extends SortRequestBase {

  @Serial private static final long serialVersionUID = -600126160341757685L;

  @JsonAlias({"page"})
  private Integer pageNumber = 0;

  @JsonAlias({"size"})
  private Integer pageSize = 10;

  @Override
  public String toString() {
    return "PageRequestDTO{" + "page=" + pageNumber + ", size=" + pageSize + '}';
  }
}
