package com.lumi.rental.core.fleet.repository.es;

import com.lumi.rental.core.fleet.entity.es.ESMaintenanceLog;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

public interface ESMaintenanceLogRepositoryCustom {

  List<ESMaintenanceLog> findByPlateNo(String plateNo);

  long countDistinctPlateNoHasMaintenanceHistory(Set<String> platNos);

  long countVehicleServicedInDateRange(
      Set<String> plateNumbers, LocalDate fromDate, LocalDate toDate);

  List<ESMaintenanceLog> getLastMaintenanceLogs(Set<String> plateNumbers, String serviceType);

  long findByPlateNoAndDateAndServiceType(String plateNo, LocalDate date, String serviceType);
}
