package com.lumi.rental.core.fleet.listener.es;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumi.rental.core.fleet.entity.VehicleOperationalData;
import com.lumi.rental.core.fleet.entity.es.ESVehicleTrackingInfoHistory;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.mapper.VehicleTrackerMapper;
import com.lumi.rental.core.fleet.service.VehicleAvailabilityService;
import com.lumi.rental.core.fleet.service.VehicleTrackerService;
import com.seera.lumi.core.fleet.dto.VehicleLiveTrackingEventDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.common.utils.Bytes;
import org.opensearch.common.geo.GeoPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleLiveTrackingDataEventListener {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final VehicleTrackerService vehicleTrackerService;
  private final VehicleTrackerMapper vehicleTrackerMapper;
  private final VehicleAvailabilityService availabilityService;
  private final ObjectMapper objectMapper;

  @KafkaListener(
      topics = {"${kafka.topic.vehicle.tracking.data}"},
      groupId = "sync-tracking-data-es",
      containerFactory = "kafkaBatchListenerContainerFactory",
      autoStartup = "${kafka.listen.auto.start:true}")
  public void listen(@Payload List<Bytes> eventList) {
    try {
      log.info("Total {} Message received", eventList.size());
      List<ESVehicleTrackingInfoHistory> vehicleTrackingInfoHistoryList = new ArrayList<>();
      List<VehicleOperationalData> vehicleOperationalDataList = new ArrayList<>();

      for (Bytes event : eventList) {
        VehicleLiveTrackingEventDTO trackingEventDTO =
            objectMapper.readValue(event.get(), VehicleLiveTrackingEventDTO.class);
        vehicleOperationalDataList.add(updateOdometerReading(trackingEventDTO));
        vehicleTrackingInfoHistoryList.add(buildVehicleTrackingInfoHistory(trackingEventDTO));
      }
      updateVehicleOperationalData(vehicleOperationalDataList);
      vehicleTrackerService.saveVehicleTrackerInfoHistory(vehicleTrackingInfoHistoryList);
    } catch (Exception ex) {
      log.error("Error while listening VehicleLiveTrackingEvent batch-event", ex);
    }
  }

  private VehicleOperationalData updateOdometerReading(
      VehicleLiveTrackingEventDTO trackingEventDTO) {
    try {
      String plateNo = trackingEventDTO.getPlateNo();
      VehicleOperationalData opsData = availabilityService.getVehicleOperationalData(plateNo);
      Integer newOdometerReading = Math.toIntExact(trackingEventDTO.getMileage() / 1000);
      if (newOdometerReading.compareTo(opsData.getOdometerReading()) > 0) {
        opsData.setOdometerReading(newOdometerReading);
        return opsData;
      }
    } catch (BusinessException ex) {
      log.error(
          "Error {}, while updating Vehicle odometer reading for {} ",
          ex.getError().getDesc(),
          trackingEventDTO);
    } catch (Exception ex) {
      log.error(
          "Unknown Error {} while updating Vehicle odometer reading for {} ",
          ex.getMessage(),
          trackingEventDTO);
    }
    return null;
  }

  private void updateVehicleOperationalData(List<VehicleOperationalData> operationalDataList) {
    List<VehicleOperationalData> opsData =
        operationalDataList.stream().filter(Objects::nonNull).toList();
    availabilityService.updateVehicleOperationDataList(opsData);
  }

  private ESVehicleTrackingInfoHistory buildVehicleTrackingInfoHistory(
      VehicleLiveTrackingEventDTO trackingEventDTO) {
    ESVehicleTrackingInfoHistory vehicleTrackingInfoHistory =
        vehicleTrackerMapper.mapVehicleTrackingInfoHistory(trackingEventDTO);
    vehicleTrackingInfoHistory.setLocation(
        new GeoPoint(trackingEventDTO.getLatitude(), trackingEventDTO.getLongitude()));
    return vehicleTrackingInfoHistory;
  }
}
