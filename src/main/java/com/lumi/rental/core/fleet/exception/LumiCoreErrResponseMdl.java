package com.lumi.rental.core.fleet.exception;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
public class LumiCoreErrResponseMdl {

  String code;
  String desc;
  String reqId;
  String path;
  String st;
}
