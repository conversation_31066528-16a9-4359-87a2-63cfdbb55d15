package com.lumi.rental.core.fleet.repository.es;

import com.seera.lumi.core.fleet.entity.opensearch.ESVehicleData;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.annotations.Query;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;

public interface ESVehicleRepository
    extends ElasticsearchRepository<ESVehicleData, String>, ESVehicleRepositoryCustom {

  Optional<ESVehicleData> findByPlateNo(String plateNo);

  @Query(
      "{\"bool\":{\"should\":[{\"bool\":{\"must\":[{\"bool\":{\"should\":[{\"range\":{\"purchaseDate\":{\"lte\":\"?0\"}}},{\"range\":{\"km\":{\"gte\":\"?1\"}}}]}},{\"bool\":{\"must_not\":[{\"exists\":{\"field\":\"lastMaintenance\"}}]}},{\"bool\":{\"must\":[{\"exists\":{\"field\":\"purchaseDate\"}}]}}]}},{\"bool\":{\"must\":[{\"bool\":{\"should\":[{\"range\":{\"lastMaintenance.date\":{\"lte\":\"?0\"}}},{\"script\":{\"script\":\"doc['km'].size()!=0 && doc['lastMaintenance.km'].size()!=0 && doc['km'].value >=  doc['lastMaintenance.km'].value + ?1\"}}]}},{\"bool\":{\"must_not\":[{\"exists\":{\"field\":\"lastMaintenance.notification.date\"}}]}},{\"bool\":{\"must\":[{\"exists\":{\"field\":\"lastMaintenance\"}}]}}]}},{\"bool\":{\"must\":[{\"bool\":{\"should\":[{\"range\":{\"lastMaintenance.notification.date\":{\"lte\":\"?0\"}}},{\"bool\":{\"must\":[{\"script\":{\"script\":\"doc['km'].size()!=0 && doc['lastMaintenance.km'].size()!=0 && doc['km'].value >=  doc['lastMaintenance.notification.km'].value + ?1\"}},{\"exists\":{\"field\":\"lastMaintenance.notification.km\"}}]}}]}},{\"bool\":{\"must\":[{\"exists\":{\"field\":\"lastMaintenance.notification.date\"}}]}}]}}]}}")
  Page<ESVehicleData> findMaintenanceDueVehicles(String date, long km, Pageable pageable);
}
