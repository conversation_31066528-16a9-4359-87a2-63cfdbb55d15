package com.lumi.rental.core.fleet.dto;

import com.lumi.rental.core.fleet.util.DateUtil;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
public class VehicleStatusInfo implements Serializable {
  private Integer statusId;
  private Integer subStatusId;
  private Long date;

  public VehicleStatusInfo(Integer statusId) {
    this.statusId = statusId;
    this.date = DateUtil.convertLocalDateToEpoch(LocalDateTime.now());
  }

  public VehicleStatusInfo(Integer statusId, Integer subStatusId) {
    this.statusId = statusId;
    this.subStatusId = subStatusId;
    this.date = DateUtil.convertLocalDateToEpoch(LocalDateTime.now());
  }
}
