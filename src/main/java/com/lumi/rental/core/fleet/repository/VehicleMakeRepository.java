package com.lumi.rental.core.fleet.repository;

import com.lumi.rental.core.fleet.dto.CountVO;
import com.lumi.rental.core.fleet.entity.VehicleMake;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface VehicleMakeRepository extends JpaRepository<VehicleMake, Integer> {

  Optional<VehicleMake> findByNameEn(String name);

  @Query(
      """
            select new com.lumi.rental.core.fleet.dto.CountVO( vm.make.id , count(v)) from Vehicle v
            LEFT JOIN VehicleModel vm on v.model.id = vm.id
            group by vm.make.id
        """)
  List<CountVO> findVehicleCountByMake();

  @Query("SELECT vm FROM VehicleMake vm where vm.nameAr is null and vm.nameEn is not null")
  List<VehicleMake> findAllMakesWithEnglishNameAndWithoutArabicName();

  Optional<VehicleMake> findByCarproMakeId(Integer makeId);
}
