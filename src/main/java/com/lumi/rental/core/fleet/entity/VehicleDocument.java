package com.lumi.rental.core.fleet.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Entity
@Accessors(chain = true)
@Inheritance(strategy = InheritanceType.JOINED)
@Table(name = "vehicle_document")
public class VehicleDocument extends BaseEntity {

  @Column(name = "plate_no")
  private String plateNo;

  @Column(name = "url")
  private String url;

  @Column(name = "extension")
  private String extension;

  @Column(name = "issuing_date")
  private LocalDateTime issuingDate;

  @Column(name = "expiry_date")
  private LocalDateTime expiryDate;

  @Column(name = "id_no")
  private String idNo;

  @JsonIgnoreProperties(
      value = {"name"},
      allowSetters = true)
  @OneToOne
  private DocumentType type;

  @Column(name = "page_no")
  private Integer pageNo;

  @Column(name = "internal")
  private Boolean internal;
}
