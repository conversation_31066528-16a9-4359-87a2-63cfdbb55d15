package com.lumi.rental.core.fleet.action.handler;

import static com.lumi.rental.core.fleet.enums.VehicleCountType.SERVICED_COUNT;

import com.lumi.rental.core.fleet.action.CountVehicleHandler;
import com.lumi.rental.core.fleet.enums.VehicleCountType;
import com.lumi.rental.core.fleet.request.VehicleCountRequest;
import com.lumi.rental.core.fleet.response.VehicleCountResponse;
import com.lumi.rental.core.fleet.service.MaintenanceDashboardService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CountServicedVehicleHandler implements CountVehicleHandler {

  private final MaintenanceDashboardService maintenanceDashboardService;

  @Override
  public VehicleCountResponse execute(VehicleCountRequest request) {
    return new VehicleCountResponse()
        .setTotalCount(
            maintenanceDashboardService.countVehicleServicedInNoOfMonth(
                request.getPlateNumbers(), request.getNoOfMonths()));
  }

  @Override
  public VehicleCountType countType() {
    return SERVICED_COUNT;
  }
}
