package com.lumi.rental.core.fleet.repository;

import com.lumi.rental.core.fleet.entity.VehicleGroup;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface VehicleGroupRepository extends JpaRepository<VehicleGroup, Integer> {

  /**
   * Retrieves a paginated list of VehicleGroup entries based on the enabled flag.
   *
   * @param enabled the enabled flag to filter by
   * @param pageable the pagination information
   * @return a paginated list of VehicleGroup entities
   */
  @EntityGraph("NEG_VEHICLE_GROUP_ALL")
  Page<VehicleGroup> findAllByEnabled(Boolean enabled, Pageable pageable);

  @EntityGraph("NEG_VEHICLE_GROUP_ALL")
  Optional<VehicleGroup> findByCode(String groupCode);

  @EntityGraph("NEG_VEHICLE_GROUP_ALL")
  Page<VehicleGroup> findByIdInAndEnabled(List<Integer> ids, Boolean enabled, Pageable pageable);
}
