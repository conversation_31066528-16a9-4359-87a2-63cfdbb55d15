package com.lumi.rental.core.fleet.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Inheritance(strategy = InheritanceType.JOINED)
@Table(name = "document_category")
public class DocumentCategory extends BaseEntity {

  @Column(name = "code")
  private String code;

  @Column(name = "description_en")
  private String descriptionEn;

  @Column(name = "description_ar")
  private String descriptionAr;
}
