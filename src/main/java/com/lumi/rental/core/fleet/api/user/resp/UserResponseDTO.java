package com.lumi.rental.core.fleet.api.user.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserResponseDTO implements Serializable {

  @Serial private static final long serialVersionUID = -1045200389894877145L;

  private Long id;
  private String firstName;
  private String lastName;
  private String email;
  private String externalId;
  private String successFactorId;
}
