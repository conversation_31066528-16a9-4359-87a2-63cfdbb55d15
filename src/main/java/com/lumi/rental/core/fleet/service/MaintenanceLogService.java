package com.lumi.rental.core.fleet.service;

import static java.lang.String.join;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumi.rental.core.fleet.entity.MaintenanceLog;
import com.lumi.rental.core.fleet.entity.MaintenanceType;
import com.lumi.rental.core.fleet.entity.es.ESMaintenanceLog;
import com.lumi.rental.core.fleet.repository.MaintenanceLogRepository;
import com.lumi.rental.core.fleet.repository.es.ESMaintenanceLogRepository;
import com.seera.lumi.core.fleet.dto.LastMaintenanceRequestDTO;
import com.seera.lumi.core.fleet.dto.MaintenanceLogDTO;
import com.seera.lumi.core.fleet.dto.VehicleMaintenanceEventDTO;
import com.seera.lumi.core.fleet.mapper.MaintenanceLogMapper;
import com.seera.lumi.core.fleet.service.MaintenanceTypeService;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.utils.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MaintenanceLogService {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final ESMaintenanceLogRepository esRepository;
  private final MaintenanceLogMapper maintenanceLogMapper;
  private final MaintenanceTypeService maintenanceTypeService;
  private final MaintenanceLogRepository maintenanceLogRepository;
  private final ObjectMapper objectMapper;

  public List<MaintenanceLogDTO> findAllMaintenanceLogs(String plateNo) {
    if (StringUtils.isEmpty(plateNo)) {
      throw new IllegalArgumentException("Invalid plateNo");
    }

    List<ESMaintenanceLog> maintenanceLogs = esRepository.findByPlateNo(plateNo);
    Map<String, MaintenanceType> maintenanceTypeMap =
        maintenanceTypeService.maintenanceTypeOnServiceType();

    return maintenanceLogs.stream()
        .filter(log -> nonNull(log.getDate()))
        .collect(Collectors.groupingBy(ESMaintenanceLog::getDate))
        .values()
        .stream()
        .map(logs -> maintenanceLogMapper.toMaintenanceLogDTO(logs, maintenanceTypeMap))
        .sorted(Comparator.comparing(MaintenanceLogDTO::getDate).reversed())
        .collect(Collectors.toList());
  }

  public Optional<MaintenanceLog> findLatestMaintenanceByPlateNo(String plateNo) {
    return maintenanceLogRepository.findFirstByPlateNoOrderByRecordDateDesc(plateNo);
  }

  public Map<String, Long> findLatestMaintenanceDateByPlateNos(Set<String> plateNos) {
    return maintenanceLogRepository.findLatestMaintenanceDateByPlateNos(plateNos);
  }

  // TODO : return result from MySQL
  public List<MaintenanceLogDTO> getLastMaintenance(LastMaintenanceRequestDTO requestDTO) {
    List<ESMaintenanceLog> maintenanceLogs =
        esRepository.getLastMaintenanceLogs(requestDTO.getPlateNos(), null);
    Map<String, List<ESMaintenanceLog>> groupedData =
        maintenanceLogs.stream()
            .collect(groupingBy(ESMaintenanceLog::getPlateNo, LinkedHashMap::new, toList()));
    Map<String, MaintenanceType> maintenanceTypeMap =
        maintenanceTypeService.maintenanceTypeOnServiceType();
    return groupedData.values().stream()
        .map(logs -> maintenanceLogMapper.toMaintenanceLogDTO(logs, maintenanceTypeMap))
        .sorted(Comparator.comparing(MaintenanceLogDTO::getDate).reversed())
        .toList();
  }

  public void saveAll(List<ESMaintenanceLog> maintenanceLogList) {
    esRepository.saveAll(maintenanceLogList);
  }

  public void syncVehicleMaintenanceDataInOpenSearchDataSource(List<Bytes> eventList) {
    List<ESMaintenanceLog> esMaintenanceLogs = new ArrayList<>();
    Map<String, Long> newRecordsCacheMap = new LinkedHashMap<>();
    eventList.forEach(
        event -> {
          try {
            VehicleMaintenanceEventDTO dto =
                objectMapper.readValue(event.get(), VehicleMaintenanceEventDTO.class);
            String key =
                join("|", dto.getPlateNo(), dto.getDocDate().toString(), dto.getServiceType());
            Long count = newRecordsCacheMap.get(key);
            if (isNull(count)) {
              count =
                  esRepository.findByPlateNoAndDateAndServiceType(
                      dto.getPlateNo().trim(),
                      dto.getDocDate(),
                      StringUtils.trim(dto.getServiceType()));
            }
            if (count.intValue() == 0) {
              ESMaintenanceLog maintenanceLog = maintenanceLogMapper.map(dto);
              esMaintenanceLogs.add(maintenanceLog);
              newRecordsCacheMap.put(key, count + 1);
            }
          } catch (Exception ex) {
            log.error(
                "Error in processing even for open-search-sync {}", new String(event.get()), ex);
          }
        });
    if (ObjectUtils.isNotEmpty(esMaintenanceLogs)) {
      saveAll(esMaintenanceLogs);
    }
  }
}
