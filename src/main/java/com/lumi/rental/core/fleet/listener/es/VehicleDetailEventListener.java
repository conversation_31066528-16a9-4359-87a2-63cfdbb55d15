package com.lumi.rental.core.fleet.listener.es;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.seera.lumi.core.fleet.dto.VehicleDTO;
import com.seera.lumi.core.fleet.entity.opensearch.ESVehicleData;
import com.seera.lumi.core.fleet.mapper.VehicleMapper;
import com.seera.lumi.core.fleet.service.VehicleService;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.common.utils.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleDetailEventListener {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final VehicleService vehicleService;
  private final VehicleMapper vehicleMapper;
  private final ObjectMapper objectMapper;

  //  @KafkaListener(
  //      topics = {"${kafka.topic.vehicle.financial.data}"},
  //      groupId = "open-search-sync",
  //      containerFactory = "kafkaBatchListenerContainerFactory",
  //      autoStartup = "${kafka.listen.auto.start:true}")
  public void listen(@Payload List<Bytes> eventList) {
    try {
      log.info("Total {} Message received for vehicle data open-search-sync", eventList.size());
      List<ESVehicleData> vehicleList = new ArrayList<>();
      for (Bytes event : eventList) {
        VehicleDTO vehicleDTO = objectMapper.readValue(event.get(), VehicleDTO.class);
        String docId = vehicleDTO.getPlateNo().replace(" ", "");
        ESVehicleData existing = vehicleService.getVehicleById(docId);
        if (existing == null) {
          existing = new ESVehicleData(docId);
        }
        vehicleMapper.map(vehicleDTO, existing);
        vehicleList.add(existing);
      }
      vehicleService.saveVehicleList(vehicleList);
    } catch (Exception ex) {
      log.error(
          "Error while processing vehicle data open-search-sync event {} exception {}",
          eventList,
          ex.getStackTrace());
    }
  }
}
