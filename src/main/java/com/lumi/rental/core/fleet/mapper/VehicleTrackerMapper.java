package com.lumi.rental.core.fleet.mapper;

import com.lumi.rental.core.fleet.entity.es.ESVehicleTrackingInfoHistory;
import com.lumi.rental.core.fleet.response.VehicleTrackingResponse;
import com.seera.lumi.core.fleet.dto.VehicleLiveTrackingEventDTO;
import com.seera.lumi.core.fleet.dto.VehicleLiveTrackingRawEventDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VehicleTrackerMapper {

  VehicleLiveTrackingEventDTO mapVehicleTrackingInfoHistoryToDTO(
      ESVehicleTrackingInfoHistory history);

  @Mapping(target = "plateNo", source = "plateNumber")
  VehicleLiveTrackingEventDTO map(VehicleLiveTrackingRawEventDTO event);

  VehicleTrackingResponse toDTO(ESVehicleTrackingInfoHistory vehicle);

  ESVehicleTrackingInfoHistory mapVehicleTrackingInfoHistory(
      VehicleLiveTrackingEventDTO trackingEventDTO);

  VehicleTrackingResponse mapVehicleTrackingDTO(VehicleLiveTrackingEventDTO cachedRecord);
}
