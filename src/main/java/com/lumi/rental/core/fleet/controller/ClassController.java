package com.lumi.rental.core.fleet.controller;

import com.lumi.rental.core.fleet.request.BasePageRequest;
import com.lumi.rental.core.fleet.request.CreateUpdateVehicleClassRequest;
import com.lumi.rental.core.fleet.response.VehicleClassResponse;
import com.lumi.rental.core.fleet.service.VehicleClassService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/vehicle-class")
public class ClassController {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final VehicleClassService classService;

  @GetMapping
  public Page<VehicleClassResponse> getAllVehicleClass(@Valid final BasePageRequest request) {
    log.info(
        "Fetching all vehicle classes with page number {} and page size {}",
        request.getPageNumber(),
        request.getPageSize());
    return classService.getAllVehicleClass(
        PageRequest.of(request.getPageNumber(), request.getPageSize()));
  }

  @GetMapping("/{id}")
  public VehicleClassResponse getVehicleClassById(
      @PathVariable(value = "id") final Integer vehicleClassId) {
    log.info("Fetching vehicle class with ID: {}", vehicleClassId);
    return classService.getVehicleClassById(vehicleClassId);
  }


  @PostMapping
  public ResponseEntity<VehicleClassResponse> createVehicleClass(
      @Valid @RequestBody CreateUpdateVehicleClassRequest request) {
    log.info("Creating a new vehicle class with request: {}", request);
    VehicleClassResponse createdVehicleClass = classService.createVehicleClass(request);
    return new ResponseEntity<>(createdVehicleClass, HttpStatus.CREATED);
  }

  @PutMapping("/{id}")
  public ResponseEntity<VehicleClassResponse> updateVehicleClass(
      @PathVariable Integer id, @Valid @RequestBody CreateUpdateVehicleClassRequest request) {
    log.info("Updating vehicle class with ID: {} and request: {}", id, request);
    VehicleClassResponse updatedVehicleClass = classService.updateVehicleClass(id, request);
    return updatedVehicleClass != null
        ? new ResponseEntity<>(updatedVehicleClass, HttpStatus.OK)
        : new ResponseEntity<>(HttpStatus.NOT_FOUND);
  }
}
