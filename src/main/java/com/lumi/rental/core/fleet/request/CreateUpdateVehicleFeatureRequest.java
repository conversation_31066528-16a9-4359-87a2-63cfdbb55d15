package com.lumi.rental.core.fleet.request;

import com.lumi.rental.core.fleet.dto.MultilingualDTO;
import com.lumi.rental.core.fleet.response.VehicleFeatureValueDTO;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
public class CreateUpdateVehicleFeatureRequest implements Serializable {

  private MultilingualDTO name;
  private String imageUrl;
  private List<VehicleFeatureValueDTO> vehicleFeatureValueDTOList;
  private Boolean enabled;
}
