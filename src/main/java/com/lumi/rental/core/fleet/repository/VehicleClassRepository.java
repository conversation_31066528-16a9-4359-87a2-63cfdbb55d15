package com.lumi.rental.core.fleet.repository;

import com.lumi.rental.core.fleet.dto.CountVO;
import com.lumi.rental.core.fleet.entity.VehicleClass;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface VehicleClassRepository extends JpaRepository<VehicleClass, Integer> {

  Page<VehicleClass> findAll(Pageable pageable);

  @Query(
      """
            select new com.lumi.rental.core.fleet.dto.CountVO( vm.vehicleClass.id , count(v)) from Vehicle v
            LEFT JOIN VehicleModel vm on v.model.id = vm.id
            group by vm.vehicleClass.id
        """)
  List<CountVO> findVehicleCountByClass();
}
