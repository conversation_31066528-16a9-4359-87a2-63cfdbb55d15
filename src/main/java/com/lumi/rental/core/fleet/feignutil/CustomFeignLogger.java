package com.lumi.rental.core.fleet.feignutil;

import static com.lumi.rental.core.fleet.constants.Constants.LogConstants.REQUEST;
import static com.lumi.rental.core.fleet.constants.Constants.LogConstants.RESPONSE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumi.rental.core.fleet.dto.RequestResponseLoggerObject;
import com.lumi.rental.core.fleet.enums.LogType;
import feign.Logger;
import feign.Request;
import feign.Response;
import feign.Util;
import java.io.IOException;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

// @Component
@RequiredArgsConstructor
public class CustomFeignLogger extends Logger {

  //    private final DownstreamLoggingConfig downstreamLoggingConfig;

  private static final org.slf4j.Logger reqResLogger = LoggerFactory.getLogger("reqResLogger");
  private static final org.slf4j.Logger log = LoggerFactory.getLogger("application");
  private final ObjectMapper mapper;

  @Override
  protected void log(String configKey, String format, Object... args) {
    /** implementation will be done */
  }

  @Override
  protected void logRequest(String configKey, Level logLevel, Request request) {
    RequestResponseLoggerObject requestObject =
        RequestResponseLoggerObject.builder()
            .logType(LogType.DOWNSTREAM_REQRESP.name())
            .eventType(REQUEST)
            .requestType(getRequestType(configKey))
            .resource(request.url())
            .methodType(request.httpMethod().name())
            .headers(getHeader(request.headers()))
            .build();

    if (Request.HttpMethod.POST.name().equals(request.httpMethod().name())) {
      try {
        requestObject.setRequest(mapper.readTree(request.body()));
      } catch (Exception ex) {
        log.error("Failed to parse request body for method: {}", request.httpMethod().name(), ex);
      }
    }
    if (reqResLogger.isInfoEnabled()) {
      reqResLogger.info(requestObject.toString());
    }
  }

  @Override
  protected IOException logIOException(
      String configKey, Level logLevel, IOException ioe, long elapsedTime) {
    RequestResponseLoggerObject responseObject =
        RequestResponseLoggerObject.builder()
            .logType(LogType.DOWNSTREAM_REQRESP.name())
            .eventType(RESPONSE)
            .requestType(getRequestType(configKey))
            .totalDuration(elapsedTime)
            .status("DownstreamException")
            .response(ioe.getMessage())
            .build();
    if (reqResLogger.isInfoEnabled()) {
      reqResLogger.info(responseObject.toString());
    }
    return super.logIOException(configKey, logLevel, ioe, elapsedTime);
  }

  @Override
  protected Response logAndRebufferResponse(
      String configKey, Level logLevel, Response response, long elapsedTime) throws IOException {
    HttpStatus responseStatus =
        Objects.requireNonNull(
            HttpStatus.resolve(response.status()), "Unable to interpret response status");
    RequestResponseLoggerObject responseObject =
        RequestResponseLoggerObject.builder()
            .logType(LogType.DOWNSTREAM_REQRESP.name())
            .eventType(RESPONSE)
            .requestType(getRequestType(configKey))
            .resource(response.request().url())
            .totalDuration(elapsedTime)
            .headers(getHeader(response.headers()))
            .status(responseStatus.toString())
            .build();

    byte[] bodyData = Util.toByteArray(response.body().asInputStream());
    try {
      responseObject.setResponse(mapper.readTree(bodyData));
    } catch (Exception ex) {
      log.error("Failed to parse response body for method: ", ex);
    }
    if (reqResLogger.isInfoEnabled()) {
      reqResLogger.info(responseObject.toString());
    }
    Response.Builder responseBuilder = response.toBuilder();
    responseBuilder.body(bodyData);
    return responseBuilder.build();
  }

  private Map<String, String> getHeader(Map<String, Collection<String>> headers) {
    Map<String, String> headerMap = new HashMap<>();
    for (Map.Entry<String, Collection<String>> entry : headers.entrySet()) {
      //            if (downstreamLoggingConfig.getHeader().contains(entry.getKey()))
      headerMap.put(entry.getKey(), entry.getValue().toString());
    }
    return headerMap;
  }

  private String getRequestType(String configKey) {
    try {
      String str = configKey.split("[(]")[0];
      String clientName = str.split("#")[0];
      String apiName = str.split("#")[1];
      return clientName.replace("Client", "") + "-" + apiName.replace("call", "");
    } catch (Exception ex) {
      return StringUtils.EMPTY;
    }
  }
}
