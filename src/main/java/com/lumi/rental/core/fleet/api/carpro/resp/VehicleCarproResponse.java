package com.lumi.rental.core.fleet.api.carpro.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Data;

@Data
public class VehicleCarproResponse implements Serializable {

  private String plateNo;
  private String plateNoInAr;
  private Integer carproMakeId;
  private Integer carproModelId;
  private String chassisNo;

  @JsonProperty("INSURANCE_POLICY_NO")
  private String insurancePolicyNo;
}
