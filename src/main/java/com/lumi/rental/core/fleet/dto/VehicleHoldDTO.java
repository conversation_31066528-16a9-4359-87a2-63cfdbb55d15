package com.lumi.rental.core.fleet.dto;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleHoldDTO implements Serializable {

  @Serial private static final long serialVersionUID = -1L;

  private String plateNo;
  private String cacheReferenceKey;
}
