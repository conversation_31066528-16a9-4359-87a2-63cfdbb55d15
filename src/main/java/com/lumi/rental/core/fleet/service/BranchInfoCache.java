package com.lumi.rental.core.fleet.service;

import com.lumi.rental.core.fleet.api.branch.BranchServiceClient;
import com.lumi.rental.core.fleet.api.branch.resp.BranchResponse;
import com.lumi.rental.core.fleet.api.branch.resp.LocationData;
import com.lumi.rental.core.fleet.dto.LocationDTO;
import com.lumi.rental.core.fleet.dto.MultilingualDTO;
import jakarta.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class BranchInfoCache {

  private static final Logger log = LoggerFactory.getLogger("application");
  private static final String CARPRO_CACHE_PREFIX = "CARPRO:";
  private static final int CACHE_CLEANUP_INTERVAL = 30 * 60 * 1000; // 30 minutes

  private final Map<String, LocationDTO> branchInfoMap = new ConcurrentHashMap<>();
  @Autowired private BranchServiceClient branchServiceClient;

  @PostConstruct
  public void init() {
    loadBranchData();
  }

  @Scheduled(fixedRate = CACHE_CLEANUP_INTERVAL)
  public void refreshCache() {
    log.info("Starting scheduled branch cache refresh");
    loadBranchData();
  }

  private void loadBranchData() {
    try {
      log.info("Refreshing branch data");
      BranchResponse response = branchServiceClient.getBranchList();
      if (response != null && response.getData() != null) {
        Map<String, LocationDTO> newCache = new ConcurrentHashMap<>();
        for (LocationData branch : response.getData()) {
          LocationDTO locationDTO =
              new LocationDTO(
                  Integer.parseInt(branch.getCode()),
                  branch.getId(),
                  new MultilingualDTO(branch.getName().getEn(), branch.getName().getAr()),
                  branch.getYaqeenMigrated());

          newCache.put(String.valueOf(branch.getId()), locationDTO);
          newCache.put(CARPRO_CACHE_PREFIX + branch.getCode(), locationDTO);
        }

        // Atomic update of cache
        branchInfoMap.clear();
        branchInfoMap.putAll(newCache);

        log.info("Branch data refreshed successfully. Total entries: {}", branchInfoMap.size() / 2);
      }
    } catch (Exception e) {
      log.error("Error while refreshing branch data", e);
    }
  }

  public LocationDTO getBranchCode(Integer code) {
    LocationDTO location = branchInfoMap.get(String.valueOf(code));
    if (location == null) {
      log.warn("Branch not found for lumi-id: {}", code);
      return new LocationDTO(null, code, null, null);
    }
    return location;
  }

  public LocationDTO getBranchCarproCode(String code) {
    LocationDTO location = branchInfoMap.get(CARPRO_CACHE_PREFIX + code);
    if (location == null) {
      log.warn("Branch not found for carpro-code: {}", code);
      return new LocationDTO(Integer.parseInt(code), null, null, null);
    }
    return location;
  }

  public Map<String, LocationDTO> getBranchInfo() {
    return new ConcurrentHashMap<>(branchInfoMap);
  }

  public MultilingualDTO getBranchName(Integer code) {
    LocationDTO location = branchInfoMap.get(String.valueOf(code));
    if (location != null) {
      return location.getName();
    } else {
      log.error("Branch not found for lumi-id: {}", code);
      return null;
    }
  }
}
