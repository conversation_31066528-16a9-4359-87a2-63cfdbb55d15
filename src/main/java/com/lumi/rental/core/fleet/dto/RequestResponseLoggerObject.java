package com.lumi.rental.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.lumi.rental.core.fleet.util.CommonUtils;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.LowerCaseStrategy.class)
public class RequestResponseLoggerObject {

  private String eventTimeStamp;

  private String logType; // UPSTREAM_REQRESP DOWNSTREAM_REQRESP APPLICATION
  private String eventType; // RequestMessage ResponseMessage
  private String status; // http status code in response
  private Long totalDuration; // response_time
  private String requestType; // api method name in case of down-stream
  private String resource; // api url
  private String methodType; // GET POST PUT
  private Map<String, String> headers;
  private Object request; // request body
  private Object response; // response body

  @Override
  public String toString() {
    return CommonUtils.getStrFromObj(this);
  }
}
