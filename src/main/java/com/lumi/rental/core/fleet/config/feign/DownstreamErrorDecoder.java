package com.lumi.rental.core.fleet.config.feign;

import static com.lumi.rental.core.fleet.exception.BaseError.INTERNAL_SERVER_BASE_ERROR;

import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.exception.DownstreamServiceException;
import feign.Response;
import feign.codec.ErrorDecoder;
import java.io.IOException;
import java.nio.charset.Charset;
import org.apache.commons.io.IOUtils;

public class DownstreamErrorDecoder implements ErrorDecoder {
  @Override
  public Exception decode(String methodKey, Response response) throws BusinessException {
    String responseBody;
    try {
      if (response.body() != null && response.body().asInputStream() != null) {
        responseBody = IOUtils.toString(response.body().asInputStream(), Charset.defaultCharset());
        return new DownstreamServiceException(responseBody, response.status());
      }
    } catch (IOException e) {
      throw new BusinessException(e, INTERNAL_SERVER_BASE_ERROR);
    }
    return new Default().decode(methodKey, response);
  }
}
