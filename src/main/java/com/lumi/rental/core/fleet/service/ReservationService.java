package com.lumi.rental.core.fleet.service;

import static com.lumi.rental.core.fleet.exception.BaseError.BAD_REQUEST_WITH_REASON;
import static com.lumi.rental.core.fleet.exception.BaseError.validationError;
import static java.util.Objects.isNull;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

import com.lumi.rental.core.fleet.dto.VehicleHoldDTO;
import com.lumi.rental.core.fleet.dto.VehicleStatusInfo;
import com.lumi.rental.core.fleet.entity.VehicleOperationalData;
import com.lumi.rental.core.fleet.entity.VehicleReservationData;
import com.lumi.rental.core.fleet.enums.StatusType;
import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.mapper.ReservationMapper;
import com.lumi.rental.core.fleet.repository.VehicleReservationDataRepository;
import com.lumi.rental.core.fleet.request.UpdateVehicleStatusRequest;
import com.lumi.rental.core.fleet.request.VehicleReservationRequest;
import com.lumi.rental.core.fleet.response.ReservationResponse;
import com.lumi.rental.core.fleet.util.CacheKeyUtil;
import com.seera.lumi.core.cache.service.CacheService;
import jakarta.validation.Valid;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import javax.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ReservationService {

  private final VehicleAvailabilityService availabilityService;

  private final VehicleReservationDataRepository reservationDataRepository;
  private final ReservationMapper reservationMapper;
  private final CacheService cacheService;

  @Transactional
  public ReservationResponse holdVehicleForReservation(VehicleReservationRequest request) {
    // Step 1: Get Vehicle Operational Data
    VehicleOperationalData vehicleOperationalData =
        availabilityService.getVehicleOperationalData(request.getPlateNo());

    // Step 2: Validate Vehicle Status
    validateVehicleStatus(vehicleOperationalData, StatusType.READY);

    // Step 3: Handle Reservation Lock
    handleVehicleLockForReservation(request, 600);

    // Return Success Response
    return new ReservationResponse("Success");
  }

  public ReservationResponse validateVehicleLock(@Valid VehicleReservationRequest request) {
    String cacheKey = CacheKeyUtil.getKeyForHoldReservation(request.getPlateNo());
    VehicleHoldDTO existingReference = cacheService.get(cacheKey, VehicleHoldDTO.class);
    validateVehicleLock(existingReference, request.getReferenceId());
    if (isNull(existingReference)) {
      return new ReservationResponse("No lock Found for this vehicle");
    } else {
      return new ReservationResponse("Success");
    }
  }

  public ReservationResponse assignVehicleForReservation(VehicleReservationRequest request) {
    // Step 1: Get Vehicle Operational Data
    VehicleOperationalData vehicleOperationalData =
        availabilityService.getVehicleOperationalData(request.getPlateNo());

    // Step 2: Validate Vehicle Status
    validateVehicleStatus(vehicleOperationalData, StatusType.READY);

    // Step 3: Handle Reservation Lock
    handleVehicleLockForReservation(request, 7200);

    // Return Success Response
    return new ReservationResponse("Success");
  }

  public ReservationResponse cancelReservation(VehicleReservationRequest request) {

    String cacheKey = CacheKeyUtil.getKeyForHoldReservation(request.getPlateNo());
    VehicleHoldDTO existingReference = cacheService.get(cacheKey, VehicleHoldDTO.class);

    if (isNotEmpty(existingReference)) {
      validateVehicleLock(existingReference, request.getReferenceId());
      flushVehicleLock(request.getPlateNo());
    }

    return new ReservationResponse("Success");
  }

  @Transactional
  public ReservationResponse confirmReservation(VehicleReservationRequest request) {
    // Step 1: Get Vehicle Operational Data
    VehicleOperationalData vehicleOperationalData =
        availabilityService.getVehicleOperationalData(request.getPlateNo());

    // Step 2: Validate Vehicle Status
    validateVehicleStatus(vehicleOperationalData, StatusType.READY);

    // Step 3: Validate Cache Reference Key
    validateCacheReferenceKey(request);

    // Step 4: Save Reservation Data
    createReservation(request);

    // Step 5: Update Vehicle Status to Rented
    VehicleStatusInfo vehicleStatusInfo = new VehicleStatusInfo(StatusType.RENTED.getId());
    vehicleOperationalData.setVehicleStatus(vehicleStatusInfo);
    availabilityService.updateVehicleOperationData(vehicleOperationalData);
    flushVehicleLock(request.getPlateNo());

    // Return Success Response
    return new ReservationResponse("Success");
  }

  @Transactional
  public ReservationResponse updateVehicleStatus(UpdateVehicleStatusRequest request) {
    // Step 1: Get Vehicle Operational Data
    VehicleOperationalData vehicleOperationalData =
        availabilityService.getVehicleOperationalData(request.getPlateNo());

    if (ObjectUtils.isNotEmpty(request.getOdometerReading()))
      vehicleOperationalData.setOdometerReading(request.getOdometerReading());

    if (ObjectUtils.isNotEmpty(request.getFuelLevel())) {
      vehicleOperationalData.setFuelLevel(request.getFuelLevel());
    }

    if (ObjectUtils.isNotEmpty(request.getBranchId()))
      vehicleOperationalData.setCurrentLocationId(request.getBranchId());

    if (ObjectUtils.isNotEmpty(request.getOwnerBranchId()))
      vehicleOperationalData.setOwnerBranchId(request.getOwnerBranchId());

    if (ObjectUtils.isNotEmpty(request.getServiceType()))
      vehicleOperationalData.setServiceTypeId(request.getServiceType().getId());

    if (ObjectUtils.isNotEmpty(request.getStatusType())) {
      assert !StatusType.getMultilevelStatusTypes().contains(request.getStatusType())
          || request.getStatusReason() != null;

      VehicleStatusInfo vehicleStatusInfo =
          new VehicleStatusInfo(request.getStatusType().getId(), request.getStatusReason());
      vehicleOperationalData.setVehicleStatus(vehicleStatusInfo);
    }
    availabilityService.updateVehicleOperationData(vehicleOperationalData);

    // Return Success Response
    return new ReservationResponse("Success");
  }

  private void validateVehicleStatus(
      VehicleOperationalData vehicleOperationalData, StatusType expectedStatus) {
    if (!expectedStatus.getId().equals(vehicleOperationalData.getVehicleStatus().getStatusId())) {
      throw new BusinessException(
          validationError(
              String.format(
                  "Vehicle %s is not in the %s state, current status is %s",
                  vehicleOperationalData.getPlateNo(),
                  expectedStatus,
                  StatusType.fromId(vehicleOperationalData.getVehicleStatus().getStatusId()))));
    }
  }

  private void handleVehicleLockForReservation(
      VehicleReservationRequest request, Integer holdForSeconds) {
    String cacheKey = CacheKeyUtil.getKeyForHoldReservation(request.getPlateNo());
    VehicleHoldDTO existingReference = cacheService.get(cacheKey, VehicleHoldDTO.class);

    validateVehicleLock(existingReference, request.getReferenceId());

    if (isNull(existingReference)) {
      takeVehicleLock(cacheKey, request.getPlateNo(), request.getReferenceId(), holdForSeconds);
    }
  }

  private void validateVehicleLock(VehicleHoldDTO existingReference, String requestReferenceId) {
    if (isNotEmpty(existingReference)
        && !existingReference.getCacheReferenceKey().equals(requestReferenceId)) {
      String errorMessage =
          String.format(
              "Vehicle is locked for %s reservation", existingReference.getCacheReferenceKey());
      throw new BusinessException(BaseError.BAD_REQUEST_WITH_REASON, errorMessage);
    }
  }

  private void takeVehicleLock(
      String cacheKey, String plateNo, String referenceId, Integer timeInSeconds) {
    VehicleHoldDTO newReference = new VehicleHoldDTO(plateNo, referenceId);
    cacheService.put(cacheKey, newReference, timeInSeconds, TimeUnit.SECONDS);
  }

  private void flushVehicleLock(String plateNo) {
    String cacheKey = CacheKeyUtil.getKeyForHoldReservation(plateNo);
    cacheService.clearAllKeys(cacheKey);
  }

  private void validateCacheReferenceKey(VehicleReservationRequest request) {
    String cacheKey = CacheKeyUtil.getKeyForHoldReservation(request.getPlateNo());
    VehicleHoldDTO cachedReference = cacheService.get(cacheKey, VehicleHoldDTO.class);

    if (ObjectUtils.isEmpty(cachedReference)) {
      throw new BusinessException(BaseError.INVALID_ARG_VALUE, "reference-id");
    }

    if (!cachedReference.getCacheReferenceKey().equals(request.getReferenceId())) {
      throw new BusinessException(BaseError.ALREADY_EXISTS, "Vehicle-Lock");
    }
  }

  private void createReservation(VehicleReservationRequest request) {
    Optional<VehicleReservationData> oldReservation =
        reservationDataRepository.findByPlateNoAndReferenceId(
            request.getPlateNo(), request.getReferenceId());
    if (oldReservation.isPresent()) {
      throw new BusinessException(BaseError.ALREADY_EXISTS, "Vehicle-Reservation");
    } else {
      VehicleReservationData newReservation =
          reservationMapper.toVehicleReservationData(
              request.getPlateNo(), request.getReferenceId(), request.getReservation());
      reservationDataRepository.saveAndFlush(newReservation);
    }
  }

  @Transactional
  public ReservationResponse updateReservation(VehicleReservationRequest request) {
    Optional<VehicleReservationData> reservation =
        reservationDataRepository.findByPlateNoAndReferenceId(
            request.getPlateNo(), request.getReferenceId());
    if (reservation.isEmpty()) {
      throw new BusinessException(BaseError.NOT_FOUND, "Vehicle-Reservation");
    } else {
      reservationMapper.updateReservationData(reservation.get(), request.getReservation());
      reservationDataRepository.save(reservation.get());
    }
    return new ReservationResponse("Success");
  }

  /** To replace existing vehicle lock reference key with new one */
  private void handleVehicleLockForReservation(
      String oldReferenceId, VehicleReservationRequest request, Integer holdForSeconds) {
    String cacheKey = CacheKeyUtil.getKeyForHoldReservation(request.getPlateNo());
    VehicleHoldDTO existingReference = cacheService.get(cacheKey, VehicleHoldDTO.class);
    if (isNull(existingReference)) {
      String errorMessage =
          String.format("Vehicle did not locked for referenceId %s", oldReferenceId);
      throw new BusinessException(BAD_REQUEST_WITH_REASON, errorMessage);
    }
    validateVehicleLock(existingReference, oldReferenceId);
    existingReference.setCacheReferenceKey(request.getReferenceId());
    takeVehicleLock(cacheKey, request.getPlateNo(), request.getReferenceId(), holdForSeconds);
  }

  public ReservationResponse holdReplaceVehicleForReservation(
      String oldReferenceId, @Valid VehicleReservationRequest request) {
    // Step 1: Get Vehicle Operational Data
    VehicleOperationalData vehicleOperationalData =
        availabilityService.getVehicleOperationalData(request.getPlateNo());

    // Step 2: Validate Vehicle Status
    validateVehicleStatus(vehicleOperationalData, StatusType.READY);

    // Step 3: Handle Reservation Lock
    handleVehicleLockForReservation(oldReferenceId, request, 600);

    // Return Success Response
    return new ReservationResponse("Success");
  }
}
