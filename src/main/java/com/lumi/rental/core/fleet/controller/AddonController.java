package com.lumi.rental.core.fleet.controller;

import com.lumi.rental.core.fleet.request.BasePageRequest;
import com.lumi.rental.core.fleet.request.CreateUpdateAddonRequest;
import com.lumi.rental.core.fleet.response.AddonResponse;
import com.lumi.rental.core.fleet.service.AddonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/addon")
public class AddonController {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final AddonService addonService;

  @GetMapping
  public Page<AddonResponse> getAllAddon(@Valid final BasePageRequest request) {
    log.info(
        "Fetching all addons with page number {} and page size {}",
        request.getPageNumber(),
        request.getPageSize());
    return addonService.getAllAddons(
        PageRequest.of(request.getPageNumber(), request.getPageSize()));
  }

  @GetMapping("/{id}")
  public AddonResponse getAddonById(@PathVariable(value = "id") final Integer addonId) {
    log.info("Fetching addon with ID: {}", addonId);
    return addonService.getAddonById(addonId);
  }

  @PostMapping
  public ResponseEntity<AddonResponse> createAddon(
      @Valid @RequestBody CreateUpdateAddonRequest request) {
    log.info("Creating a new addon with request: {}", request);
    AddonResponse createdAddon = addonService.createAddon(request);
    return new ResponseEntity<>(createdAddon, HttpStatus.CREATED);
  }

  @PutMapping("/{id}")
  public ResponseEntity<AddonResponse> updateAddon(
      @PathVariable Integer id, @Valid @RequestBody CreateUpdateAddonRequest request) {
    log.info("Updating addon with ID: {} and request: {}", id, request);
    AddonResponse updatedAddon = addonService.updateAddon(id, request);
    return updatedAddon != null
        ? new ResponseEntity<>(updatedAddon, HttpStatus.OK)
        : new ResponseEntity<>(HttpStatus.NOT_FOUND);
  }
}
