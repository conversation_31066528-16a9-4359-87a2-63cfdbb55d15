package com.lumi.rental.core.fleet.action.handler;

import static com.lumi.rental.core.fleet.enums.VehicleCountType.HAS_KM_READING_COUNT;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumi.rental.core.fleet.action.CountVehicleHandler;
import com.lumi.rental.core.fleet.enums.VehicleCountType;
import com.lumi.rental.core.fleet.request.VehicleCountRequest;
import com.lumi.rental.core.fleet.response.VehicleCountResponse;
import com.lumi.rental.core.fleet.util.VehicleUtil;
import com.seera.lumi.core.cache.service.CacheService;
import com.seera.lumi.core.fleet.dto.VehicleLiveTrackingEventDTO;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CountKmReadingVehicleHandler implements CountVehicleHandler {

  private final CacheService cacheService;
  private final ObjectMapper objectMapper;

  @Override
  public VehicleCountResponse execute(VehicleCountRequest request) {
    long count =
        request.getPlateNumbers().stream()
            .map(VehicleUtil::getPlateNumberKeyForIOT)
            .map(cacheService::get)
            .filter(Objects::nonNull)
            .map(object -> objectMapper.convertValue(object, VehicleLiveTrackingEventDTO.class))
            .filter(event -> event.getMileage() > 0)
            .count();
    return new VehicleCountResponse().setTotalCount(count);
  }

  @Override
  public VehicleCountType countType() {
    return HAS_KM_READING_COUNT;
  }
}
