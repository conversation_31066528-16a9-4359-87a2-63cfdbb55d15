package com.lumi.rental.core.fleet.request;

import com.lumi.rental.core.fleet.dto.ReservationDTO;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

@Data
public class VehicleReservationRequest implements Serializable {

  @NotNull @NotEmpty private String plateNo;

  @NotNull @NotEmpty private String referenceId;

  private ReservationDTO reservation;
}
