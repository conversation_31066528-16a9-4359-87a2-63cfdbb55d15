package com.lumi.rental.core.fleet.listener;

import static com.lumi.rental.core.fleet.constants.Constants.DEFAULT_MDC_UUID_TOKEN_KEY;
import static org.springframework.kafka.retrytopic.RetryTopicConstants.DEFAULT_DLT_SUFFIX;
import static org.springframework.kafka.support.KafkaHeaders.*;

import com.lumi.rental.core.fleet.service.VehicleSyncService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.common.utils.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.kafka.retrytopic.DltStrategy;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleDetailFromSAPEventListener {

  private static final Logger log = LoggerFactory.getLogger("application");
  private final VehicleSyncService syncService;

  @KafkaListener(
      topics = {"${kafka.topic.vehicle.financial.data}"},
      groupId = "sap-sync-vehicle",
      containerFactory = "kafkaBatchListenerContainerFactory",
      autoStartup = "${kafka.listen.auto.start:true}")
  public void listen(@Payload List<Bytes> eventList) {
    try {
      MDC.put(DEFAULT_MDC_UUID_TOKEN_KEY, UUID.randomUUID().toString().replace("-", ""));
      log.info(
          "[LISTENER] message received for vehicle info data sap-sync-vehicle batch-size {}",
          eventList.size());
      syncService.syncVehicleMetaDataFromSAP(eventList);
      syncService.syncVehicleFinancialInfoFromSAP(eventList);
    } catch (Exception ex) {
      log.error("[LISTENER] error while processing vehicle info data sap-sync-vehicle", ex);
    }
  }

  @RetryableTopic(attempts = "1", dltStrategy = DltStrategy.NO_DLT)
  @KafkaListener(
      topics = {"${kafka.topic.vehicle.financial.data}" + DEFAULT_DLT_SUFFIX},
      groupId = "sync-vehicle-financial-dlt",
      containerFactory = "kafkaListenerContainerFactory",
      autoStartup = "${kafka.listen.auto.start:true}")
  public void listenTODLT(
      @Header(DLT_ORIGINAL_TOPIC) String originalTopic,
      @Header(DLT_EXCEPTION_MESSAGE) String exceptionMessage,
      @Header(DLT_EXCEPTION_STACKTRACE) String exceptionStackTrace) {
    log.info(
        "Unable to process message on topic:{} , errorMessage:{}, exceptionStacktrace:{}",
        originalTopic,
        exceptionMessage,
        exceptionStackTrace);
  }
}
