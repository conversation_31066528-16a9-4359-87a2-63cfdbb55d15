package com.lumi.rental.core.fleet.mapper;

import com.lumi.rental.core.fleet.dto.FeatureDTO;
import com.lumi.rental.core.fleet.dto.FeatureValueDTO;
import com.lumi.rental.core.fleet.dto.ModelImageDTO;
import com.lumi.rental.core.fleet.dto.MultilingualDTO;
import com.lumi.rental.core.fleet.entity.*;
import com.lumi.rental.core.fleet.request.CreateUpdateVehicleModelRequest;
import com.lumi.rental.core.fleet.response.*;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

@Mapper(
    componentModel = "spring",
    uses = {VehicleMakeMapper.class, VehicleGroupMapper.class, VehicleClassMapper.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface VehicleModelMapper {

  @Mapping(target = "make", source = "vehicleModel.make")
  @Mapping(target = "materialId", source = "vehicleModel.sapMaterialId")
  @Mapping(target = "name", source = "vehicleModel", qualifiedByName = "toVehicleModelName")
  @Mapping(
      target = "images",
      source = "vehicleModel.modelImages",
      qualifiedByName = "toVehicleModelImageUrls")
  @Mapping(target = "features", source = "features", qualifiedByName = "toModelFeatures")
  //  @Mapping(target = "vehicleTypeResponse", source = "vehicleModel.vehicleType")
  @Mapping(target = "vehicleClassResponse", source = "vehicleModel.vehicleClass")
  VehicleModelBasicResponse buildVehicleModelBasicResponse(
      VehicleModel vehicleModel, List<ModelFeature> features);

  @Mapping(target = "name", source = "vehicleModel", qualifiedByName = "toVehicleModelName")
  @Mapping(target = "make", source = "make")
  @Mapping(target = "vehicleGroup", source = "vehicleGroup.code")
  @Mapping(target = "images", source = "modelImages", qualifiedByName = "toVehicleModelImageUrls")
  VehicleModelResponse buildVehicleModelResponse(VehicleModel vehicleModel);

  //  @Mapping(target = "name", source = "vehicleModel", qualifiedByName = "toVehicleModelName")
  //  @Mapping(target = "make", source = "vehicleModel.make")
  //  @Mapping(target = "id", source = "vehicleModel.id")
  //  @Mapping(target = "vehicleGroup", source = "vehicleGroup.code")
  //  //  @Mapping(target = "enabled", source = "vehicleModel.enabled")
  //  @Mapping(target = "groupResponse", source = "vehicleGroup")
  //  @Mapping(
  //      target = "images",
  //      source = "vehicleModel.modelImages",
  //      qualifiedByName = "toVehicleModelImageUrls")
  //  VehicleModelResponseV2 buildVehicleModelResponseV2(
  //      VehicleModel vehicleModel, VehicleGroup vehicleGroup);

  //  @Mapping(target = "code", expression = "java(getVehicleClassCode(vehicleClass))")
  //  @Mapping(target = "name", source = "vehicleClass")
  //  VehicleClassResponse toVehicleClassResponse(VehicleClass vehicleClass);

  default String getVehicleClassCode(VehicleClass vehicleClass) {
    if (vehicleClass != null && StringUtils.isNotBlank(vehicleClass.getNameEn())) {
      return vehicleClass.getNameEn().toUpperCase();
    }
    return null;
  }

  @Mapping(target = "en", source = "nameEn")
  @Mapping(target = "ar", source = "nameAr")
  MultilingualDTO toMultilingualDTO(VehicleClass vehicleClass);

  @Named("toModelFeatures")
  default List<ModelFeatureResponse> toModelFeatures(List<ModelFeature> modelFeatures) {
    if (modelFeatures == null || modelFeatures.isEmpty()) {
      return Collections.emptyList();
    }
    return modelFeatures.stream().map(this::toModelFeatureResponse).collect(Collectors.toList());
  }

  @Mapping(target = "feature", source = "featureValue.feature")
  @Mapping(target = "featureValue", source = "featureValue", qualifiedByName = "toFeatureValueList")
  ModelFeatureResponse toModelFeatureResponse(ModelFeature feature);

  @Mapping(target = "name.en", source = "nameEn")
  @Mapping(target = "name.ar", source = "nameAr")
  FeatureDTO toFeatureDTO(VehicleFeature vehicleFeature);

  @Mapping(target = "name.en", source = "nameEn")
  @Mapping(target = "name.ar", source = "nameAr")
  FeatureValueDTO toFeatureValueDTO(VehicleFeatureValue featureValue);

  @Named("toFeatureValueList")
  default List<FeatureValueDTO> toFeatureValueDTOList(VehicleFeatureValue featureValue) {
    return Collections.singletonList(toFeatureValueDTO(featureValue));
  }

  @Mapping(target = "name", source = "model", qualifiedByName = "toVehicleModelName")
  @Mapping(target = "vehicleGroup", source = "vehicleGroup.code")
  VehicleModelBasicResponseV2 buildVehicleModelBasicResponseV2(VehicleModel model);

  @Mapping(target = "name", source = "model", qualifiedByName = "toVehicleModelName")
  @Mapping(target = "vehicleGroup", source = "vehicleGroup.code")
  @Mapping(target = "groupResponse", source = "vehicleGroup")
  VehicleModelBasicResponseV3 buildVehicleModelBasicResponseV3(VehicleModel model);

  @Mapping(target = "name", source = "vehicleModel", qualifiedByName = "toVehicleModelName")
  @Mapping(target = "vehicleGroup", source = "vehicleGroup.code")
  @Mapping(target = "groupResponse", source = "vehicleGroup")
  @Mapping(target = "images", source = "modelImages", qualifiedByName = "toVehicleModelImageUrls")
  @Mapping(
      target = "primaryImageUrl",
      source = "modelImages",
      qualifiedByName = "toPrimaryImageUrl")
  VehicleModelResponseV2 buildVehicleModelResponseV2(VehicleModel vehicleModel);

  @Named("toVehicleModelName")
  default MultilingualDTO toVehicleModelName(VehicleModel vehicleModel) {
    return new MultilingualDTO(vehicleModel.getNameEn(), vehicleModel.getNameAr());
  }

  @Named("toVehicleModelImageUrls")
  default List<ModelImageDTO> toVehicleModelImageUrls(List<ModelImages> modelImages) {
    return modelImages.stream().map(this::toModelImageDTO).toList();
  }

  @Named("toPrimaryImageUrl")
  default String toPrimaryImageUrl(List<ModelImages> modelImages) {
    return modelImages.stream()
        .filter(image -> Boolean.TRUE.equals(image.getPrimary()))
        .map(ModelImages::getUrl)
        .findFirst()
        .orElseGet(
            () ->
                modelImages.stream()
                    .map(ModelImages::getUrl)
                    .findFirst()
                    .orElse(
                        "https://cdn-content.lumirental.com/cms/1642062661646_CToyotaCorolla2021.png")); // TODO : remove this image
  }

  ModelImageDTO toModelImageDTO(ModelImages modelImage);

  @Mapping(target = "vehicleGroup", ignore = true)
  @Mapping(target = "nameEn", source = "name.en")
  @Mapping(target = "nameAr", source = "name.ar")
  VehicleModel buildVehicleModel(CreateUpdateVehicleModelRequest request);
}
