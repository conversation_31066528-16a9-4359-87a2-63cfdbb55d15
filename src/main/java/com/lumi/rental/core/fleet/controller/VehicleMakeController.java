package com.lumi.rental.core.fleet.controller;

import com.lumi.rental.core.fleet.request.BasePageRequest;
import com.lumi.rental.core.fleet.request.CreateUpdateVehicleMakeRequest;
import com.lumi.rental.core.fleet.response.VehicleMakeResponse;
import com.lumi.rental.core.fleet.service.VehicleMakeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/vehicle-make")
public class VehicleMakeController {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final VehicleMakeService makeService;

  @GetMapping
  public Page<VehicleMakeResponse> getAllVehicleMake(@Valid final BasePageRequest request) {
    log.info(
        "Fetching all vehicle makes with page number {} and page size {}",
        request.getPageNumber(),
        request.getPageSize());
    return makeService.getAllVehicleMake(
        PageRequest.of(request.getPageNumber(), request.getPageSize()));
  }

  @GetMapping("/{id}")
  public VehicleMakeResponse getVehicleMakeById(
      @PathVariable(value = "id") final Integer vehicleMakeId) {
    log.info("Fetching vehicle make with ID: {}", vehicleMakeId);
    return makeService.getVehicleMakeById(vehicleMakeId);
  }

  @PostMapping
  public ResponseEntity<VehicleMakeResponse> createVehicleMake(
      @Valid @RequestBody CreateUpdateVehicleMakeRequest request) {
    log.info("Creating a new vehicle make with request: {}", request);
    VehicleMakeResponse createdVehicleMake = makeService.createVehicleMake(request);
    return new ResponseEntity<>(createdVehicleMake, HttpStatus.CREATED);
  }

  @PutMapping("/{id}")
  public ResponseEntity<VehicleMakeResponse> updateVehicleMake(
      @PathVariable Integer id, @Valid @RequestBody CreateUpdateVehicleMakeRequest request) {
    log.info("Updating vehicle make with ID: {} and request: {}", id, request);
    VehicleMakeResponse updatedVehicleMake = makeService.updateVehicleMake(id, request);
    return updatedVehicleMake != null
        ? new ResponseEntity<>(updatedVehicleMake, HttpStatus.OK)
        : new ResponseEntity<>(HttpStatus.NOT_FOUND);
  }
}
