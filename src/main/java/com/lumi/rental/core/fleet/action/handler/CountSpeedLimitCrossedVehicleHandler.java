package com.lumi.rental.core.fleet.action.handler;

import static com.lumi.rental.core.fleet.enums.VehicleCountType.CROSSED_SPEED_LIMIT_COUNT;

import com.lumi.rental.core.fleet.action.CountVehicleHandler;
import com.lumi.rental.core.fleet.enums.VehicleCountType;
import com.lumi.rental.core.fleet.request.VehicleCountRequest;
import com.lumi.rental.core.fleet.response.VehicleCountResponse;
import com.lumi.rental.core.fleet.service.VehicleTrackerService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CountSpeedLimitCrossedVehicleHandler implements CountVehicleHandler {

  private final VehicleTrackerService vehicleTrackingService;

  @Override
  public VehicleCountResponse execute(VehicleCountRequest request) {
    return vehicleTrackingService.getVehicleStatsCrossedSpeedLimitInSelectedMonth(
        request.getPlateNumbers(), request.getNoOfMonths());
  }

  @Override
  public VehicleCountType countType() {
    return CROSSED_SPEED_LIMIT_COUNT;
  }
}
