package com.lumi.rental.core.fleet.repository.es;

import com.lumi.rental.core.fleet.entity.es.ESVehicleTrackingInfoHistory;
import java.util.List;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;

public interface ESVehicleTrackerHistoryRepository
    extends ElasticsearchRepository<ESVehicleTrackingInfoHistory, String>,
        ESVehicleTrackerHistoryRepositoryCustom {

  List<ESVehicleTrackingInfoHistory> findByPlateNoOrderByRecordDateTimeDesc(String plateNo);
}
