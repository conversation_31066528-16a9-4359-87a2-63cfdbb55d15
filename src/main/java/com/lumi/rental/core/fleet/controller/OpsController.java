package com.lumi.rental.core.fleet.controller;

import com.lumi.rental.core.fleet.entity.Vehicle;
import com.lumi.rental.core.fleet.entity.VehicleModel;
import com.lumi.rental.core.fleet.entity.VehicleOperationalData;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.repository.VehicleModelRepository;
import com.lumi.rental.core.fleet.repository.VehicleOperationalDataRepository;
import com.lumi.rental.core.fleet.repository.VehicleRepository;
import com.lumi.rental.core.fleet.response.BaseResponse;
import com.lumi.rental.core.fleet.service.BranchInfoCache;
import com.lumi.rental.core.fleet.service.SlackNotificationService;
import com.seera.lumi.core.cache.service.CacheService;
import com.seera.lumi.core.fleet.constants.CacheConstants;
import com.seera.lumi.core.fleet.exception.VehicleErrors;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/ops")
public class OpsController {
  private static final Logger log = LoggerFactory.getLogger("application");
  private final VehicleModelRepository modelRepository;
  private final VehicleRepository vehicleRepository;
  private final CacheService cacheService;
  private final BranchInfoCache branchInfoCache;
  private final VehicleOperationalDataRepository operationalDataRepository;
  private final JdbcTemplate jdbcTemplate;
  private final SlackNotificationService slackNotificationService;

  @GetMapping("/cache/flush")
  public ResponseEntity<BaseResponse> flushCache(
      @RequestParam(value = "keyPrefix", required = false) String keyPrefix) {
    String prefix = StringUtils.defaultIfEmpty(keyPrefix, CacheConstants.FLEET_CACHE_PREFIX);
    log.info("Flushing cache keys with prefix: {}", prefix);
    cacheService.clearAllKeys(prefix + "*");
    return ResponseEntity.ok(
        new BaseResponse(
            String.format("Cache keys with prefix '%s' flushed successfully", prefix)));
  }

  @GetMapping("/check-cpu-core")
  public ResponseEntity<BaseResponse> checkCpuCore() {
    int cores = Runtime.getRuntime().availableProcessors();
    return ResponseEntity.ok(new BaseResponse("total-cores:" + cores));
  }

  @GetMapping("/cache/branch/refresh")
  @Scheduled(fixedRate = 30 * 60 * 1000) // 30 minutes in milliseconds
  public ResponseEntity<BaseResponse> refreshBranchCache() {
    log.info("Refreshing branch info cache");
    branchInfoCache.refreshCache();
    return ResponseEntity.ok(new BaseResponse("Branch info cache refreshed successfully"));
  }

  @GetMapping("/cache/branch/info")
  public ResponseEntity<?> getBranchCacheInfo() {
    log.info("Fetching branch info cache");
    return ResponseEntity.ok(branchInfoCache.getBranchInfo());
  }

  @GetMapping("/vehicle/update-model")
  public ResponseEntity<BaseResponse> updateVehicleModel(
      @RequestParam(value = "plateNo") String plateNo,
      @RequestParam(value = "modelId") Integer modelId) {
    log.info("Updating vehicle model for plate-no: {} to model-id: {}", plateNo, modelId);

    VehicleModel model =
        modelRepository
            .findById(modelId)
            .orElseThrow(() -> new BusinessException(VehicleErrors.MODEL_NOT_FOUND));
    // Update vehicle
    Vehicle vehicle =
        vehicleRepository
            .findByPlateNo(plateNo)
            .orElseThrow(() -> new BusinessException(VehicleErrors.VEHICLE_NOT_FOUND));
    vehicle.setModel(model);
    vehicleRepository.saveAndFlush(vehicle);

    // Update operational data
    VehicleOperationalData operationalData =
        operationalDataRepository
            .findByPlateNo(plateNo)
            .orElseThrow(() -> new BusinessException(VehicleErrors.OPERATIONAL_DATA_NOT_FOUND));
    operationalData.setModel(model);
    operationalDataRepository.saveAndFlush(operationalData);

    log.info("Vehicle model updated successfully for plate-no: {}", plateNo);
    return ResponseEntity.ok(new BaseResponse("Vehicle model updated successfully"));
  }

  @GetMapping("/generate-validation-report")
  @Scheduled(cron = "0 0 5 * * *")
  public ResponseEntity<BaseResponse> generateValidationReport() {
    generateAndSendValidationReport();
    return ResponseEntity.ok(new BaseResponse("Validation report generated and sent successfully"));
  }

  @Async
  public void generateAndSendValidationReport() {
    log.info("Generating validation report...");

    List<Map<String, Object>> reportData = new ArrayList<>();

    // Check 1: Vehicles missing in operational data
    int count1 =
        checkDataInconsistencyCount(
            "Vehicles present in Vehicle table but not in VOD",
            "select * from `lumi-core-fleet-service`.vehicle_operational_data vod "
                + "right join `lumi-core-fleet-service`.vehicle v on vod.plate_no = v.plate_no "
                + "where vod.plate_no is null",
            "operational data");
    reportData.add(createReportEntry("Missing Operational Data", count1, "Critical"));

    // Check 2: Vehicles with missing Arabic plate numbers
    int count2 =
        checkDataInconsistencyCount(
            "Vehicles with missing Arabic plate numbers",
            "SELECT * FROM `lumi-core-fleet-service`.vehicle where plate_no_ar is null",
            "plate_no_ar");
    reportData.add(createReportEntry("Missing Arabic Plate Number In Vehicle", count2, "Medium"));

    // Check 3: Vehicles with missing model_id
    int count3 =
        checkDataInconsistencyCount(
            "Vehicles with missing model_id",
            "SELECT * FROM `lumi-core-fleet-service`.vehicle where model_id is null",
            "model_id");
    reportData.add(createReportEntry("Missing Model-ID In Vehicle", count3, "High"));

    // Check 4: Vehicles with missing year
    int count4 =
        checkDataInconsistencyCount(
            "Vehicles with missing year",
            "SELECT * FROM `lumi-core-fleet-service`.vehicle where year is null",
            "year");
    reportData.add(createReportEntry("Missing Year In Vehicle", count4, "Medium"));

    // Check 5: Vehicles with missing color
    int count5 =
        checkDataInconsistencyCount(
            "Vehicles with missing color",
            "SELECT * FROM `lumi-core-fleet-service`.vehicle where color is null",
            "color");
    reportData.add(createReportEntry("Missing Color In Vehicle", count5, "Low"));

    // Check 6: Vehicles with missing chassis_no
    int count6 =
        checkDataInconsistencyCount(
            "Vehicles with missing chassis_no",
            "SELECT * FROM `lumi-core-fleet-service`.vehicle where chassis_no is null",
            "chassis_no");
    reportData.add(createReportEntry("Missing Chassis-Number In Vehicle", count6, "High"));

    // Check 7: Vehicles with missing asset_id
    int count7 =
        checkDataInconsistencyCount(
            "Vehicles with missing asset_id",
            "SELECT * FROM `lumi-core-fleet-service`.vehicle where asset_id is null",
            "asset_id");
    reportData.add(createReportEntry("Missing Asset-ID In Vehicle", count7, "High"));

    // Check 8: Vehicles with missing policy_no
    int count8 =
        checkDataInconsistencyCount(
            "Vehicles with missing policy_no",
            "SELECT * FROM `lumi-core-fleet-service`.vehicle where policy_no is null",
            "policy_no");
    reportData.add(createReportEntry("Missing Policy-Number In Vehicle", count8, "Medium"));

    // Check 9: Vehicles with missing sap_model_name
    int count9 =
        checkDataInconsistencyCount(
            "Vehicles with missing sap_model_name",
            "SELECT * FROM `lumi-core-fleet-service`.vehicle where sap_model_name is null",
            "sap_model_name");
    reportData.add(createReportEntry("Missing SAP-Model-Name In Vehicle", count9, "Medium"));

    // Check 10: VOD with missing model_id
    int count10 =
        checkDataInconsistencyCount(
            "Vehicles_Operational_Data with missing model_id",
            "SELECT * FROM `lumi-core-fleet-service`.vehicle_operational_data where model_id is null",
            "model_id");
    reportData.add(createReportEntry("Missing Model-ID In Ops-Data", count10, "High"));

    // Check 11: VOD with missing service_type_id
    int count11 =
        checkDataInconsistencyCount(
            "Vehicles_Operational_Data with missing service_type_id",
            "SELECT * FROM `lumi-core-fleet-service`.vehicle_operational_data where service_type_id is null",
            "service_type_id");
    reportData.add(createReportEntry("Missing Service-Type-ID In Ops-Data", count11, "High"));

    // Check 13: VOD with missing vehicle_status
    int count13 =
        checkDataInconsistencyCount(
            "Vehicles_Operational_Data with missing vehicle_status",
            "SELECT * FROM `lumi-core-fleet-service`.vehicle_operational_data where vehicle_status is null",
            "vehicle_status");
    reportData.add(createReportEntry("Missing Vehicle-Status In Ops-Data", count13, "Critical"));

    // Check 14: VOD with missing owner_branch_id
    int count14 =
        checkDataInconsistencyCount(
            "Vehicles_Operational_Data with missing owner_branch_id",
            "SELECT * FROM `lumi-core-fleet-service`.vehicle_operational_data where owner_branch_id is null",
            "owner_branch_id");
    reportData.add(createReportEntry("Missing Owner-Branch-ID In Ops-Data", count14, "High"));

    // Check 15: VOD with missing current_location_id
    int count15 =
        checkDataInconsistencyCount(
            "Vehicles_Operational_Data with missing current_location_id",
            "SELECT * FROM `lumi-core-fleet-service`.vehicle_operational_data where current_location_id is null",
            "current_location_id");
    reportData.add(createReportEntry("Missing Current-Location-ID In Ops-Data", count15, "High"));

    // Check 16: VOD with missing odometer_reading
    int count16 =
        checkDataInconsistencyCount(
            "Vehicles_Operational_Data with missing odometer_reading",
            "SELECT * FROM `lumi-core-fleet-service`.vehicle_operational_data where odometer_reading is null",
            "odometer_reading");
    reportData.add(createReportEntry("Missing Odometer-Reading In Ops-Data", count16, "Medium"));

    // Check 17: VOD with missing fuel_level
    int count17 =
        checkDataInconsistencyCount(
            "Vehicles_Operational_Data with missing fuel_level",
            "SELECT * FROM `lumi-core-fleet-service`.vehicle_operational_data where fuel_level is null",
            "fuel_level");
    reportData.add(createReportEntry("Missing Fuel-Level In Ops-Data", count17, "Medium"));

    // Generate and send report
    String report = generateTabularReport(reportData);
    sendReportToSlack(report);
  }

  private int checkDataInconsistencyCount(String queryName, String query, String fieldName) {
    log.info("Running scheduled check for {}", queryName);

    List<Map<String, Object>> results = jdbcTemplate.queryForList(query);

    if (!results.isEmpty()) {
      StringBuilder plateNumbers =
          new StringBuilder(String.format("Missing %s for plate numbers: ", fieldName));
      for (int i = 0; i < Math.min(results.size(), 10); i++) {
        plateNumbers.append(results.get(i).get("plate_no")).append(", ");
      }

      if (results.size() > 10) {
        plateNumbers.append("and ").append(results.size() - 10).append(" more");
      } else if (!results.isEmpty()) {
        plateNumbers.delete(plateNumbers.length() - 2, plateNumbers.length());
      }

      log.error(
          "[DATA-INCONSISTENCY] {} Found {} entries. plate numbers: {}",
          queryName,
          results.size(),
          plateNumbers);
    } else {
      log.info("No inconsistencies found for {}", queryName);
    }

    return results.size();
  }

  private Map<String, Object> createReportEntry(String queryName, int count, String priority) {
    Map<String, Object> entry = new LinkedHashMap<>();
    entry.put("queryName", queryName);
    entry.put("count", count);
    entry.put("priority", priority);
    entry.put("status", count > 0 ? "FAILED" : "PASSED");
    return entry;
  }

  private String generateTabularReport(List<Map<String, Object>> reportData) {
    StringBuilder report = new StringBuilder();
    report.append("🚗 LUMI FLEET DATA VALIDATION REPORT\n");
    report.append("=====================================\n\n");

    // Header
    report.append(
        String.format(
            "%-40s | %-8s | %-10s | %-8s\n", "VALIDATION CHECK", "COUNT", "PRIORITY", "STATUS"));
    report.append("─".repeat(75)).append("\n");

    int totalIssues = 0;
    int failedChecks = 0;

    // Data rows
    for (Map<String, Object> entry : reportData) {
      String queryName = (String) entry.get("queryName");
      int count = (Integer) entry.get("count");
      String priority = (String) entry.get("priority");
      String status = (String) entry.get("status");

      // Truncate query name if too long
      if (queryName.length() > 100) {
        queryName = queryName.substring(0, 35) + "...";
      }

      report.append(
          String.format("%-40s | %-8d | %-10s | %-8s\n", queryName, count, priority, status));

      totalIssues += count;
      if (count > 0) {
        failedChecks++;
      }
    }

    report.append("─".repeat(75)).append("\n");
    report.append(
        String.format(
            "SUMMARY: %d Total Issues | %d Failed Checks | %d Passed Checks\n",
            totalIssues, failedChecks, reportData.size() - failedChecks));

    return report.toString();
  }

  private void sendReportToSlack(String report) {
    try {
      log.info("Sending validation report to Slack...");
      log.info("\n{}", report);

      slackNotificationService.sendNotification(report);

    } catch (Exception e) {
      log.error("Failed to send report to Slack", e);
    }
  }
}
