package com.lumi.rental.core.fleet.service;

import static org.apache.commons.lang3.StringUtils.trim;

import com.lumi.rental.core.fleet.dto.DocumentTypeDTO;
import com.lumi.rental.core.fleet.entity.DocumentType;
import com.lumi.rental.core.fleet.entity.VehicleDocument;
import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.mapper.VehicleDocumentMapper;
import com.lumi.rental.core.fleet.mapper.VehicleDocumentTypeMapper;
import com.lumi.rental.core.fleet.repository.DocumentTypeRepository;
import com.lumi.rental.core.fleet.repository.VehicleDocumentRepository;
import com.lumi.rental.core.fleet.repository.specification.DocumentTypeSpecification;
import com.lumi.rental.core.fleet.request.CreateUpdateVehicleDocument;
import com.lumi.rental.core.fleet.request.DocumentTypeSearchRequestDTO;
import com.lumi.rental.core.fleet.response.DocumentTypeResponse;
import com.lumi.rental.core.fleet.response.VehicleDocumentResponse;
import com.lumi.rental.core.fleet.util.PageUtil;
import com.seera.lumi.core.fleet.dto.DocumentUrlResponseDTO;
import com.seera.lumi.core.fleet.exception.VehicleErrors;
import com.seera.lumi.core.fleet.service.ContentService;
import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/** Service for managing document types and vehicle documents */
@Service
@RequiredArgsConstructor
@Transactional
public class DocumentService {

  private final VehicleService vehicleService;
  private final ContentService contentService;
  private final DocumentUrlSigner documentUrlSigner;
  private final VehicleDocumentMapper documentMapper;
  private final VehicleDocumentTypeMapper documentTypeMapper;
  private final DocumentTypeRepository documentTypeRepository;
  private final VehicleDocumentRepository vehicleDocumentRepository;

  // Document Type Operations
  @Transactional(readOnly = true)
  public Page<DocumentTypeDTO> listDocumentTypes(DocumentTypeSearchRequestDTO requestDTO) {
    return documentTypeRepository
        .findAll(new DocumentTypeSpecification(requestDTO), PageUtil.getPageable(requestDTO))
        .map(documentTypeMapper::buildDocumentTypeDTO);
  }

  public DocumentTypeDTO createDocumentType(DocumentTypeDTO documentTypeDTO) {
    return Optional.ofNullable(documentTypeDTO)
        .map(documentTypeMapper::toEntity)
        .map(documentTypeRepository::saveAndFlush)
        .map(documentTypeMapper::buildDocumentTypeDTO)
        .orElseThrow(() -> new BusinessException(BaseError.INVALID_ARG_VALUE, "DocumentType"));
  }

  public DocumentTypeDTO updateDocumentType(DocumentTypeDTO documentTypeDTO) {
    return Optional.ofNullable(documentTypeDTO)
        .map(documentTypeMapper::toEntity)
        .flatMap(this::partialUpdateDocumentType)
        .map(documentTypeMapper::buildDocumentTypeDTO)
        .orElseThrow(() -> new BusinessException(BaseError.INVALID_ARG_VALUE, "DocumentType"));
  }

  private Optional<DocumentType> partialUpdateDocumentType(DocumentType documentType) {
    return documentTypeRepository
        .findById(documentType.getId())
        .map(
            existingType -> {
              documentTypeMapper.partialUpdateDocumentType(existingType, documentType);
              return documentTypeRepository.save(existingType);
            });
  }

  // Vehicle Document Operations
  @Transactional(readOnly = true)
  public List<VehicleDocumentResponse> search(String plateNumber) {
    // Input validation
    String trimmedPlateNumber = trim(plateNumber);
    if (StringUtils.isEmpty(trimmedPlateNumber)) {
      throw new BusinessException(BaseError.INVALID_ARG_VALUE, "PlateNo");
    }

    // Fetch documents with pagination to avoid memory issues
    return vehicleDocumentRepository.findByPlateNo(trimmedPlateNumber).stream()
        .parallel() // Parallel processing for better performance
        .map(
            doc -> {
              // Sign URL and map to response in one pass
              String signedUrl = documentUrlSigner.sign(doc.getUrl());
              doc.setUrl(signedUrl);
              return documentMapper.buildVehicleDocumentResponse(doc);
            })
        .collect(Collectors.toList());
  }

  public DocumentTypeResponse createDocument(final CreateUpdateVehicleDocument documentDTO) {
    // Ensure the vehicle and DocumentType exists, else throw an exception
    vehicleService
        .findByPlateNo(documentDTO.getPlateNo())
        .orElseThrow(() -> new BusinessException(VehicleErrors.VEHICLE_NOT_FOUND));
    DocumentType documentType =
        documentTypeRepository
            .findById(documentDTO.getTypeId())
            .orElseThrow(() -> new BusinessException(BaseError.INVALID_ARG_VALUE, "DocumentType"));
    VehicleDocument document = documentMapper.toEntity(documentDTO).setType(documentType);
    vehicleDocumentRepository.saveAndFlush(document);
    return documentMapper.toDocumentTypeResponse(document);
  }

  public DocumentTypeResponse updateDocument(Integer id, CreateUpdateVehicleDocument documentDTO) {
    VehicleDocument existingDoc =
        vehicleDocumentRepository
            .findById(id)
            .orElseThrow(() -> new BusinessException(BaseError.NOT_FOUND, "VehicleDocument"));
    documentMapper.partialUpdate(existingDoc, documentDTO);
    vehicleDocumentRepository.saveAndFlush(existingDoc);
    return documentMapper.toDocumentTypeResponse(existingDoc);
  }

  // File Upload Operations
  public DocumentUrlResponseDTO upload(String plateNumber, MultipartFile file) throws IOException {
    if (StringUtils.isEmpty(trim(plateNumber)) || file == null || file.getBytes().length == 0) {
      throw new BusinessException(BaseError.INVALID_ARG_VALUE, "PlateNumber or File");
    }
    String fileUrl = contentService.uploadPrivate(plateNumber, file);
    return DocumentUrlResponseDTO.builder().url(fileUrl).build();
  }
}
