package com.lumi.rental.core.fleet.mapper;

import com.lumi.rental.core.fleet.dto.LineOfBusinessDTO;
import com.lumi.rental.core.fleet.dto.VehicleStatusDTO;
import com.lumi.rental.core.fleet.entity.Vehicle;
import com.lumi.rental.core.fleet.entity.VehicleGroup;
import com.lumi.rental.core.fleet.entity.VehicleOperationalData;
import com.lumi.rental.core.fleet.enums.*;
import com.lumi.rental.core.fleet.response.VehicleAvailabilityResponse;
import com.lumi.rental.core.fleet.response.VehicleResponseV3;
import com.lumi.rental.core.fleet.service.BranchInfoCache;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = "spring",
    uses = {VehicleModelMapper.class, FuelMapper.class, BranchInfoCache.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VehicleAvailabilityMapper {

  @Mapping(target = "plateNo", source = "plateNo")
  @Mapping(target = "model", source = "model")
  @Mapping(target = "location", source = "currentLocationId")
  @Mapping(target = "fuelLevelInfo", source = "fuelLevel")
  @Mapping(target = "fuelLevel", ignore = true)
  @Mapping(target = "odometerReading", source = "odometerReading")
  @Mapping(target = "vehicleStatus", source = "opsData")
  @Mapping(target = "lineOfBusiness", source = "opsData")
  VehicleResponseV3 buildVehicleResponseV3(VehicleOperationalData opsData);

  @Mapping(target = "fuelLevelInfo", source = "fuelLevel")
  VehicleAvailabilityResponse buildVehicleAvailabilityResponse(
      VehicleOperationalData operationalData);

  default String getVehicleGroup(VehicleGroup vehicleGroup) {
    return vehicleGroup.getCode();
  }

  @Mapping(target = "model", ignore = true)
  @Mapping(target = "modelYear", source = "vehicle.year")
  void populateVehicleInfo(@MappingTarget VehicleAvailabilityResponse response, Vehicle vehicle);

  default VehicleStatusDTO buildVehicleStatusDTO(VehicleOperationalData opsData) {
    return new VehicleStatusDTO(
        opsData.getVehicleStatus().getStatusId(),
        opsData.getVehicleStatus().getSubStatusId(),
        opsData.getVehicleStatus().getDate());
    //    if (opsData.getVehicleStatus().getStatusId().equals(StatusType.NEED_PREPARATION.getId()))
    // {
    //      return new VehicleStatusDTO(
    //          StatusType.NEED_PREPARATION,
    //          NeedPreparationReason.fromId(opsData.getVehicleStatus().getSubStatusId()),
    //          opsData.getVehicleStatus().getDate());
    //    } else if
    // (opsData.getVehicleStatus().getStatusId().equals(StatusType.OUT_OF_SERVICE.getId())) {
    //      return new VehicleStatusDTO(
    //          StatusType.OUT_OF_SERVICE,
    //          YaqeenOutOfServiceReason.fromId(opsData.getVehicleStatus().getSubStatusId()),
    //          opsData.getVehicleStatus().getDate());
    //    }
  }

  default LineOfBusinessDTO buildLineOfBusinessDTO(VehicleOperationalData opsData) {
    return new LineOfBusinessDTO(
        ServiceType.fromId(opsData.getServiceTypeId()),
        SubServiceType.fromId(opsData.getSubServiceTypeId()));
  }
}
