package com.lumi.rental.core.fleet.service;

import com.lumi.rental.core.fleet.dto.VehicleOperationalDTO;
import com.lumi.rental.core.fleet.dto.VehicleStatusInfo;
import com.lumi.rental.core.fleet.entity.NonRevenueMovement;
import com.lumi.rental.core.fleet.entity.VehicleOperationalData;
import com.lumi.rental.core.fleet.enums.NonRevenueMovementReason;
import com.lumi.rental.core.fleet.enums.NonRevenueMovementStatus;
import com.lumi.rental.core.fleet.enums.StatusType;
import com.lumi.rental.core.fleet.enums.YaqeenOutOfServiceReason;
import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.mapper.ReservationMapper;
import com.lumi.rental.core.fleet.repository.NonRevenueMovementRepository;
import com.lumi.rental.core.fleet.request.CloseNRMRequest;
import com.lumi.rental.core.fleet.request.CreateNRMRequest;
import com.lumi.rental.core.fleet.response.NRMResponse;
import com.lumi.rental.core.fleet.util.DateUtil;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import javax.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class NRMService {

  private final VehicleAvailabilityService availabilityService;
  private final UserService userService;

  private final NonRevenueMovementRepository nrmRepository;
  private final ReservationMapper reservationMapper;

  public NRMResponse getNRM(Integer nrmId) {
    return reservationMapper.toResponse(
        nrmRepository
            .findById(nrmId)
            .orElseThrow(
                () ->
                    new BusinessException(
                        BaseError.BAD_REQUEST_WITH_REASON,
                        "Non-Revenue Movement with ID " + nrmId + " not found")));
  }

  public Page<NRMResponse> findAllNRMLogsByPlateNo(String plateNo, Pageable pageable) {
    return nrmRepository
        .findAllByPlateNo(plateNo, pageable)
        .map(reservationMapper::toResponse)
        .map(
            response -> {
              Optional.ofNullable(response.getDriverId())
                  .filter(StringUtils::isNotBlank)
                  .map(userService::findUserBySuccessFactorId)
                  .ifPresent(
                      user -> response.setDriverId(user.getFirstName() + " " + user.getLastName()));
              return response;
            });
  }

  public NRMResponse createNRM(final CreateNRMRequest request, String allowedBranchIds) {
    // Step 1: Get Vehicle Operational Data
    VehicleOperationalData vehicleOperationalData =
        availabilityService.getVehicleOperationalData(request.getPlateNo());

    validateOpenNRMRequest(request, vehicleOperationalData, allowedBranchIds);

    NonRevenueMovementReason nrmReason = NonRevenueMovementReason.fromId(request.getReasonId());

    // Step 2: Create and Save NonRevenueMovement
    NonRevenueMovement nonRevenueMovement = createAndSaveNRM(request, vehicleOperationalData);

    // Step 3: Update Vehicle Status to NRM_OPENED
    VehicleStatusInfo vehicleStatusInfo =
        new VehicleStatusInfo(StatusType.NRM_OPENED.getId(), nrmReason.getId());
    vehicleOperationalData.setVehicleStatus(vehicleStatusInfo);
    availabilityService.updateVehicleOperationData(vehicleOperationalData);

    // Return NRM Response
    return reservationMapper.toResponse(nonRevenueMovement);
  }

  @Transactional
  public NRMResponse closeNRM(CloseNRMRequest request, String allowedBranchIds) {
    Optional<NonRevenueMovement> nrmOptional = nrmRepository.findById(request.getNrmId());
    if (nrmOptional.isEmpty()) {
      throw new BusinessException(BaseError.BAD_REQUEST_WITH_REASON, "NRM-Id not found");
    } else if (NonRevenueMovementStatus.fromId(nrmOptional.get().getStatusId())
        == NonRevenueMovementStatus.CLOSED) {
      throw new BusinessException(BaseError.BAD_REQUEST_WITH_REASON, "NRM already closed");
    }
    NonRevenueMovement openedNrm = nrmOptional.get();

    // Step 1: Get Vehicle Operational Data
    VehicleOperationalData vehicleOperationalData =
        availabilityService.getVehicleOperationalData(openedNrm.getPlateNo());

    validateCloseNRMRequest(request, vehicleOperationalData);
    List<Integer> allowedBranchIdList =
        Arrays.stream(allowedBranchIds.split(",")).map(Integer::parseInt).toList();
    if (!allowedBranchIdList.contains(openedNrm.getCheckoutData().getBranchId())
        || !allowedBranchIdList.contains(openedNrm.getCheckinData().getBranchId())) {
      throw new BusinessException(
          BaseError.BAD_REQUEST_WITH_REASON, "You are not allowed to close NRM for this branch");
    }
    // Step 2: Find and Update NonRevenueMovement
    NonRevenueMovement nrm = findAndUpdateNRM(openedNrm, request, NonRevenueMovementStatus.CLOSED);

    // Step 3: Update VehicleOperationalData
    if (nrm.getReasonId().equals(NonRevenueMovementReason.WORKSHOP_TRANSFER.getId())) {
      VehicleStatusInfo vehicleStatusInfo =
          new VehicleStatusInfo(
              StatusType.OUT_OF_SERVICE.getId(), YaqeenOutOfServiceReason.AT_WORKSHOP.getId());
      vehicleOperationalData.setVehicleStatus(vehicleStatusInfo);
    } else {
      VehicleStatusInfo vehicleStatusInfo = new VehicleStatusInfo(StatusType.READY.getId());
      vehicleOperationalData.setVehicleStatus(vehicleStatusInfo);
    }
    vehicleOperationalData.setFuelLevel(request.getFuelLevel());
    vehicleOperationalData.setOdometerReading(request.getOdometerReading());
    vehicleOperationalData.setCurrentLocationId(nrm.getCheckinData().getBranchId());
    availabilityService.updateVehicleOperationData(vehicleOperationalData);

    // Return NRM Response
    return reservationMapper.toResponse(nrm);
  }

  private void validateCloseNRMRequest(
      CloseNRMRequest request, VehicleOperationalData vehicleOperationalData) {
    if (!StatusType.NRM_OPENED
        .getId()
        .equals(vehicleOperationalData.getVehicleStatus().getStatusId())) {
      throw new BusinessException(BaseError.BAD_REQUEST_WITH_REASON, "NRM not opened");
    }
    if (request.getOdometerReading() < vehicleOperationalData.getOdometerReading()) {
      throw new BusinessException(
          BaseError.BAD_REQUEST_WITH_REASON,
          "Current Odometer reading should be greater or equal to previous reading");
    }
    if (request.getFuelLevel() > 4) {
      throw new BusinessException(BaseError.BAD_REQUEST_WITH_REASON, "Fuel Level should be <= 4");
    }
  }

  private void validateOpenNRMRequest(
      CreateNRMRequest request,
      VehicleOperationalData vehicleOperationalData,
      String allowedBranchIds) {
    List<Integer> allowedBranchIdList =
        Arrays.stream(allowedBranchIds.split(",")).map(Integer::parseInt).toList();
    if (!allowedBranchIdList.contains(request.getCheckoutBranch())
        || !allowedBranchIdList.contains(request.getCheckinBranch())) {
      throw new BusinessException(
          BaseError.BAD_REQUEST_WITH_REASON, "You are not allowed to open NRM for this branch");
    }
    if (StatusType.NRM_OPENED
        .getId()
        .equals(vehicleOperationalData.getVehicleStatus().getStatusId())) {
      throw new BusinessException(BaseError.BAD_REQUEST_WITH_REASON, "NRM already opened");
    }
    // TODO : we can check if vehicle can move from current status to NRM_OPENED
    if (request.getCheckoutBranch().equals(request.getCheckinBranch())) {
      throw new BusinessException(
          BaseError.BAD_REQUEST_WITH_REASON, "Checkout and Checkin branch same");
    }
  }

  private NonRevenueMovement createAndSaveNRM(
      CreateNRMRequest request, VehicleOperationalData vehicleOperationalData) {
    NonRevenueMovement nonRevenueMovement = reservationMapper.toNonRevenueMovement(request);
    nonRevenueMovement.setStatusId(NonRevenueMovementStatus.OPEN.getId());
    nonRevenueMovement.setCheckoutData(
        buildCheckOutVehicleOperationalDTO(request, vehicleOperationalData));
    nonRevenueMovement.setCheckinData(buildCheckInVehicleOperationalDTO(request));
    return nrmRepository.saveAndFlush(nonRevenueMovement);
  }

  private VehicleOperationalDTO buildCheckInVehicleOperationalDTO(CreateNRMRequest request) {
    return VehicleOperationalDTO.builder().branchId(request.getCheckinBranch()).build();
  }

  private VehicleOperationalDTO buildCheckOutVehicleOperationalDTO(
      CreateNRMRequest request, VehicleOperationalData vehicleOperationalData) {
    return VehicleOperationalDTO.builder()
        .date(DateUtil.getCurrentDateTimeInEpoch())
        .branchId(request.getCheckoutBranch())
        .fuelLevel(vehicleOperationalData.getFuelLevel())
        .odometerReading(vehicleOperationalData.getOdometerReading())
        .remarks(request.getCheckoutRemarks())
        .build();
  }

  private NonRevenueMovement findAndUpdateNRM(
      NonRevenueMovement openedNrm,
      CloseNRMRequest closeNRMRequest,
      NonRevenueMovementStatus status) {

    openedNrm.setStatusId(status.getId());
    openedNrm.getCheckinData().setDate(DateUtil.getCurrentDateTimeInEpoch());
    openedNrm.getCheckinData().setOdometerReading(closeNRMRequest.getOdometerReading());
    openedNrm.getCheckinData().setFuelLevel(closeNRMRequest.getFuelLevel());
    openedNrm.getCheckinData().setRemarks(closeNRMRequest.getRemarks());
    return nrmRepository.saveAndFlush(openedNrm);
  }
}
