package com.lumi.rental.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDate;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VehicleDetailSyncEventDTO {

  private String plateNo;
  private String assetId;
  private String make;
  private String model;
  private String modelYear;
  private String color;
  private String deactivationOn;
  private Double nbv;
  private Double adv;
  private Double purchasePrice;

  @Field(type = FieldType.Date, format = DateFormat.date, pattern = "yyyy-MM-dd")
  private LocalDate purchaseDate;
}
