package com.lumi.rental.core.fleet.controller;

import static java.util.Objects.nonNull;
import static org.hibernate.internal.util.StringHelper.isNotEmpty;
import static org.springframework.http.ResponseEntity.ok;

import com.lumi.rental.core.fleet.api.agreement.resp.AgreementInspectionUpdateResponse;
import com.lumi.rental.core.fleet.dto.InspectionMetadataDTO;
import com.lumi.rental.core.fleet.entity.InspectionReport;
import com.lumi.rental.core.fleet.enums.InspectionReportType;
import com.lumi.rental.core.fleet.mapper.InspectionMapper;
import com.lumi.rental.core.fleet.request.BasePageRequest;
import com.lumi.rental.core.fleet.request.CompleteInspectionRequest;
import com.lumi.rental.core.fleet.request.InitiateInspectionRequest;
import com.lumi.rental.core.fleet.response.InspectionResponse;
import com.lumi.rental.core.fleet.response.InspectionResponse.AgreementCheckinResponse;
import com.lumi.rental.core.fleet.service.InspectionService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/inspection")
public class InspectionController {

  private final InspectionService inspectionService;
  private final InspectionMapper inspectionMapper;

  @GetMapping("/plateNo")
  public ResponseEntity<InspectionResponse> getLatestInspectionByPlateNo(
      @RequestParam String plateNo) {
    InspectionReport latestReport = inspectionService.getLatestInspectionByPlateNo(plateNo);
    return ok(inspectionMapper.buildInspectionResponse(latestReport));
  }

  @GetMapping("/vehicle/logs")
  public ResponseEntity<?> getInspectionLogsByPlateNo(
      @RequestParam(value = "plateNo") String plateNo, @Valid final BasePageRequest request) {
    return ok(
        inspectionService.getInspectionLogsByPlateNo(
            plateNo, PageRequest.of(request.getPageNumber(), request.getPageSize())));
  }

  @GetMapping("/{id}")
  public ResponseEntity<InspectionResponse> getInspectionReportById(
      @PathVariable(value = "id") final Integer inspectionId) {
    InspectionReport inspectionReport = inspectionService.getInspectionReportById(inspectionId);
    return ok(inspectionMapper.buildInspectionResponse(inspectionReport));
  }

  @GetMapping("/vehicle/search")
  public ResponseEntity<?> searchVehicleForInspection(
      @RequestParam(value = "inspectionReportTypeId") InspectionReportType inspectionReportType,
      @RequestParam(value = "plateNo", required = false) String plateNo,
      @RequestParam(value = "branchId") Integer branchId,
      @Valid final BasePageRequest request) {
    return ok(
        inspectionService.searchVehicleForInspection(
            plateNo,
            branchId,
            inspectionReportType,
            PageRequest.of(request.getPageNumber(), request.getPageSize())));
  }

  @PostMapping("/initiated")
  public ResponseEntity<InspectionResponse> initiateInspection(
      @Valid @RequestBody InitiateInspectionRequest request) {
    return new ResponseEntity<>(inspectionService.initiateInspection(request), HttpStatus.CREATED);
  }

  @PutMapping("/{id}")
  public ResponseEntity<InspectionResponse> updateInspectionData(
      @PathVariable("id") final Integer inspectionId,
      @RequestBody @Valid InspectionMetadataDTO metadataDTO) {
    InspectionReport inspectionReport =
        inspectionService.updateInspectionData(inspectionId, metadataDTO);
    return ok(inspectionMapper.buildInspectionResponse(inspectionReport));
  }

  @PutMapping("/{id}/modify")
  public ResponseEntity<InspectionResponse> modifyInspectionData(
      @PathVariable("id") final Integer inspectionId,
      @RequestBody @Valid InspectionMetadataDTO metadataDTO) {
    InspectionReport inspectionReport =
        inspectionService.modifyInspectionData(inspectionId, metadataDTO);
    return ok(inspectionMapper.buildInspectionResponse(inspectionReport));
  }

  @PostMapping("/{id}/saveforlater")
  public ResponseEntity<InspectionResponse> saveForLaterInspectionUsingId(
      @PathVariable("id") final Integer inspectionId) {
    InspectionReport inspectionReport = inspectionService.saveForLater(inspectionId);
    return ok(inspectionMapper.buildInspectionResponse(inspectionReport));
  }

  @PostMapping("/{id}/completed")
  public ResponseEntity<InspectionResponse> completeInspectionUsingId(
      @PathVariable("id") final Integer inspectionId,
      @RequestParam(value = "referenceId", required = false) final String referenceId,
      @RequestBody @Valid InspectionMetadataDTO metadataDTO) {
    InspectionReport inspectionReport =
        inspectionService.completeInspection(inspectionId, referenceId, metadataDTO);
    return ok(notifyAgreementServiceAndMapResponse(inspectionReport));
  }

  @PostMapping("/completed")
  public ResponseEntity<InspectionResponse> completeInspection(
      @RequestBody @Valid CompleteInspectionRequest request) {
    InspectionReport inspectionReport = inspectionService.completeInspection(request);
    return ok(notifyAgreementServiceAndMapResponse(inspectionReport));
  }

  private InspectionResponse notifyAgreementServiceAndMapResponse(
      InspectionReport inspectionReport) {
    AgreementInspectionUpdateResponse agreementResponse = null;
    if ("AGREEMENT".equalsIgnoreCase(inspectionReport.getReferenceType())) {
      agreementResponse = inspectionService.notifyAgreementService(inspectionReport);
    }
    InspectionResponse inspectionResponse =
        inspectionMapper.buildInspectionResponse(inspectionReport);
    if (nonNull(agreementResponse) && isNotEmpty(agreementResponse.getMessage())) {
      inspectionResponse.setAgreementResponse(
          new AgreementCheckinResponse(
              agreementResponse.getMessage(), agreementResponse.getClosed()));
    }
    return inspectionResponse;
  }
}
