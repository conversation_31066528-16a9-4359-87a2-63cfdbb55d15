package com.lumi.rental.core.fleet.repository;

import com.lumi.rental.core.fleet.entity.MaintenanceLog;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface MaintenanceLogRepository
    extends JpaRepository<MaintenanceLog, Long>, JpaSpecificationExecutor<MaintenanceLog> {

  List<MaintenanceLog> findByPlateNo(String plateNo);

  Optional<MaintenanceLog> findFirstByPlateNoOrderByRecordDateDesc(String plateNo);

  //    @EntityGraph("maintenance-log-all-relation")
  Optional<MaintenanceLog> findByPlateNoAndRecordDate(String plateNo, LocalDateTime recordDate);

  @Query(
      "SELECT ml.plateNo as plateNo, UNIX_TIMESTAMP(MAX(ml.recordDate)) as recordDate FROM MaintenanceLog ml WHERE ml.plateNo IN :plateNos GROUP BY ml.plateNo")
  List<Map<String, Object>> findLatestMaintenanceDateForPlateNos(
      @Param("plateNos") List<String> plateNos);

  default Map<String, Long> findLatestMaintenanceDateByPlateNos(Set<String> plateNos) {
    return plateNos == null || plateNos.isEmpty()
        ? Collections.emptyMap()
        : findLatestMaintenanceDateForPlateNos(new ArrayList<>(plateNos)).stream()
            .collect(
                Collectors.toMap(
                    map -> (String) map.get("plateNo"),
                    map -> ((Number) map.get("recordDate")).longValue(),
                    (v1, v2) -> v1,
                    LinkedHashMap::new));
  }
  //    @Query(value = """
  //        SELECT maintenance.*
  //        FROM   core_fleet.maintenance_log maintenance
  //               INNER JOIN (SELECT plate_no,
  //                                  Max(record_date) AS date
  //                           FROM   core_fleet.maintenance_log
  //                           WHERE  plate_no in (:plateNos)
  //                           GROUP  BY plate_no) latest_log
  //                       ON ( latest_log.plate_no = maintenance.plate_no
  //                            AND latest_log.record_date = maintenance.record_date )
  //        GROUP  BY maintenance.plate_no,
  //                  maintenance.date;
  //        """, nativeQuery = true)
  //    List<MaintenanceLog> getLastMaintenanceByPlateNos(@Param("plateNos") Set<String> plateNos);

  //    @Query(value = """
  //        SELECT maintenance.*
  //        FROM   core_fleet.maintenance_log maintenance
  //               INNER JOIN (SELECT plate_no,
  //                                  Max(date) AS date
  //                           FROM   core_fleet.maintenance_log
  //                                  INNER JOIN  core_fleet.maintenance_log_type log_Type
  //                                    ON (log_Type.log_id = maintenance_log.id)
  //                           WHERE  plate_no in (:plateNos)
  //                                  AND log_Type.maintenance_type_id = :typeId
  //                           GROUP  BY plate_no) latest_log
  //                       ON ( latest_log.plate_no = maintenance.plate_no
  //                            AND latest_log.date = maintenance.date )
  //        GROUP  BY maintenance.plate_no,
  //                  maintenance.date
  //        """, nativeQuery = true)
  //    List<MaintenanceLog> getLastMaintenanceByPlateNosAndMaintenanceTypeId(@Param("plateNos")
  // Set<String> plateNos,@Param("typeId") Long typeId);

  //    @Query(value = """
  //
  //              select IFNULL(ml2.id, CAST(-1*9999999*RAND() AS SIGNED) ) as id, pt.plate_no,
  // ml2.date, ml2.remarks, ml2.km, ml2.order_id
  //              from core_fleet.maintenance_log ml2
  //              right join (
  //             select ml.id as id, plate_no , MAX(`date`) as latestdate from
  // core_fleet.maintenance_log ml
  //             group by plate_no ) as t1 on ml2.plate_no = t1.plate_no and ml2.date=t1.latestdate
  //            right join core_fleet.plateno_tenant pt on pt.plate_no =t1.plate_no and pt.tenant_id
  // = :tenantId
  //             where (:plateNo IS NULL or pt.plate_no like %:plateNo%)
  //                              """,
  //            countQuery = """
  //          select count(pt.plate_no) from core_fleet.plateno_tenant pt
  //          where pt.tenant_id =:tenantId
  //            and (:plateNo IS NULL or pt.plate_no like %:plateNo%)
  //                    """,
  //            nativeQuery = true)
  //    Page<MaintenanceLog> getLastMaintenanceLogs(int tenantId, @Param("plateNo") String plateNo,
  // Pageable pageable);

  //          select IFNULL(t.id, 0 ) as id, t.plate_no, ml2.date, ml2.remarks, ml2.km, ml2.order_id
  //  from (
  //          select IFNULL(ml.id, 0 ) as id, pt.plate_no, max(ml.date) as date, ml.remarks, ml.km,
  // ml.order_id
  //                    from core_fleet.maintenance_log ml
  //                    right join core_fleet.plateno_tenant pt on pt.plate_no =ml.plate_no
  //                      GROUP BY pt.plate_no, pt.tenant_id HAVING pt.tenant_id =:tenantId
  //                      and (:plateNo IS NULL or pt.plate_no like %:plateNo%)) as t
  //                  left join core_fleet.maintenance_log ml2 on t.date = ml2.date and t.plate_no =
  // ml2.plate_no

}
