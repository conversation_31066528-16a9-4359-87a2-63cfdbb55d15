package com.lumi.rental.core.fleet.repository;

import com.lumi.rental.core.fleet.entity.NonRevenueMovement;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface NonRevenueMovementRepository extends JpaRepository<NonRevenueMovement, Integer> {

  Page<NonRevenueMovement> findAllByPlateNo(String plateNo, Pageable pageable);
}
