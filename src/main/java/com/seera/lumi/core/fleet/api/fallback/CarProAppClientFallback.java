package com.seera.lumi.core.fleet.api.fallback;

import com.seera.lumi.core.fleet.api.CarProAppClient;
import feign.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class CarProAppClientFallback implements FallbackFactory<CarProAppClient> {

  private static final Logger log = LoggerFactory.getLogger("application");

  @Override
  public CarProAppClient create(Throwable cause) {
    log.error("Unable to get response from car pro app:", cause);
    return new CarProAppClient() {

      @Override
      public Response downloadFile(String appName, String prgName, String arguments) {
        throw new RuntimeException(cause);
      }
    };
  }
}
