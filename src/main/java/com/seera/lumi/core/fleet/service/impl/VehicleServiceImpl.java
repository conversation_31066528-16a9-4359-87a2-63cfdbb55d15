package com.seera.lumi.core.fleet.service.impl;

import com.lumi.rental.core.fleet.repository.es.ESVehicleRepository;
import com.seera.lumi.core.fleet.dto.ESDeepSearchRequestDTO;
import com.seera.lumi.core.fleet.dto.ESDeepSearchResponse;
import com.seera.lumi.core.fleet.dto.SortRequestDTO;
import com.seera.lumi.core.fleet.entity.opensearch.ESVehicleData;
import com.seera.lumi.core.fleet.service.VehicleService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class VehicleServiceImpl implements VehicleService {

  //  private final VehicleRepository vehicleRepository;
  //  private final VehicleFinancialInfoRepository vehicleFinancialInfoRepository;

  //  private final VehicleResponseMapper vehicleResponseMapper;
  //  private final VehicleTrackerService vehicleTrackingService;
  private final ESVehicleRepository esVehicleRepository;

  //  private final CountVehicleHandlerFactory countVehicleHandlerFactory;

  //  @Override
  //  public VehicleResponse getVehicleByPlateNumber(String plateNumber) {
  //    Vehicle vehicle = vehicleRepository.findByPlateNo(plateNumber)
  //        .orElseThrow(() -> new BusinessException(VehicleErrors.VEHICLE_NOT_FOUND));
  //    VehicleFinancialInfo vehicleFinancialInfo = vehicleFinancialInfoRepository.findByPlateNo(
  //        plateNumber).orElse(null);
  //    VehicleLiveTrackingEventDTO liveTrackingEventDTO =
  // vehicleTrackingService.getLatestVehicleTrackingInfo(
  //        plateNumber);
  //    return vehicleResponseMapper.toResponse(vehicle, liveTrackingEventDTO,
  // vehicleFinancialInfo);
  //  }

  //  @Override
  //  public ESVehicleData getVehicles(String plateNo) {
  //    return esVehicleRepository.findByPlateNo(plateNo)
  //        .map(v -> {
  //          if (v.getKm() != null) {
  //            v.setKm(v.getKm() / 1000);
  //          }
  //          return v;
  //        })
  //        .orElseThrow(() -> new BusinessException(VehicleErrors.VEHICLE_NOT_FOUND));
  //  }

  @Override
  public ESVehicleData getVehicleById(String docId) {
    return esVehicleRepository.findById(docId).orElse(null);
  }

  //  @Override
  //  public void saveVehicle(ESVehicleData vehicle) {
  //    esVehicleRepository.save(vehicle);
  //  }

  @Override
  public void saveVehicleList(List<ESVehicleData> vehicles) {
    esVehicleRepository.saveAll(vehicles);
  }

  //  @Override
  //  public Page<ESVehicleData> getMaintenanceDueVehicles(MaintenanceDueVehicleRequest request) {
  //    if (request == null || request.getKm() == 0 || StringUtils.isEmpty(request.getDate())) {
  //      throw new IllegalArgumentException("Invalid request parameters");
  //    }
  //    Page<ESVehicleData> dueVehicles = esVehicleRepository.findMaintenanceDueVehicles(
  //        request.getDate(),
  //        request.getKm(), PageRequest.of(request.getPageNumber(), request.getPageSize()));
  //    dueVehicles.forEach(v -> v.setKm(v.getKm() != null ? v.getKm() / 1000 : null));
  //    return dueVehicles;
  //  }

  @Override
  public Page<ESVehicleData> findAll(SortRequestDTO request) {
    Page<ESVehicleData> dueVehicles =
        esVehicleRepository.findAll(PageRequest.of(request.getPageNumber(), request.getPageSize()));
    dueVehicles.forEach(v -> v.setKm(v.getKm() != null ? v.getKm() / 1000 : null));
    return dueVehicles;
  }

  @Override
  public ESDeepSearchResponse<ESVehicleData> findAllUsingScrolling(ESDeepSearchRequestDTO request) {
    ESDeepSearchResponse<ESVehicleData> vehicles =
        esVehicleRepository.scrollOverAllVehiclesWithPagination(request);
    vehicles.getData().forEach(v -> v.setKm(v.getKm() != null ? v.getKm() / 1000 : null));
    return vehicles;
  }

  //  private VehicleKeyWiseStats prepareVehicleKeyWiseStats(KeyCount keyData, long totalCount) {
  //    VehicleCountResponse countResponseDTO = countVehicleHandlerFactory.getHandler(
  //            VehicleCountType.RUNNING_STATUS_COUNT)
  //        .map(service -> service.execute(new VehicleCountRequest(keyData.getDataList())))
  //        .orElse(new VehicleCountResponse());
  //    return new VehicleKeyWiseStats().setKey(keyData.getKey())
  //        .setKeyCount(keyData.getCount())
  //        .setValue(calculatePercentage(keyData.getCount(), totalCount))
  //        .setIdle(countResponseDTO.getIdle())
  //        .setRunning(countResponseDTO.getRunning())
  //        .setRunningPercent(calculatePercentage(countResponseDTO.getRunning(),
  // keyData.getCount()));
  //  }

  @Override
  public List<ESVehicleData> getVehicles(Set<String> plateNos) {
    return esVehicleRepository.findByPlateNoIn(plateNos);
  }

  //  public Set<String> getVehiclePlateNumbers() {
  //    return vehicleRepository.findDistinctPlateNumbers().stream()
  //        .filter(ObjectUtils::isNotEmpty).collect(toSet());
  //  }

  //  @Override
  //  public SearchResponse<String> getVehicleAutoCompleteSuggestions(
  //      SearchRequestDTO searchRequestDTO) {
  //    if (ObjectUtils.isEmpty(searchRequestDTO.getQuery())) {
  //      throw new BusinessException(BaseError.validationError("Invalid query parameter"));
  //    }
  //    return new
  // SearchResponse<>(vehicleRepository.getVehicleSuggestions(searchRequestDTO.getQuery(),
  //        getPageableWithoutSort(searchRequestDTO)));
  //  }

}
