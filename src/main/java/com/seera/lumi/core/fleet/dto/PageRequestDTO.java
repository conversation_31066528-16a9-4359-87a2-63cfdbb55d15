package com.seera.lumi.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
public class PageRequestDTO implements Serializable {

  @Serial private static final long serialVersionUID = -600126160341757685L;

  @JsonAlias({"page"})
  private Integer pageNumber = 0;

  @JsonAlias({"size"})
  private Integer pageSize = 10;

  @Override
  public String toString() {
    final StringBuilder sb =
        new StringBuilder("PageRequestDTO{")
            .append("page=")
            .append(pageNumber)
            .append(", size=")
            .append(pageSize)
            .append('}');
    return sb.toString();
  }
}
