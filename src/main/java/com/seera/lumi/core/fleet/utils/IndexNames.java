package com.seera.lumi.core.fleet.utils;

import com.lumi.rental.core.fleet.entity.es.ESMaintenanceLog;
import com.lumi.rental.core.fleet.entity.es.ESVehicleTrackingInfoHistory;
import com.seera.lumi.core.fleet.entity.opensearch.ESVehicleData;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.Document;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class IndexNames {

  public static final String VEHICLE_MAINTENANCE_DATA =
      ESMaintenanceLog.class.getAnnotation(Document.class).indexName();
  public static final String VEHICLE_DATA =
      ESVehicleData.class.getAnnotation(Document.class).indexName();
  public static final String VEHICLE_TRACKING_HISTORY =
      ESVehicleTrackingInfoHistory.class.getAnnotation(Document.class).indexName();
}
