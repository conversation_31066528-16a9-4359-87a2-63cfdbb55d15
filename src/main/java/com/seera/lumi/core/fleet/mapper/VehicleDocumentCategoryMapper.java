package com.seera.lumi.core.fleet.mapper;

import com.lumi.rental.core.fleet.entity.DocumentCategory;
import com.seera.lumi.core.fleet.dto.DocumentTypeCategoryDTO;
import com.seera.lumi.core.fleet.dto.DocumentTypeCategorySearchRequestDTO;
import java.util.List;
import org.mapstruct.*;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VehicleDocumentCategoryMapper {
  DocumentTypeCategoryDTO toDTO(DocumentCategory vehicle);

  DocumentCategory toEntity(DocumentTypeCategoryDTO vehicle);

  List<DocumentTypeCategoryDTO> toDTO(List<DocumentCategory> vehicle);

  default DocumentTypeCategorySearchRequestDTO toSearch(DocumentTypeCategoryDTO categoryDTO) {
    if (categoryDTO == null) {
      return null;
    }

    return new DocumentTypeCategorySearchRequestDTO().setCode(categoryDTO.getCode());
  }

  @Named("partialUpdate")
  @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
  void partialUpdate(@MappingTarget DocumentCategory entity, DocumentCategory dto);
}
