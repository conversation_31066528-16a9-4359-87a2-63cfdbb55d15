package com.seera.lumi.core.fleet.service.impl;

import static com.lumi.rental.core.fleet.constants.Constants.AUTH_TOKEN_PREFIX;

import com.lumi.rental.core.fleet.constants.Constants;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.seera.lumi.core.fleet.api.AuthenticationClient;
import com.seera.lumi.core.fleet.api.ContentClient;
import com.seera.lumi.core.fleet.api.request.PasswordLoginKeycloakUserRequest;
import com.seera.lumi.core.fleet.api.response.ContentResponse;
import com.seera.lumi.core.fleet.exception.DocumentErrors;
import com.seera.lumi.core.fleet.service.ContentService;
import com.seera.lumi.core.fleet.utils.FileUtil;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class ContentServiceImpl implements ContentService {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final ContentClient contentClient;
  private final AuthenticationClient authenticationClient;

  @Value("${file.max.upload.bytes}")
  private Long maxUploadBytes;

  @Value("${rest.call.auth.service.username}")
  private String authServiceUsername;

  @Value("${rest.call.auth.service.password}")
  private String authServicePassword;

  public ContentServiceImpl(
      ContentClient contentClient, AuthenticationClient authenticationClient) {
    this.contentClient = contentClient;
    this.authenticationClient = authenticationClient;
  }

  @Override
  public String uploadPrivate(String plateNumber, MultipartFile file) {
    if (!FileUtil.isValid(file, maxUploadBytes)) {
      log.info("[ ** Invalid File Cannot Upload **]");
      throw new BusinessException(DocumentErrors.INVALID_DOCUMENT);
    }
    if (StringUtils.isEmpty(plateNumber)) {
      log.info("[ ** plateNumber cannot be empty for upload ** ]");
      throw new BusinessException(DocumentErrors.PLATE_NO_IS_REQUIRED);
    }
    ContentResponse<String> uploadResponse =
        contentClient
            .uploadPrivate(
                AUTH_TOKEN_PREFIX + getAuthToken(),
                Constants.BUCKET_FLEET_DOCUMENTS + "," + plateNumber.replace(" ", "_"),
                file)
            .getBody();
    if (null == uploadResponse || StringUtils.isBlank(uploadResponse.getData())) {
      log.info("[ ** No Url received in Upload Response **]");
      throw new BusinessException(DocumentErrors.DOCUMENTS_UPLOAD_FAILED);
    }
    return uploadResponse.getData();
  }

  @Override
  public String sign(String url) {
    if (StringUtils.isBlank(url)) {
      log.info("Cannot Sign empty url");
      return url;
    }
    String signedUrl = Objects.requireNonNull(contentClient.signedUrl(url).getBody()).getData();
    if (StringUtils.isBlank(signedUrl)) {
      log.error("Signing Document Url Failed on Content Service;");
      return url;
    }
    return signedUrl;
  }

  private String getAuthToken() {
    return Objects.requireNonNull(
            authenticationClient
                .login(
                    PasswordLoginKeycloakUserRequest.builder()
                        .username(authServiceUsername)
                        .password(authServicePassword)
                        .build())
                .getBody())
        .getToken();
  }
}
