package com.seera.lumi.core.fleet.service;

import com.seera.lumi.core.fleet.dto.*;
import com.seera.lumi.core.fleet.entity.opensearch.ESVehicleData;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Service
public interface VehicleService {

  //  VehicleResponse getVehicleByPlateNumber(String plateNumber);

  //  ESVehicleData getVehicles(String plateNo);

  ESVehicleData getVehicleById(String docId);

  //  void saveVehicle(ESVehicleData vehicle);

  void saveVehicleList(List<ESVehicleData> vehicles);

  //  Page<ESVehicleData> getMaintenanceDueVehicles(MaintenanceDueVehicleRequest request);

  Page<ESVehicleData> findAll(SortRequestDTO request);

  ESDeepSearchResponse<ESVehicleData> findAllUsingScrolling(ESDeepSearchRequestDTO request);

  List<ESVehicleData> getVehicles(Set<String> plateNos);

  //  Set<String> getVehiclePlateNumbers();

  //  Page<VehicleBasicResponseDTO> getVehicleByPlateNumbers(VehicleSearchRequestDTO requestDTO);

  //  VehicleFilterResponseDTO getVehicleFilterData(VehicleSearchRequestDTO requestDTO);

  //  SearchResponse<String> getVehicleAutoCompleteSuggestions(SearchRequestDTO requestDTO);

}
