package com.seera.lumi.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VehicleDTO {

  private String id;
  private String plateNo;
  private String assetId;
  private String make;
  private String model;
  private Long modelId;
  private Long year;
  private Long km;
  private String color;
  private String carGroup;
  private String chassisNo;
  private Long serviceType;
  private Long transmissionType;
  private BigDecimal nbv;
  private BigDecimal adv;
  private BigDecimal purchasePrice;

  @Field(type = FieldType.Date, format = DateFormat.date, pattern = "yyyy-MM-dd")
  private LocalDate purchaseDate;

  private LastMaintenanceLogDTO lastMaintenance;
}
