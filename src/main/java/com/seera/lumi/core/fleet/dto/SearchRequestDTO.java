package com.seera.lumi.core.fleet.dto;

import java.io.Serial;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SearchRequestDTO extends SortRequestDTO {

  @Serial private static final long serialVersionUID = -600126160341757685L;

  private String query;

  private LocalDateTime from;
  private LocalDateTime to;

  @Override
  public String toString() {
    final StringBuilder sb =
        new StringBuilder(super.toString())
            .append("SearchRequestDTO{")
            .append("query='")
            .append(query)
            .append('\'')
            .append(", from=")
            .append(from)
            .append(", to=")
            .append(to)
            .append('}')
            .append(super.toString());
    return sb.toString();
  }
}
