package com.seera.lumi.core.fleet.dto;

import java.io.Serial;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
public class DocumentTypeCategorySearchRequestDTO extends SearchRequestDTO {

  @Serial private static final long serialVersionUID = -650126160341757685L;
  String code;

  public String getCode() {
    return code;
  }

  public DocumentTypeCategorySearchRequestDTO setCode(String code) {
    this.code = code;
    return this;
  }
}
