package com.seera.lumi.core.fleet.service.factory;

import static java.util.Optional.ofNullable;

import com.seera.lumi.core.fleet.enums.FileStorageType;
import com.seera.lumi.core.fleet.service.FileStorageService;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class FileStorageServiceFactory {

  private static final Map<FileStorageType, FileStorageService> FILE_STORAGE_SERVICE_MAP =
      new EnumMap<>(FileStorageType.class);

  @Autowired
  public FileStorageServiceFactory(List<FileStorageService> services) {
    services.forEach(service -> FILE_STORAGE_SERVICE_MAP.put(service.fileStorageType(), service));
  }

  public Optional<FileStorageService> getService(FileStorageType storageType) {
    return ofNullable(FILE_STORAGE_SERVICE_MAP.get(storageType));
  }
}
