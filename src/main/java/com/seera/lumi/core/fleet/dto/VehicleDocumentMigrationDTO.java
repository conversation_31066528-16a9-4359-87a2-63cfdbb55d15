package com.seera.lumi.core.fleet.dto;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@ToString
public class VehicleDocumentMigrationDTO {

  Long idNo;
  Integer docType;
  Integer pageNo;
  String licenceNo;
  String fileName;
  String transactionDate;
  String transactionTime;
}
