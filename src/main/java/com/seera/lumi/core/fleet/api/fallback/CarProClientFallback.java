package com.seera.lumi.core.fleet.api.fallback;

import com.seera.lumi.core.fleet.api.CarProClient;
import com.seera.lumi.core.fleet.dto.VehicleStatusResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class CarProClientFallback implements FallbackFactory<CarProClient> {

  private static final Logger log = LoggerFactory.getLogger("application");

  @Override
  public CarProClient create(Throwable cause) {
    log.error("Unable to get response from car pro service:", cause);
    return new CarProClient() {

      @Override
      public VehicleStatusResponse getVehicleStatusData(String plateNo) {
        return null;
      }
    };
  }
}
