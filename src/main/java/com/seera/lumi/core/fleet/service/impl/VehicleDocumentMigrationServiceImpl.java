package com.seera.lumi.core.fleet.service.impl;

import static com.lumi.rental.core.fleet.util.DateUtil.convertDateAndTime;
import static com.seera.lumi.core.fleet.exception.FileStorageErrors.SERVICE_NOT_FOUND;
import static java.util.Objects.nonNull;
import static java.util.function.Function.identity;

import com.lumi.rental.core.fleet.entity.VehicleDocument;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.mapper.VehicleDocumentMapper;
import com.lumi.rental.core.fleet.repository.DocumentTypeRepository;
import com.lumi.rental.core.fleet.repository.VehicleDocumentRepository;
import com.seera.lumi.core.fleet.api.CarProAppClient;
import com.seera.lumi.core.fleet.dto.VehicleDocumentMigrationDTO;
import com.seera.lumi.core.fleet.enums.FileStorageType;
import com.seera.lumi.core.fleet.service.VehicleDocumentMigrationService;
import com.seera.lumi.core.fleet.service.factory.FileStorageServiceFactory;
import feign.Response;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.coyote.BadRequestException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleDocumentMigrationServiceImpl implements VehicleDocumentMigrationService {

  private static final Logger log = LoggerFactory.getLogger("application");
  private final VehicleDocumentRepository vehicleDocumentRepository;
  private final DocumentTypeRepository documentTypeRepository;
  private final FileStorageServiceFactory fileStorageServiceFactory;
  private final CarProAppClient carProAppClient;
  private final VehicleDocumentMapper vehicleDocumentMapper;
  private final String ARG_SEPARATOR = ",-A";
  private final String ARG_EOF = ",-A0";
  @Value("${file.storage.service}")
  private FileStorageType fileStorageType;
  @Value("${api.carpro.app.name}")
  private String carProAppName;
  @Value("${api.carpro.app.program.name}")
  private String carProProgramName;
  @Value("${aws.s3.sap.vehicle.document.folder}")
  private String documentFolderName;
  @Value("${aws.cdn.content.url}")
  private String awsCdnContentUrl;

  @Override
  public void migrateVehicleDocuments(VehicleDocumentMigrationDTO eventDTO) {
    Map<String, VehicleDocument> vehicleDocumentMap =
        vehicleDocumentRepository.findByPlateNo(eventDTO.getLicenceNo()).stream()
            .collect(Collectors.toMap(this::vehicleDocumentKey, identity(), (o, n) -> o));

    LocalDateTime transactionDateTime =
        convertDateAndTime(eventDTO.getTransactionDate(), eventDTO.getTransactionTime());
    VehicleDocument vehicleDocument = vehicleDocumentMap.get(vehicleDocumentDTOKey(eventDTO));

    if (nonNull(vehicleDocument)) {
      log.info("Updating existing vehicle document for {}", eventDTO);
      vehicleDocument.setUrl(downloadFileAndUploadToStorage(eventDTO));
      vehicleDocumentRepository.save(vehicleDocument);
    } else {
      log.info("No vehicle document found, Creating new document for {}", eventDTO);
      VehicleDocument newVehicleDocument =
          vehicleDocumentMapper.toEntity(
              eventDTO,
              documentTypeRepository.getReferenceById(eventDTO.getDocType()),
              transactionDateTime);
      newVehicleDocument.setUrl(downloadFileAndUploadToStorage(eventDTO));
      vehicleDocumentRepository.save(newVehicleDocument);
    }
  }

  //  @Override
  //  public void migrateVehicleDocuments(VehicleDocumentMigrationEventDTO eventDTO) {
  //    if (isNotEmpty(eventDTO.getDocumentList())) {
  //      Map<String, VehicleDocument> vehicleDocumentMap = vehicleDocumentRepository.findByPlateNo(
  //              eventDTO.getPlateNo()).stream()
  //          .collect(Collectors.toMap(this::vehicleDocumentKey, identity(), (o, n) -> o));
  //      List<VehicleDocument> saveDocuments = new ArrayList<>();
  //      eventDTO.getDocumentList().forEach(dto -> {
  //        LocalDateTime transactionDateTime = convertDateAndTime(dto.getTransactionDate(),
  //            dto.getTransactionTime());
  //        VehicleDocument vehicleDocument = vehicleDocumentMap.get(vehicleDocumentDTOKey(dto));
  //        if (nonNull(vehicleDocument)) {
  //          if (isNull(vehicleDocument.getUpdatedOn()) || (nonNull(transactionDateTime)
  //              && transactionDateTime.isAfter(vehicleDocument.getUpdatedOn()))) {
  //            vehicleDocument.setUrl(downloadFileAndUploadToStorage(dto));
  //            vehicleDocument.setUpdatedOn(transactionDateTime);
  //            saveDocuments.add(vehicleDocument);
  //          }
  //        } else {
  //          vehicleDocument = vehicleDocumentMapper.toEntity(dto,
  //              documentTypeRepository.getReferenceById(dto.getDocType()), transactionDateTime);
  //          vehicleDocument.setUrl(downloadFileAndUploadToStorage(dto));
  //          saveDocuments.add(vehicleDocument);
  //        }
  //      });
  //      if (isNotEmpty(saveDocuments)) {
  //        vehicleDocumentRepository.saveAll(saveDocuments);
  //      }
  //    }
  //  }

  private String generateCarProRequestArguments(VehicleDocumentMigrationDTO dto) {
    StringBuilder arguments = new StringBuilder("-A");
    arguments.append(dto.getIdNo());
    arguments.append(ARG_SEPARATOR);
    arguments.append(dto.getDocType());
    arguments.append(ARG_SEPARATOR);
    arguments.append(dto.getPageNo());
    arguments.append(ARG_EOF);
    return arguments.toString();
  }

  private String downloadFileAndUploadToStorage(VehicleDocumentMigrationDTO dto) {
    try {
      Response response =
          carProAppClient.downloadFile(
              carProAppName, carProProgramName, generateCarProRequestArguments(dto));
      byte[] bytes = response.body().asInputStream().readAllBytes();
      String fileContent = new String(bytes);
      if (fileContent.contains("CONTENT-TYPE: application/pdf") && fileContent.length() < 50) {
        log.error(
            "File is empty, discarding file for event {} file-content {}", dto, new String(bytes));
        throw new BadRequestException("File is empty : " + fileContent);
      }
      String folderName = documentFolderName + "/" + dto.getLicenceNo().replaceAll("\s", "_");
      return fileStorageServiceFactory
          .getService(fileStorageType)
          .map(service -> service.uploadFile(folderName, dto.getFileName(), bytes))
          .orElseThrow(() -> new BusinessException(SERVICE_NOT_FOUND));
    } catch (Exception exception) {
      throw new RuntimeException(exception);
    }
  }

  private String vehicleDocumentKey(VehicleDocument document) {
    return String.join(
        "|",
        document.getIdNo(),
        document.getPageNo().toString(),
        document.getType().getId().toString());
  }

  private String vehicleDocumentDTOKey(VehicleDocumentMigrationDTO dto) {
    return String.join(
        "|", dto.getIdNo().toString(), dto.getPageNo().toString(), dto.getDocType().toString());
  }

  //  @Transactional
  //  @Override
  //  public void processVehicleDocumentEvent(VehicleDocumentMetadataEvent event) {
  //    if (nonNull(event.getPlateNo())) {
  //      Optional<DocumentType> documentType = documentTypeRepository.findByCode(event.getType());
  //      if (documentType.isPresent()) {
  //        List<VehicleDocument> vehicleDocuments =
  // vehicleDocumentRepository.findByPlateNoAndType_id(
  //            event.getPlateNo(), documentType.get().getId());
  //        VehicleDocument vehicleDocument =
  //            isNotEmpty(vehicleDocuments) ? vehicleDocuments.get(0)
  //                : new VehicleDocument();
  //        if (vehicleDocument.getCreatedOn() == null) {
  //          vehicleDocument.setCreatedOn(LocalDateTime.now());
  //        }
  //        vehicleDocument.setType(documentType.get());
  //        vehicleDocument.setPlateNo(event.getPlateNo());
  //        vehicleDocument.setUpdatedOn(LocalDateTime.now());
  //        vehicleDocument.setExtension(getExtension(event.getFilePath()));
  //        vehicleDocument.setUrl(getCdnFilePath(event.getFilePath()));
  //        vehicleDocumentRepository.save(vehicleDocument);
  //      }
  //    }
  //  }

  //  private String getCdnFilePath(String fileName) {
  //    return awsCdnContentUrl.endsWith("/") ? awsCdnContentUrl + fileName
  //        : awsCdnContentUrl + "/" + fileName;
  //  }

}
