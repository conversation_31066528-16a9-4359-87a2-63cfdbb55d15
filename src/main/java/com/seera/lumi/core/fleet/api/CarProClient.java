package com.seera.lumi.core.fleet.api;

import com.seera.lumi.core.fleet.api.fallback.CarProClientFallback;
import com.seera.lumi.core.fleet.dto.VehicleStatusResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/** Internal CarPro Backend Service Client */
@Component
@FeignClient(
    name = "CarProClient",
    url = "${api.carpro.app.service.base.url}",
    dismiss404 = true,
    fallbackFactory = CarProClientFallback.class)
public interface CarProClient {

  @GetMapping("/carpro/vehicles/status")
  VehicleStatusResponse getVehicleStatusData(@RequestParam("plateNo") String plateNo);
}
