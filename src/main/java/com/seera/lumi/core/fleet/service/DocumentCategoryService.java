package com.seera.lumi.core.fleet.service;

import com.lumi.rental.core.fleet.entity.DocumentCategory;
import com.seera.lumi.core.fleet.dto.DocumentTypeCategorySearchRequestDTO;
import java.util.List;
import java.util.Optional;

public interface DocumentCategoryService {

  List<DocumentCategory> findAll(DocumentTypeCategorySearchRequestDTO search);

  DocumentCategory save(DocumentCategory documentType);

  Optional<DocumentCategory> partialUpdate(DocumentCategory documentType);
}
