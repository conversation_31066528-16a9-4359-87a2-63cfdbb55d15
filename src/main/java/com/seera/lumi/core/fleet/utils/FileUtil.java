package com.seera.lumi.core.fleet.utils;

import com.seera.lumi.core.fleet.enums.DocumentExtension;
import java.util.Arrays;
import org.apache.commons.io.FilenameUtils;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

public class FileUtil {

  private FileUtil() {}

  public static boolean isValid(MultipartFile file, Long maxUploadBytes) {
    return file != null
        && !file.isEmpty()
        && file.getSize() < maxUploadBytes
        && hasValidContentType(file.getContentType())
        && hasValidExtension(file);
  }

  public static boolean hasValidContentType(String contentType) {
    try {
      MediaType.parseMediaType(contentType);
    } catch (Exception e) {
      return false;
    }
    return true;
  }

  public static boolean hasValidExtension(MultipartFile file) {
    return getDocumentExtension(file) != null;
  }

  public static DocumentExtension getDocumentExtension(MultipartFile file) {
    String fileExtension = "." + FilenameUtils.getExtension(file.getOriginalFilename());
    return Arrays.stream(DocumentExtension.values())
        .filter(extension -> extension.getCode().equalsIgnoreCase(fileExtension))
        .findFirst()
        .orElse(null);
  }
}
