package com.seera.lumi.core.fleet.entity.opensearch;

import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(indexName = "vehicledata")
public class ESVehicleData {

  private String id;
  private String plateNo;
  private ESVehicleMake make;
  private ESVehicleModel model;
  private String assetId;
  private Long modelId;
  private Long year;
  private Long km;
  private String color;
  private String carGroup;
  private String chassisNo;
  private Long serviceType;
  private Long transmissionType;
  private BigDecimal nbv;
  private BigDecimal adv;
  private BigDecimal purchasePrice;

  @Field(type = FieldType.Date, format = DateFormat.date, pattern = "yyyy-MM-dd")
  private LocalDate purchaseDate;

  @Field(type = FieldType.Object)
  private ESLastMaintenanceLog lastMaintenance;

  public ESVehicleData(String docId) {
    this.id = docId;
  }

  @Override
  public String toString() {
    return ReflectionToStringBuilder.toString(this);
  }
}
