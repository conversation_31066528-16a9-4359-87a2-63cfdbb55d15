package com.seera.lumi.core.fleet.business.impl;

import com.seera.lumi.core.fleet.business.BusinessService;

public abstract class AbstractBusinessService<I, O> implements BusinessService<I, O> {

  @Override
  public final O doAction(I request) {
    // 1. validate request
    validateRequest(request);
    // 2. execute service
    O response = execute(request);
    // 3. log transaction
    return this.postExecution(request, response);
  }

  abstract void validateRequest(I request);

  protected O postExecution(I request, O response) {
    return response;
  }
}
