package com.seera.lumi.core.fleet.dto;

import static lombok.AccessLevel.PRIVATE;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.lumi.rental.core.fleet.dto.VehicleLastMaintenanceDTO;
import com.lumi.rental.core.fleet.response.VehicleMakeResponse;
import com.lumi.rental.core.fleet.response.VehicleModelResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VehicleBasicResponseDTO {

  String plateNo;
  VehicleMakeResponse make;
  VehicleModelResponse model;
  Long year;
  String carGroup;
  VehicleLastMaintenanceDTO lastMaintenance;
}
