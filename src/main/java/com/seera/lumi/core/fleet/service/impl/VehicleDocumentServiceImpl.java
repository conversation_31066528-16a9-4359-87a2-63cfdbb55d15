package com.seera.lumi.core.fleet.service.impl;

import com.lumi.rental.core.fleet.entity.VehicleDocument;
import com.lumi.rental.core.fleet.mapper.VehicleDocumentMapper;
import com.lumi.rental.core.fleet.repository.VehicleDocumentRepository;
import com.lumi.rental.core.fleet.service.DocumentUrlSigner;
import com.seera.lumi.core.fleet.service.VehicleDocumentService;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
@RequiredArgsConstructor
public class VehicleDocumentServiceImpl implements VehicleDocumentService {

  private static final Logger log = LoggerFactory.getLogger("application");
  private final VehicleDocumentRepository vehicleDocumentRepository;
  private final DocumentUrlSigner documentUrlSigner;
  private final VehicleDocumentMapper vehicleDocumentMapper;

  @Override
  @Transactional(readOnly = true)
  public List<VehicleDocument> findByPlateNo(String plateNo) {
    return vehicleDocumentRepository.findByPlateNo(plateNo).stream()
        .map(
            d -> {
              d.setUrl(documentUrlSigner.sign(d.getUrl()));
              return d;
            })
        .toList();
  }

  @Override
  public VehicleDocument save(VehicleDocument document) {
    log.debug("Request to save VehicleDocument : {}", document);
    return vehicleDocumentRepository.save(document);
  }

  @Override
  public Optional<VehicleDocument> partialUpdate(VehicleDocument document) {
    return vehicleDocumentRepository
        .findById(document.getId())
        .map(
            existingType -> {
              vehicleDocumentMapper.partialUpdate(existingType, document);
              return existingType;
            })
        .map(vehicleDocumentRepository::save);
  }
}
