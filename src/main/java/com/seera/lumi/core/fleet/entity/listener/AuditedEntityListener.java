package com.seera.lumi.core.fleet.entity.listener;

import com.lumi.rental.core.fleet.entity.AuditedBaseEntity;
import com.seera.lumi.core.fleet.utils.SecurityUtil;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;

import static java.util.Objects.nonNull;

public class AuditedEntityListener {

  private static final Logger log = LoggerFactory.getLogger("application");

  @PreUpdate
  private void beforeAnyUpdate(AuditedBaseEntity entity) {
    log.info("In pre update:{}", entity.toString());
    entity.setUpdatedOn(LocalDateTime.now());
    String userId = getCurrentAuditor();
    entity.setUpdatedBy(userId);
  }

  @PrePersist
  private void prePersist(AuditedBaseEntity entity) {
    log.info("In pre persist:{}", entity.toString());
    entity.setCreatedOn(LocalDateTime.now());
    String userId = getCurrentAuditor();
    entity.setCreatedBy(userId);
    entity.setUpdatedBy(userId);
  }

  public String getCurrentAuditor() {
    String userId = SecurityUtil.getLoggedInUserId();
    if (nonNull(userId)) {
      return userId;
    }
    return "00000-00000-00000";
  }
}
