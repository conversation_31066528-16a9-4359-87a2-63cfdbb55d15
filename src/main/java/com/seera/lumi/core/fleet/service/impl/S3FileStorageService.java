package com.seera.lumi.core.fleet.service.impl;

import static com.seera.lumi.core.fleet.enums.FileStorageType.AWS;

import com.seera.lumi.core.fleet.enums.FileStorageType;
import com.seera.lumi.core.fleet.service.FileStorageService;
import java.io.File;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "file", name = "storage.service", havingValue = "AWS")
public class S3FileStorageService implements FileStorageService {

  private static final Logger log = LoggerFactory.getLogger("application");
  private final S3Client s3Client;
  @Value("${aws.s3.vehicle.document.bucket}")
  private String bucketName;
  @Value("${aws.cdn.content.url}")
  private String awsCdnContentUrl;

  @Override
  public FileStorageType fileStorageType() {
    return AWS;
  }

  @Override
  public String uploadFile(String folder, String fileName, byte[] bytes) {
    if (StringUtils.hasText(folder)) {
      fileName = folder + File.separator + fileName;
    }
    log.info(
        "Starting file upload to S3: bucket={}, fileName={}, fileSize={}",
        bucketName,
        fileName,
        bytes.length);
    long start = System.currentTimeMillis();
    PutObjectRequest request = PutObjectRequest.builder().bucket(bucketName).key(fileName).build();
    s3Client.putObject(request, RequestBody.fromBytes(bytes));
    log.info(
        "File upload completed: bucket={}, fileName={}, fileSize={}, TimeTaken={}ms",
        bucketName,
        fileName,
        bytes.length,
        (System.currentTimeMillis() - start));
    return getCdnFilePath(fileName);
  }

  private String getCdnFilePath(String fileName) {
    return awsCdnContentUrl.endsWith("/")
        ? awsCdnContentUrl + fileName
        : awsCdnContentUrl + "/" + fileName;
  }
}
