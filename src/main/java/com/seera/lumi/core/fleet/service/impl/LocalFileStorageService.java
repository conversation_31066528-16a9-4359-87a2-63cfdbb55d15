package com.seera.lumi.core.fleet.service.impl;

import static com.seera.lumi.core.fleet.enums.FileStorageType.LOCAL;
import static java.io.File.separator;

import com.seera.lumi.core.fleet.enums.FileStorageType;
import com.seera.lumi.core.fleet.service.FileStorageService;
import java.io.File;
import java.io.FileOutputStream;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class LocalFileStorageService implements FileStorageService {
  private static final Logger log = LoggerFactory.getLogger("application");

  @Value("${file.storage.local.location}")
  private String localStorageLocation;

  @Override
  public FileStorageType fileStorageType() {
    return LOCAL;
  }

  @Override
  public String uploadFile(String folder, String fileName, byte[] bytes) {
    createFolder(folder);
    File file =
        new File(
            new StringBuilder(localStorageLocation)
                .append(separator)
                .append(folder)
                .append(separator)
                .append(fileName)
                .toString());
    try (FileOutputStream fileOutputStream = new FileOutputStream(file)) {
      file.mkdirs();
      file.createNewFile();
      fileOutputStream.write(bytes);
      fileOutputStream.flush();
    } catch (Exception e) {
      e.printStackTrace();
      throw new RuntimeException(e);
    }
    log.info("File upload completed: location={}", file.getAbsolutePath());
    return file.getAbsolutePath();
  }

  private void createFolder(String folder) {
    File file =
        new File(
            new StringBuilder(localStorageLocation).append(separator).append(folder).toString());
    if (!file.exists()) {
      file.mkdirs();
    }
  }
}
