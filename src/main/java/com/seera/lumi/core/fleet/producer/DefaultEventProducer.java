package com.seera.lumi.core.fleet.producer;

import java.util.concurrent.CompletableFuture;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class DefaultEventProducer {

  private static final Logger log = LoggerFactory.getLogger("application");

  private final KafkaTemplate defaultKafkaTemplate;

  public DefaultEventProducer(KafkaTemplate defaultKafkaTemplate) {
    this.defaultKafkaTemplate = defaultKafkaTemplate;
  }

  @Async
  public void send(String topic, Object key, Object event) {
    ProducerRecord producerRecord = new ProducerRecord(topic, key, event);
    CompletableFuture<SendResult<Integer, String>> future =
        defaultKafkaTemplate.send(producerRecord);
    future
        .thenApply(
            result -> {
              log.info("Event sent successfully:{}", event);
              return result;
            })
        .exceptionallyComposeAsync(
            ex -> {
              log.error("Event event failed: {}", event, ex);
              return null;
            });
  }
}
