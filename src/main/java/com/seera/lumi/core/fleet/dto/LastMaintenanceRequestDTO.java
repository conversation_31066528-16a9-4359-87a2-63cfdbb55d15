package com.seera.lumi.core.fleet.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.Set;

@Data
@Accessors(chain = true)
public class LastMaintenanceRequestDTO implements Serializable {

  @Serial private static final long serialVersionUID = -600126160341757685L;

  @NotNull private Set<String> plateNos;
  private Long maintenanceTypeId;
}
