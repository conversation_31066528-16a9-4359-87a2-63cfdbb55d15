package com.seera.lumi.core.fleet.mapper;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toSet;

import com.lumi.rental.core.fleet.entity.MaintenanceType;
import com.lumi.rental.core.fleet.entity.es.ESMaintenanceLog;
import com.seera.lumi.core.fleet.dto.MaintenanceLogDTO;
import com.seera.lumi.core.fleet.dto.MaintenanceLogTypeDTO;
import com.seera.lumi.core.fleet.dto.VehicleMaintenanceEventDTO;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MaintenanceLogMapper {

  static Integer zeroIfNull(Long val) {
    return isNull(val) ? 0 : val.intValue();
  }

  @Mapping(source = "text", target = "remarks")
  @Mapping(source = "docDate", target = "date")
  ESMaintenanceLog map(VehicleMaintenanceEventDTO vehicleMaintenanceEventDTO);

  default MaintenanceLogDTO toMaintenanceLogDTO(
      List<ESMaintenanceLog> maintenanceLogs, Map<String, MaintenanceType> maintenanceTypeMap) {
    MaintenanceLogDTO maintenanceLogDTO = toMaintenanceLogDTO(maintenanceLogs.getFirst());
    maintenanceLogDTO.setKm(getKm(maintenanceLogs));
    maintenanceLogDTO.setMaintenanceLogTypes(
        maintenanceLogs.stream()
            .map(ESMaintenanceLog::getServiceType)
            .map(maintenanceTypeMap::get)
            .filter(Objects::nonNull)
            .map(this::toMaintenanceTypeResponseDTO)
            .collect(toSet()));
    maintenanceLogDTO.setRemarks(
        maintenanceLogs.stream()
            .map(ESMaintenanceLog::getRemarks)
            .distinct()
            .collect(joining(",")));
    return maintenanceLogDTO;
  }

  @Mapping(source = "id", target = "id", ignore = true)
  @Mapping(source = "id", target = "maintenanceTypeId")
  @Mapping(source = "nameEn", target = "serviceType")
  MaintenanceLogTypeDTO toMaintenanceTypeResponseDTO(MaintenanceType maintenanceType);

  @Mapping(source = "id", target = "id", ignore = true)
  @Mapping(source = "date", target = "date", qualifiedByName = "convertLocalDateTime")
  MaintenanceLogDTO toMaintenanceLogDTO(ESMaintenanceLog maintenanceLog);

  @Named("convertLocalDateTime")
  default LocalDateTime convertLocalDateTime(LocalDate localDate) {
    return nonNull(localDate) ? localDate.atStartOfDay() : null;
  }

  default Integer getKm(List<ESMaintenanceLog> maintenanceLogs) {
    return maintenanceLogs.stream()
        .map(ESMaintenanceLog::getKm)
        .map(MaintenanceLogMapper::zeroIfNull)
        .mapToInt(Integer::intValue)
        .max()
        .getAsInt();
  }
}
