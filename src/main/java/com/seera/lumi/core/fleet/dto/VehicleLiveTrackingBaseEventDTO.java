package com.seera.lumi.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VehicleLiveTrackingBaseEventDTO {

  protected Integer vehicleID;
  protected String displayName;
  protected Long mileage;
  protected Integer speedLimit;
  protected Integer literPer100KM;
  protected String recordDateTime;
  protected Double longitude;
  protected Double latitude;
  protected Boolean engineStatus;
  protected Integer speed;
  protected Integer direction;
}
