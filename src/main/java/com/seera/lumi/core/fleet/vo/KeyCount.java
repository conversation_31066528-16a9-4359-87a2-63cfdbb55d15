package com.seera.lumi.core.fleet.vo;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@RequiredArgsConstructor
@AllArgsConstructor
public class KeyCount {

  final String key;
  final long count;
  List<String> dataList;
}
