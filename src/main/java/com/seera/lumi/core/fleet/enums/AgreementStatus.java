package com.seera.lumi.core.fleet.enums;

import static java.util.Arrays.stream;
import static org.apache.commons.lang3.StringUtils.trim;

public enum AgreementStatus {
  OPEN("open"),
  CLOSE("close"),
  CANCEL("cancel"),
  PRE_CLOSE("preclose"),
  REOPEN("reopen");

  private final String status;

  AgreementStatus(String status) {
    this.status = status;
  }

  public static AgreementStatus resolveAgreementStatus(String key) {
    return stream(AgreementStatus.values())
        .filter(statusEnum -> statusEnum.getStatus().equals(trim(key)))
        .findFirst()
        .orElse(null);
  }

  public String getStatus() {
    return status;
  }
}
