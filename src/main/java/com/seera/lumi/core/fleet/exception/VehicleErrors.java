package com.seera.lumi.core.fleet.exception;

import com.lumi.rental.core.fleet.exception.BaseError;
import org.springframework.http.HttpStatus;

public class VehicleErrors extends BaseError {
  public static final BaseError VEHICLE_NOT_FOUND =
      new VehicleErrors(2001, "error.vehicle.not.found", HttpStatus.NOT_FOUND);
  public static final BaseError MODEL_NOT_FOUND =
      new VehicleErrors(2002, "error.model.not.found", HttpStatus.NOT_FOUND);
  public static final BaseError OPERATIONAL_DATA_NOT_FOUND =
      new VehicleErrors(2003, "error.operational.data.not.found", HttpStatus.NOT_FOUND);

  protected VehicleErrors(int code, String desc, HttpStatus httpStatus) {
    super(code, desc, httpStatus);
  }
}
