package com.seera.lumi.core.fleet.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CacheConstants {

  public static final String CACHE_SEPARATOR = "::";
  public static final String FLEET_CACHE_PREFIX = "FLEET" + CACHE_SEPARATOR;

  public static final String VEHICLE_LAST_MAINTENANCE_CACHE =
      FLEET_CACHE_PREFIX + "VEHICLE_LAST_MAINTENANCE_CACHE";

  public static final String MODEL_ALL = FLEET_CACHE_PREFIX + "MODEL::ALL";
  public static final String MODEL_BASIC_RESPONSE_V2_ALL =
      FLEET_CACHE_PREFIX + "MODEL::BASIC_RESPONSE_V2_ALL";
  public static final String MODEL_BASIC_RESPONSE_V2_ALL_ALL =
      FLEET_CACHE_PREFIX + "MODEL::BASIC_RESPONSE_V2_ALL_ALL";
  public static final String MODEL_RESPONSE_V2_BY_ID =
      FLEET_CACHE_PREFIX + "MODEL::RESPONSE_V2_BY_ID";
  public static final String MODEL_BASIC_RESPONSE_V2_BY_ID =
      FLEET_CACHE_PREFIX + "MODEL::BASIC_RESPONSE_V2_BY_ID";
  public static final String MODEL_BY_ID = FLEET_CACHE_PREFIX + "MODEL::BY_ID";
  public static final String MODEL_BY_GROUP_CODE = FLEET_CACHE_PREFIX + "MODEL::BY_GROUP_CODE";
  public static final String MODEL_BY_FAMILY_MEMBER =
      FLEET_CACHE_PREFIX + "MODEL::BY_FAMILY_MEMBER";
  public static final String MODEL_BY_FACE_MODEL_ID =
      FLEET_CACHE_PREFIX + "MODEL::BY_FACE_MODEL_ID";

  public static final String GROUP_RESPONSE_ALL_DETAIL =
      FLEET_CACHE_PREFIX + "GROUP::RESPONSE_ALL_DETAIL";
  public static final String GROUP_RESPONSE_ALL = FLEET_CACHE_PREFIX + "GROUP::RESPONSE_ALL";
  public static final String GROUP_RESPONSE_BY_CODE =
      FLEET_CACHE_PREFIX + "GROUP::RESPONSE_BY_CODE";
  public static final String GROUP_RESPONSE_BY_ID = FLEET_CACHE_PREFIX + "GROUP::RESPONSE_BY_ID";
  public static final String GROUP_MAP_BY_CODE = FLEET_CACHE_PREFIX + "GROUP::MAP_BY_CODE";
  public static final String GROUP_RESPONSE_BY_IDS = FLEET_CACHE_PREFIX + "GROUP::RESPONSE_BY_IDS";

  public static final String VEHICLE_METADATA_BY_PLATE_NOS =
      FLEET_CACHE_PREFIX + "METADATA::BY_PLATE_NOS";

  public static final String MAKE_CACHE_PREFIX = FLEET_CACHE_PREFIX + "MAKE" + CACHE_SEPARATOR;
  public static final String MAKE_RESPONSE_ALL = MAKE_CACHE_PREFIX + "RESPONSE_ALL";
  public static final String MAKE_RESPONSE_BY_ID = MAKE_CACHE_PREFIX + "RESPONSE_BY_ID";
  public static final String MAKE_VEHICLE_COUNT = MAKE_CACHE_PREFIX + "VEHICLE_COUNT";

  public static final String CLASS_CACHE_PREFIX = FLEET_CACHE_PREFIX + "CLASS" + CACHE_SEPARATOR;
  public static final String CLASS_RESPONSE_ALL = CLASS_CACHE_PREFIX + "RESPONSE_ALL";
  public static final String CLASS_RESPONSE_BY_ID = CLASS_CACHE_PREFIX + "RESPONSE_BY_ID";
  public static final String CLASS_VEHICLE_COUNT = CLASS_CACHE_PREFIX + "VEHICLE_COUNT";

  public static final String ADDON_CACHE_PREFIX = FLEET_CACHE_PREFIX + "ADDON" + CACHE_SEPARATOR;
  public static final String ADDON_RESPONSE_ALL = ADDON_CACHE_PREFIX + "RESPONSE_ALL";
  public static final String ADDON_RESPONSE_BY_ID = ADDON_CACHE_PREFIX + "RESPONSE_BY_ID";
}
