package com.seera.lumi.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MaintenanceLogTypeDTO implements Serializable {

  @Serial private static final long serialVersionUID = 42L;

  private Long id;
  private Integer maintenanceTypeId;
  private String serviceType;
}
