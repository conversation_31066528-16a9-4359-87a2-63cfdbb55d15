package com.seera.lumi.core.fleet.dto;

import jakarta.validation.constraints.NotNull;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.Set;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class VehicleSearchRequestDTO extends SearchRequestDTO {

  @Serial private static final long serialVersionUID = -650126160341757685L;

  @NotNull Set<String> plateNumbers;

  String make;
  String model;
  Set<String> carGroups;
  Set<String> modelYears;
  Set<String> makes;
  Set<String> models;
  Integer lastMaintenanceInDays;
}
