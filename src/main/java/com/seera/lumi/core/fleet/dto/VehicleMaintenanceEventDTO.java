package com.seera.lumi.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import lombok.Data;

@Data
public class VehicleMaintenanceEventDTO {

  private String amount;
  private String currency;
  private String assignment;
  private String text;
  private String km;
  private String plateNo;
  private String serviceType;
  private String orderId;
  private String glNo;

  @JsonFormat(pattern = "yyyy-MM-dd")
  private LocalDate docDate;

  @JsonFormat(pattern = "yyyy-MM-dd")
  private LocalDate postingDate;
}
