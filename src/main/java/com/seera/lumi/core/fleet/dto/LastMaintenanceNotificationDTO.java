package com.seera.lumi.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDate;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class LastMaintenanceNotificationDTO {

  @Field(type = FieldType.Date, format = DateFormat.date, pattern = "yyyy-MM-dd")
  private LocalDate date;

  private Integer km;

  public LocalDate getDate() {
    return date;
  }

  public LastMaintenanceNotificationDTO setDate(LocalDate date) {
    this.date = date;
    return this;
  }

  public Integer getKm() {
    return km;
  }

  public LastMaintenanceNotificationDTO setKm(Integer km) {
    this.km = km;
    return this;
  }
}
