package com.seera.lumi.core.fleet.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class PasswordLoginKeycloakUserRequest implements Serializable {
  @Serial private static final long serialVersionUID = -1045200389894877145L;

  private String username;

  private String password;
}
