package com.seera.lumi.core.fleet.service.impl;

import com.lumi.rental.core.fleet.entity.DocumentCategory;
import com.lumi.rental.core.fleet.repository.DocumentCategoryRepository;
import com.lumi.rental.core.fleet.repository.specification.DocumentCategorySpecification;
import com.lumi.rental.core.fleet.util.PageUtil;
import com.seera.lumi.core.fleet.dto.DocumentTypeCategorySearchRequestDTO;
import com.seera.lumi.core.fleet.mapper.VehicleDocumentCategoryMapper;
import com.seera.lumi.core.fleet.service.DocumentCategoryService;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
@RequiredArgsConstructor
public class DocumentCategoryServiceImpl implements DocumentCategoryService {
  private static final Logger log = LoggerFactory.getLogger("application");
  private final DocumentCategoryRepository documentCategoryRepository;
  private final VehicleDocumentCategoryMapper vehicleDocumentCategoryMapper;

  @Override
  @Transactional(readOnly = true)
  public List<DocumentCategory> findAll(DocumentTypeCategorySearchRequestDTO filterDTO) {
    log.debug("Request to get all Document category {}", filterDTO);
    DocumentCategorySpecification specification = new DocumentCategorySpecification(filterDTO);
    return documentCategoryRepository
        .findAll(specification, PageUtil.getPageable(filterDTO))
        .getContent();
  }

  @Override
  public DocumentCategory save(DocumentCategory category) {
    return documentCategoryRepository.save(category);
  }

  @Override
  public Optional<DocumentCategory> partialUpdate(DocumentCategory category) {
    return documentCategoryRepository
        .findById(category.getId())
        .map(
            existingType -> {
              vehicleDocumentCategoryMapper.partialUpdate(existingType, category);
              return existingType;
            })
        .map(documentCategoryRepository::save);
  }
}
