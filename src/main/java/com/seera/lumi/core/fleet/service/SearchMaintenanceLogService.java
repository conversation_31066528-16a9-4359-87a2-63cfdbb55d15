package com.seera.lumi.core.fleet.service;

import com.seera.lumi.core.fleet.dto.LastMaintenanceRequestDTO;
import com.seera.lumi.core.fleet.dto.MaintenanceLogSearchRequestDTO;
import com.lumi.rental.core.fleet.entity.MaintenanceLog;
import com.lumi.rental.core.fleet.entity.MaintenanceType;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface SearchMaintenanceLogService {

  Page<MaintenanceLog> search(MaintenanceLogSearchRequestDTO searchRequestDTO);

  List<MaintenanceLog> getMaintenanceLogsByPlateNumber(String plateNumber);

  List<MaintenanceLog> getLastMaintenance(LastMaintenanceRequestDTO requestDTO);

  Page<MaintenanceLog> getLastMaintenanceLogs(int tenantId, String plateNo, Pageable pageable);

  List<MaintenanceType> getAllMaintenanceTypes();
}
