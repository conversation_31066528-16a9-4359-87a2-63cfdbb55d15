// package com.seera.lumi.core.fleet.dto;
//
// import static lombok.AccessLevel.PRIVATE;
//
// import java.io.Serializable;
// import lombok.Data;
// import lombok.NoArgsConstructor;
// import lombok.experimental.Accessors;
// import lombok.experimental.FieldDefaults;
//
// @Data
// @Accessors(chain = true)
// @FieldDefaults(level = PRIVATE)
// @NoArgsConstructor
// public class VehicleDocumentMetadataEvent implements Serializable {
//
//  private static final long serialVersionUID = -635323273079722183L;
//
//  String plateNo;
//  String s3BucketName;
//  String filePath;
//  String type;
// }
