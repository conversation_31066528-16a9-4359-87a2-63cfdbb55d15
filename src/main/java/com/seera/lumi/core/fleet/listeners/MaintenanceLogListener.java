package com.seera.lumi.core.fleet.listeners;

import com.seera.lumi.core.fleet.dto.LastMaintenanceLogDTO;
import com.seera.lumi.core.fleet.dto.VehicleDTO;
import com.seera.lumi.core.fleet.dto.VehicleMaintenanceLogDataDTO;
import com.seera.lumi.core.fleet.producer.EventProducer;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.DltHandler;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import static org.springframework.kafka.support.KafkaHeaders.*;

@Service
public class MaintenanceLogListener {
  private static final Logger log = LoggerFactory.getLogger("application");
  private final EventProducer eventProducer;

  //  @Value("${kafka.topic.vehicle.financial.data}")
  private String vehicleDataTopic;

  public MaintenanceLogListener(EventProducer eventProducer) {
    this.eventProducer = eventProducer;
  }

  //  @KafkaListener(
  //      topics = {"${kafka.topic.vehicle.maintenance.data}"},
  //      groupId = "populate-last-maintenance-log-in-vehicle-data",
  //      concurrency = "${kafka.listen.concurrency}",
  //      containerFactory = "kafkaListenerContainerFactory",
  //      autoStartup = "${kafka.listen.auto.start:true}")
  public void listen(
      @Payload VehicleMaintenanceLogDataDTO event, @Headers MessageHeaders eventHeaders) {
    log.info("Message received, Value:{} Headers:{}", event, eventHeaders);
    eventProducer.send(vehicleDataTopic, event.getPlateNo(), buildVehicleDTOForPublish(event));
  }

  private VehicleDTO buildVehicleDTOForPublish(VehicleMaintenanceLogDataDTO vehicle) {
    VehicleDTO vehicleIndexData = new VehicleDTO();
    vehicleIndexData.setPlateNo(vehicle.getPlateNo());
    LastMaintenanceLogDTO lastMaintenanceLog = new LastMaintenanceLogDTO();
    lastMaintenanceLog.setKm(vehicle.getKm());
    lastMaintenanceLog.setDate(vehicle.getDocDate());
    vehicleIndexData.setLastMaintenance(lastMaintenanceLog);
    //  log.info("Vehicle index data {}", vehicleIndexData);
    return vehicleIndexData;
  }

  @DltHandler
  public void listenToDLT(
      @Header(ORIGINAL_TOPIC) String originalTopic,
      @Header(RECEIVED_KEY) String receivedKey,
      @Header(EXCEPTION_MESSAGE) String exceptionMessage,
      @Header(EXCEPTION_STACKTRACE) String exceptionStackTrace) {
    log.info(
        "Unable to process message on topic:{} for messageKey:{} , errorMessage:{}, exceptionStacktrace:{}",
        originalTopic,
        receivedKey,
        exceptionMessage,
        exceptionStackTrace);
  }
}
