package com.seera.lumi.core.fleet.utils;

import static java.util.Objects.isNull;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.DateFormat;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ApplicationUtil {

  public static double calculatePercentage(double val1, double val2) {
    if (val1 == 0 || val2 == 0) {
      return 0;
    }
    return round((val1 / val2) * 100, 2);
  }

  public static double round(double value, int places) {
    if (places < 0) {
      throw new IllegalArgumentException();
    }
    BigDecimal bd = BigDecimal.valueOf(value);
    bd = bd.setScale(places, RoundingMode.HALF_UP);
    return bd.doubleValue();
  }

  public static String formatDate(LocalDateTime date, DateFormat format) {
    return DateTimeFormatter.ofPattern(format.getPattern()).format(date);
  }

  public static Double zeroIfNull(BigDecimal value) {
    return isNull(value) ? 0.0 : value.doubleValue();
  }
}
