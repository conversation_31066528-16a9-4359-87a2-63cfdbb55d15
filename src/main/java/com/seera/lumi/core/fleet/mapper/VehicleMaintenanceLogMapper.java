package com.seera.lumi.core.fleet.mapper;

import com.lumi.rental.core.fleet.entity.MaintenanceLog;
import com.lumi.rental.core.fleet.entity.MaintenanceType;
import com.lumi.rental.core.fleet.entity.es.ESMaintenanceLog;
import com.seera.lumi.core.fleet.dto.*;
import com.seera.lumi.core.fleet.entity.opensearch.ESVehicleData;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

import java.util.*;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VehicleMaintenanceLogMapper {

  MaintenanceLogDTO toDTO(MaintenanceLog maintenanceLog);

  @Mapping(source = "id", target = "id")
  @Mapping(target = "name.en", source = "nameEn")
  @Mapping(target = "name.ar", source = "nameAr")
  MaintenanceTypeResponse toMaintenanceTypeResponse(MaintenanceType maintenanceType);

  @Mapping(source = "model.name", target = "name")
  VehicleModelResponse toModelResponse(ESVehicleData vehicle);

  @Mapping(source = "make.name", target = "name")
  VehicleMakeResponse toMakeResponse(ESVehicleData vehicle);

  @Named("toMaintenanceLogs")
  default List<MaintenanceTypeResponse> toMaintenanceLogs(
      List<ESMaintenanceLog> maintenanceLogs, Map<String, MaintenanceType> maintenanceTypeMap) {
    return Optional.ofNullable(maintenanceLogs).orElseGet(Collections::emptyList).stream()
        .map(ESMaintenanceLog::getServiceType)
        .map(maintenanceTypeMap::get)
        .filter(Objects::nonNull)
        .map(this::toMaintenanceTypeResponse)
        .collect(Collectors.toList());
  }
}
