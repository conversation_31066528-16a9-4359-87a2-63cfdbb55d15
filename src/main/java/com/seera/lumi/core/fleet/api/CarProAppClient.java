package com.seera.lumi.core.fleet.api;

import static org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE;

import com.seera.lumi.core.fleet.api.fallback.CarProAppClientFallback;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/** External CarPro Application Client */
@Component
@FeignClient(
    name = "CarProAppClient",
    url = "${api.carpro.app.base.url}",
    dismiss404 = true,
    fallbackFactory = CarProAppClientFallback.class)
public interface CarProAppClient {

  @GetMapping(value = "/Magic94Scripts/mgrqispi94.dll", consumes = APPLICATION_OCTET_STREAM_VALUE)
  Response downloadFile(
      @RequestParam("appName") String appName,
      @RequestParam("prgName") String prgName,
      @RequestParam("arguments") String arguments);
}
