package com.seera.lumi.core.fleet.mapper;

import com.seera.lumi.core.fleet.dto.*;
import com.seera.lumi.core.fleet.entity.opensearch.*;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VehicleMapper {

  //  @Named("changeEmptyToNull")
  //  default String changeEmptyToNull(String value) {
  //    return isEmpty(value) ? null : value;
  //  }

  @Mapping(target = "km", source = "mileage")
  VehicleDTO toDTO(VehicleLiveTrackingEventDTO vehicle);

  default void map(@NonNull VehicleDTO src, ESVehicleData target) {
    if (src.getPlateNo() != null) {
      target.setPlateNo(src.getPlateNo());
    }
    if (src.getAssetId() != null) {
      target.setAssetId(src.getAssetId());
    }
    if (src.getModelId() != null) {
      target.setModelId(src.getModelId());
    }
    if (src.getYear() != null) {
      target.setYear(src.getYear());
    }
    if (StringUtils.isNotBlank(src.getCarGroup())) {
      target.setCarGroup(src.getCarGroup());
    }
    if (src.getKm() != null) {
      target.setKm(src.getKm());
    }
    if (src.getColor() != null) {
      target.setColor(src.getColor());
    }
    if (src.getChassisNo() != null) {
      target.setChassisNo(src.getChassisNo());
    }
    if (src.getServiceType() != null) {
      target.setServiceType(src.getServiceType());
    }
    if (src.getTransmissionType() != null) {
      target.setTransmissionType(src.getTransmissionType());
    }
    if (src.getNbv() != null) {
      target.setNbv(src.getNbv());
    }
    if (src.getAdv() != null) {
      target.setAdv(src.getAdv());
    }
    if (src.getPurchasePrice() != null) {
      target.setPurchasePrice(src.getPurchasePrice());
    }
    if (src.getPurchaseDate() != null) {
      target.setPurchaseDate(src.getPurchaseDate());
    }
    if (src.getLastMaintenance() != null) {
      target.setLastMaintenance(toLastMaintenance(src.getLastMaintenance()));
    }
    if (src.getMake() != null) {
      target.setMake(toMakeResponse(src));
    }
    if (src.getModel() != null) {
      target.setModel(toModelResponse(src));
    }
  }

  default ESLastMaintenanceLog toLastMaintenance(LastMaintenanceLogDTO lastMaintenance) {
    ESLastMaintenanceLog lastMaintenanceLog = new ESLastMaintenanceLog();

    lastMaintenanceLog.setKm(lastMaintenance.getKm());
    lastMaintenanceLog.setType(lastMaintenance.getType());
    lastMaintenanceLog.setDate(lastMaintenance.getDate());
    if (lastMaintenance.getNotification() != null) {
      lastMaintenanceLog.setNotification(
          toLastMaintenanceNotification(lastMaintenance.getNotification()));
    }
    return lastMaintenanceLog;
  }

  default ESLastMaintenanceNotification toLastMaintenanceNotification(
      LastMaintenanceNotificationDTO lastMaintenanceNotificationDTO) {
    ESLastMaintenanceNotification lastMaintenanceNotification = new ESLastMaintenanceNotification();

    lastMaintenanceNotification.setDate(lastMaintenanceNotificationDTO.getDate());
    lastMaintenanceNotification.setKm(lastMaintenanceNotificationDTO.getKm());

    return lastMaintenanceNotification;
  }

  @Mapping(source = "model", target = "name.en")
  ESVehicleModel toModelResponse(VehicleDTO src);

  @Mapping(source = "make", target = "name.en")
  ESVehicleMake toMakeResponse(VehicleDTO src);

  //  @Named("sanitizeString")
  //  default String sanitizeString(String name) {
  //    return name.replace(" ", " ").trim();
  //  }

  @Mapping(target = "lastMaintenance", source = "esVehicleData.lastMaintenance", ignore = true)
  VehicleBasicResponseDTO toVehicleBasicResponse(ESVehicleData esVehicleData);

  //  List<VehicleBasicResponseDTO> toVehicleBasicResponse(List<ESVehicleData> esVehicleData);
}
