package com.seera.lumi.core.fleet.listeners;

import com.seera.lumi.core.cache.service.CacheService;
import com.seera.lumi.core.fleet.dto.VehicleDTO;
import com.seera.lumi.core.fleet.dto.VehicleLiveTrackingEventDTO;
import com.seera.lumi.core.fleet.mapper.VehicleMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VehicleTrackingDataListener {

  public static final int SIGNIFICANT_KM_CHANGE = 500000;
  public static final String CACHE_LAST_SIGNIFICANT_KM = "LAST_SIGNIFICANT_KM:";
  private static final Logger log = LoggerFactory.getLogger("application");
  private final VehicleMapper vehicleMapper;
  private final CacheService cacheService;

  //  @Value("${kafka.topic.vehicle.data}")
  //  private String vehicleDataTopic;

  //  @KafkaListener(
  //      topics = {"${kafka.topic.vehicle.tracking.data}"},
  //      groupId = "publish-kilometer-update-in-vehicle-data",
  //      concurrency = "${kafka.listen.concurrency}",
  //      containerFactory = "kafkaListenerContainerFactory",
  //      autoStartup = "${kafka.listen.auto.start:true}")
  public void listen(
      @Payload VehicleLiveTrackingEventDTO event, @Headers MessageHeaders eventHeaders) {

    try {
      //    log.info("Message received, Value:{} Headers:{}", event, eventHeaders);

      if (!isValid(event)) {
        log.warn("[*WARNING*][L2] Not a valid event");
        return;
      }

      VehicleDTO vehicleData = extractVehicleData(event);
      if (!isKmChangeSignificant(vehicleData)) {
        return;
      }

      // publishKmChangeToVehicleData(vehicleData);
    } catch (Exception e) {
      log.error("[*WARNING*][L2] Error while mapping vehicle tracking data to vehicle", e);
    }
  }

  //  private void publishKmChangeToVehicleData(VehicleDTO vehicleData) {
  //    cacheService.put(CACHE_LAST_SIGNIFICANT_KM + vehicleData.getPlateNo(), vehicleData.getKm(),
  // 1,
  //        TimeUnit.DAYS);
  //    eventProducer.send(vehicleDataTopic, vehicleData.getPlateNo(), vehicleData);
  //  }

  private VehicleDTO extractVehicleData(VehicleLiveTrackingEventDTO event) {
    return vehicleMapper.toDTO(event);
  }

  private boolean isValid(VehicleLiveTrackingEventDTO event) {
    return event != null && !StringUtils.isEmpty(event.getPlateNo());
  }

  private boolean isKmChangeSignificant(VehicleDTO vehicleData) {
    Integer lastKm =
        (Integer) cacheService.get(CACHE_LAST_SIGNIFICANT_KM + vehicleData.getPlateNo());
    return lastKm == null || vehicleData.getKm() - lastKm > SIGNIFICANT_KM_CHANGE;
  }
}
