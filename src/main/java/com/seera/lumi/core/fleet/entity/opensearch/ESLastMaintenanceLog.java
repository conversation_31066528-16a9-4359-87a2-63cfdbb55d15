package com.seera.lumi.core.fleet.entity.opensearch;

import java.time.LocalDate;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

@Getter
@Setter
@Accessors(chain = true)
public class ESLastMaintenanceLog {

  private String id;
  private Long glNo;
  private Integer km;
  private String type;
  private String remarks;

  @Field(type = FieldType.Date, format = DateFormat.date, pattern = "yyyy-MM-dd")
  private LocalDate date;

  @Field(type = FieldType.Object)
  private ESLastMaintenanceNotification notification;
}
