package com.seera.lumi.core.fleet.api;

import com.seera.lumi.core.fleet.api.fallback.ContentClientFallback;
import com.seera.lumi.core.fleet.api.response.ContentResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@FeignClient(
    name = "ContentClient",
    url = "${api.content.service.base.url}",
    fallbackFactory = ContentClientFallback.class)
@Component
public interface ContentClient {

  @PostMapping(value = "/v2/content/{folder}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  ResponseEntity<ContentResponse<String>> uploadPrivate(
      @RequestHeader(value = "Authorization") String authHeader,
      @PathVariable("folder") String folder,
      @RequestPart("file") MultipartFile file);

  @PostMapping(value = "/content/sign")
  ResponseEntity<ContentResponse<String>> signedUrl(@RequestBody String url);
}
