package com.seera.lumi.core.fleet.business.impl;

import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.mapper.VehicleDocumentMapper;
import com.lumi.rental.core.fleet.request.CreateUpdateVehicleDocument;
import com.seera.lumi.core.fleet.service.VehicleDocumentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.lumi.rental.core.fleet.exception.BaseError.INVALID_ARG_VALUE;

@Component
@RequiredArgsConstructor
public class CreateDocumentService
    extends AbstractBusinessService<CreateUpdateVehicleDocument, CreateUpdateVehicleDocument> {

  private final VehicleDocumentService documentService;
  private final VehicleDocumentMapper vehicleDocumentMapper;

  @Override
  public CreateUpdateVehicleDocument execute(CreateUpdateVehicleDocument documentDTO) {
    return null;
    //        return
    // vehicleDocumentMapper.toDTO(documentService.save(vehicleDocumentMapper.toEntity(documentDTO)));
  }

  @Override
  void validateRequest(CreateUpdateVehicleDocument documentDTO) {
    if (Objects.isNull(documentDTO)) {
      throw new BusinessException(INVALID_ARG_VALUE, "documentDTO");
    }
  }
}
