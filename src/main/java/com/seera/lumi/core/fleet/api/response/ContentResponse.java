package com.seera.lumi.core.fleet.api.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContentResponse<T> implements Serializable {

  @Serial private static final long serialVersionUID = 6418695469322527159L;

  private T data;
  private String message;

  public ContentResponse(T data) {
    this.data = data;
  }
}
