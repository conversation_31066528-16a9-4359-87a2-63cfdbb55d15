package com.seera.lumi.core.fleet.dto;

import lombok.EqualsAndHashCode;

import java.io.Serial;

@EqualsAndHashCode(callSuper = true)
public class DocumentCategorySearchRequestDTO extends SearchRequestDTO {

  @Serial private static final long serialVersionUID = -650126160341757685L;
  String code;
  String name;

  public String getCode() {
    return code;
  }

  public DocumentCategorySearchRequestDTO setCode(String code) {
    this.code = code;
    return this;
  }

  public String getName() {
    return name;
  }

  public DocumentCategorySearchRequestDTO setName(String name) {
    this.name = name;
    return this;
  }
}
