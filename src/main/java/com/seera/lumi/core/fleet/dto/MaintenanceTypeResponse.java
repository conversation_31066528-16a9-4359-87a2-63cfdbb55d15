package com.seera.lumi.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.lumi.rental.core.fleet.dto.MultilingualDTO;
import java.io.Serial;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MaintenanceTypeResponse implements Serializable {

  @Serial private static final long serialVersionUID = -600126160341757685L;

  private Long id;
  private MultilingualDTO name;
}
