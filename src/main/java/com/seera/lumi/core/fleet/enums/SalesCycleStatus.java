package com.seera.lumi.core.fleet.enums;

import static java.util.Arrays.stream;
import static org.apache.commons.lang3.StringUtils.trim;

public enum SalesCycleStatus {
  READY("Ready"),
  RENTED("Rented"),
  STOLEN("Stolen"),
  IN_SALE_CYCLE("In Sale Cycle"),
  AT_CUSTOMER("At Customer"),
  OUT_OF_SERVICE("Out Of Service"),
  SOLD("Sold"),
  AT_FOREIGN_BRANCH("At Foreign Branch"),
  RESERVED("Reserved"),
  PRE_CHECKED_IN("Pre Checked In"),
  FOREIGN_CAR_RETURNED("Foreign Car Returned"),
  TOTAL_LOSS("TOTAL LOSS");

  private final String saleCycleStatus;

  SalesCycleStatus(String saleCycleStatus) {
    this.saleCycleStatus = saleCycleStatus;
  }

  public static SalesCycleStatus resolveSalesCycleStatus(String key) {
    return stream(SalesCycleStatus.values())
        .filter(statusEnum -> statusEnum.getSaleCycleStatus().equals(trim(key)))
        .findFirst()
        .orElse(null);
  }

  public String getSaleCycleStatus() {
    return saleCycleStatus;
  }
}
