package com.seera.lumi.core.fleet.business.impl;

import static org.apache.commons.lang3.StringUtils.trim;

import com.lumi.rental.core.fleet.exception.BaseError;
import com.lumi.rental.core.fleet.exception.BusinessException;
import com.lumi.rental.core.fleet.mapper.VehicleDocumentMapper;
import com.lumi.rental.core.fleet.request.CreateUpdateVehicleDocument;
import com.seera.lumi.core.fleet.service.VehicleDocumentService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SearchDocumentService
    extends AbstractBusinessService<String, List<CreateUpdateVehicleDocument>> {

  private final VehicleDocumentService vehicleDocumentService;
  private final VehicleDocumentMapper vehicleDocumentMapper;

  @Override
  public List<CreateUpdateVehicleDocument> execute(String plateNo) {
    return null;
    //        return
    // vehicleDocumentService.findByPlateNo(trim(plateNo)).stream().map(vehicleDocumentMapper::toDTO).toList();
  }

  @Override
  void validateRequest(String plateNo) {
    if (StringUtils.isEmpty(trim(plateNo))) {
      throw new BusinessException(BaseError.INVALID_ARG_VALUE, "PlateNo");
    }
  }
}
