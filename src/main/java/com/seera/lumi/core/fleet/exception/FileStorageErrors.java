package com.seera.lumi.core.fleet.exception;

import com.lumi.rental.core.fleet.exception.BaseError;
import org.springframework.http.HttpStatus;

public class FileStorageErrors extends BaseError {

  public static final BaseError SERVICE_NOT_FOUND =
      new FileStorageErrors(
          3001, "error.invalid.storage.service", HttpStatus.INTERNAL_SERVER_ERROR);

  protected FileStorageErrors(int code, String desc, HttpStatus httpStatus) {
    super(code, desc, httpStatus);
  }
}
