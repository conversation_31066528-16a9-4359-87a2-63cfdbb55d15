package com.seera.lumi.core.fleet.exception;

import com.lumi.rental.core.fleet.exception.BaseError;
import org.springframework.http.HttpStatus;

public class DocumentErrors extends BaseError {

  public static final BaseError INVALID_DOCUMENT =
      new DocumentErrors(4001, "Invalid document", HttpStatus.BAD_REQUEST);
  public static final BaseError DOCUMENTS_UPLOAD_FAILED =
      new DocumentErrors(4002, "Document upload failed", HttpStatus.BAD_REQUEST);

  public static final BaseError PLATE_NO_IS_REQUIRED =
      new DocumentErrors(4003, "Plate no cannot be empty", HttpStatus.BAD_REQUEST);

  public DocumentErrors(int code, String desc, HttpStatus httpStatus) {
    super(code, desc, httpStatus);
  }
}
