package com.seera.lumi.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.lumi.rental.core.fleet.dto.MultilingualDTO;
import java.util.HashSet;
import java.util.Set;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VehicleFilterResponseDTO {

  Set<MultilingualDTO> makes = new HashSet<>();
  Set<Long> modelYears = new HashSet<>();
  Set<MultilingualDTO> models = new HashSet<>();
  Set<String> carGroups = new HashSet<>();
}
