package com.seera.lumi.core.fleet.api.fallback;

import com.seera.lumi.core.fleet.api.ContentClient;
import com.seera.lumi.core.fleet.api.response.ContentResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Component
public class ContentClientFallback implements FallbackFactory<ContentClient> {

  private static final Logger log = LoggerFactory.getLogger("application");

  @Override
  public ContentClient create(Throwable cause) {
    return new ContentClient() {

      @Override
      public ResponseEntity<ContentResponse<String>> uploadPrivate(
          String authHeader, String folder, MultipartFile multipartFile) {
        log.error("Error uploading private files:", cause);
        return ResponseEntity.ok(new ContentResponse(null));
      }

      @Override
      public ResponseEntity<ContentResponse<String>> signedUrl(String url) {
        log.error("Error while generating signed url:", cause);
        return ResponseEntity.ok(new ContentResponse(null));
      }
    };
  }
}
