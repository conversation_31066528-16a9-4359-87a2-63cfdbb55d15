package com.seera.lumi.core.fleet.api.fallback;

import com.seera.lumi.core.fleet.api.AuthenticationClient;
import com.seera.lumi.core.fleet.api.request.PasswordLoginKeycloakUserRequest;
import com.seera.lumi.core.fleet.api.response.KeycloakTokenResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
public class AuthenticationClientFallback implements FallbackFactory<AuthenticationClient> {

  private static final Logger log = LoggerFactory.getLogger("application");

  @Override
  public AuthenticationClient create(Throwable cause) {
    log.debug("Unable to get response from auth service:", cause);
    return new AuthenticationClient() {

      @Override
      public ResponseEntity<KeycloakTokenResponse> login(PasswordLoginKeycloakUserRequest request) {
        return ResponseEntity.ok(null);
      }
    };
  }
}
