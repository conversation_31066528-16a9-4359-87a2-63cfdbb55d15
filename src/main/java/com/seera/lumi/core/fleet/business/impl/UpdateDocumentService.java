package com.seera.lumi.core.fleet.business.impl;

import com.lumi.rental.core.fleet.request.CreateUpdateVehicleDocument;
import com.lumi.rental.core.fleet.mapper.VehicleDocumentMapper;
import com.seera.lumi.core.fleet.service.VehicleDocumentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UpdateDocumentService
    extends AbstractBusinessService<CreateUpdateVehicleDocument, CreateUpdateVehicleDocument> {

  private final VehicleDocumentService documentTypeService;
  private final VehicleDocumentMapper vehicleDocumentMapper;

  @Override
  public CreateUpdateVehicleDocument execute(CreateUpdateVehicleDocument documentTypeDTO) {
    //        return
    // documentTypeService.partialUpdate(vehicleDocumentMapper.toEntity(documentTypeDTO))
    //                .map(vehicleDocumentMapper::toDTO).get();
    return null;
  }

  @Override
  void validateRequest(CreateUpdateVehicleDocument documentTypeDTO) {}
}
