package com.seera.lumi.core.fleet.api.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KeycloakTokenResponse implements Serializable {

  @Serial private static final long serialVersionUID = -7931366895734447538L;
  private String token;
  private long expiresIn;
  private long refreshExpiresIn;
  private String refreshToken;

  private String tokenType;

  private String idToken;

  private int notBeforePolicy;

  private String sessionState;
  private Map<String, Object> otherClaims = new HashMap<>();

  private String scope;

  private String error;

  private String errorDescription;

  private String errorUri;
}
