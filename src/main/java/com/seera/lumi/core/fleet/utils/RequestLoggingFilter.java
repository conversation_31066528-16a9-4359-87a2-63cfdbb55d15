// package com.seera.lumi.core.fleet.utils;
//
// import jakarta.servlet.http.HttpServletRequest;
// import org.springframework.web.filter.CommonsRequestLoggingFilter;
//
// public class RequestLoggingFilter extends CommonsRequestLoggingFilter {
//  @Override
//  protected boolean shouldLog(HttpServletRequest request) {
//    return !request.getRequestURI().contains("actuator") && super.shouldLog(request);
//  }
// }
