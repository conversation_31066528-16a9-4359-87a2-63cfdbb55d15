package com.seera.lumi.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDate;
import lombok.ToString;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LastMaintenanceLogDTO {

  private Integer km;
  private String type;

  @Field(type = FieldType.Date, format = DateFormat.date, pattern = "yyyy-MM-dd")
  private LocalDate date;

  private LastMaintenanceNotificationDTO notification;

  public Integer getKm() {
    return km;
  }

  public LastMaintenanceLogDTO setKm(Integer km) {
    this.km = km;
    return this;
  }

  public String getType() {
    return type;
  }

  public LastMaintenanceLogDTO setType(String type) {
    this.type = type;
    return this;
  }

  public LocalDate getDate() {
    return date;
  }

  public LastMaintenanceLogDTO setDate(LocalDate date) {
    this.date = date;
    return this;
  }

  public LastMaintenanceNotificationDTO getNotification() {
    return notification;
  }

  public LastMaintenanceLogDTO setNotification(LastMaintenanceNotificationDTO notification) {
    this.notification = notification;
    return this;
  }
}
