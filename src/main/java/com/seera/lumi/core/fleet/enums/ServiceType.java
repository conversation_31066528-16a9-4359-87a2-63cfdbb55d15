package com.seera.lumi.core.fleet.enums;

import static java.util.Arrays.stream;
import static org.apache.commons.lang3.StringUtils.trim;

public enum ServiceType {
  LEASE("Lease"),
  RENTAL("Rental"),
  LIMO("Limo");

  private final String type;

  ServiceType(String type) {
    this.type = type;
  }

  public static ServiceType resolveServiceType(String key) {
    if ("RENTAL_VEHICLE".equals(key)) { // To support existing V1 API mapper for vehicle.
      return RENTAL;
    }
    return stream(ServiceType.values())
        .filter(typeEnum -> typeEnum.getType().equals(trim(key)))
        .findFirst()
        .orElse(null);
  }

  public String getType() {
    return type;
  }
}
