package com.seera.lumi.core.fleet.opensearch;

import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.opensearch.search.sort.SortOrder.ASC;
import static org.opensearch.search.sort.SortOrder.DESC;

import java.io.IOException;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.index.query.QueryBuilder;
import org.opensearch.search.aggregations.AggregationBuilder;
import org.opensearch.search.aggregations.PipelineAggregationBuilder;
import org.opensearch.search.builder.SearchSourceBuilder;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OpenSearchUtil {

  public static final int DEFAULT_OS_FETCH_SIZE = 10000;

  public static SearchResponse searchData(
      String indexName,
      QueryBuilder queryBuilder,
      List<AggregationBuilder> aggregationBuilders,
      List<PipelineAggregationBuilder> pipelineAggregationBuilders,
      boolean fetchSource,
      RestHighLevelClient restClient) {
    return searchData(
        new OpenSearchFetchRequest(restClient, indexName)
            .setQueryBuilder(queryBuilder)
            .setAggregationBuilders(aggregationBuilders)
            .setPipelineAggregationBuilders(pipelineAggregationBuilders)
            .setFetchSource(fetchSource)
            .setFrom(0)
            .setSize(0));
  }

  public static SearchResponse searchData(OpenSearchFetchRequest request) {
    var searchRequest = new SearchRequest(request.getIndexName());
    var searchSourceBuilder = new SearchSourceBuilder();
    searchSourceBuilder.fetchSource(request.isFetchSource());
    searchSourceBuilder.query(request.getQueryBuilder());
    if (nonNull(request.getPageable())) {
      searchSourceBuilder.from(
          request.getPageable().getPageNumber() * request.getPageable().getPageSize());
      searchSourceBuilder.size(request.getPageable().getPageSize());
      request.getPageable().getSort();
      request
          .getPageable()
          .getSort()
          .forEach(
              order ->
                  searchSourceBuilder.sort(order.getProperty(), order.isAscending() ? ASC : DESC));
    } else {
      searchSourceBuilder.from(request.getFrom());
      searchSourceBuilder.size(request.getSize());
    }
    searchSourceBuilder.trackTotalHits(request.isIncludeTotalCount());
    if (isNotEmpty(request.getIncludeKeys())) {
      searchSourceBuilder.fetchSource(
          request.getIncludeKeys().toArray(String[]::new), List.of().toArray(String[]::new));
    }
    if (isNotEmpty(request.getAggregationBuilders())) {
      request.getAggregationBuilders().forEach(searchSourceBuilder::aggregation);
    }
    if (isNotEmpty(request.getPipelineAggregationBuilders())) {
      request.getPipelineAggregationBuilders().forEach(searchSourceBuilder::aggregation);
    }
    searchRequest.source(searchSourceBuilder);
    try {
      return request.getRestClient().search(searchRequest, RequestOptions.DEFAULT);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  public static SearchResponse deepSearchData(OpenSearchFetchRequest request) {
    var searchRequest = new SearchRequest(request.getIndexName());
    var searchSourceBuilder = new SearchSourceBuilder();
    searchSourceBuilder.fetchSource(request.isFetchSource());
    searchSourceBuilder.query(request.getQueryBuilder());
    searchSourceBuilder.from(0);
    searchSourceBuilder.size(request.getPageable().getPageSize());
    if (isNotEmpty(request.getSearchAfter())) {
      searchSourceBuilder.searchAfter(request.getSearchAfter().toArray());
    }
    request.getPageable().getSort();
    request
        .getPageable()
        .getSort()
        .forEach(
            order ->
                searchSourceBuilder.sort(order.getProperty(), order.isAscending() ? ASC : DESC));
    searchSourceBuilder.trackTotalHits(request.isIncludeTotalCount());

    if (isNotEmpty(request.getIncludeKeys())) {
      searchSourceBuilder.fetchSource(
          request.getIncludeKeys().toArray(String[]::new), List.of().toArray(String[]::new));
    }
    if (isNotEmpty(request.getAggregationBuilders())) {
      request.getAggregationBuilders().forEach(searchSourceBuilder::aggregation);
    }
    if (isNotEmpty(request.getPipelineAggregationBuilders())) {
      request.getPipelineAggregationBuilders().forEach(searchSourceBuilder::aggregation);
    }
    searchRequest.source(searchSourceBuilder);
    try {
      return request.getRestClient().search(searchRequest, RequestOptions.DEFAULT);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  public static String lastNDaysDateLiteral(Integer days) {
    return "now-" + days + "d";
  }
}
