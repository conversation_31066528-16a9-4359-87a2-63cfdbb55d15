package com.seera.lumi.core.fleet.dto;

import java.io.Serial;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SortRequestDTO extends PageRequestDTO {
  @Serial private static final long serialVersionUID = -635323273079722183L;

  private List<String> sort = List.of("id");
  private String order = "desc";

  public String[] getSort() {
    return sort == null ? new String[] {"id"} : sort.toArray(String[]::new);
  }

  @Override
  public String toString() {
    final StringBuilder sb =
        new StringBuilder(super.toString())
            .append("SortRequestDTO{")
            .append("sort='")
            .append(sort)
            .append('\'')
            .append(", order='")
            .append(order)
            .append('\'')
            .append('}')
            .append(super.toString());
    return sb.toString();
  }
}
