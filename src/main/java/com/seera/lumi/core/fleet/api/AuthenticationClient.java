package com.seera.lumi.core.fleet.api;

import com.lumi.rental.core.fleet.config.feign.FeignConfig;
import com.seera.lumi.core.fleet.api.fallback.AuthenticationClientFallback;
import com.seera.lumi.core.fleet.api.request.PasswordLoginKeycloakUserRequest;
import com.seera.lumi.core.fleet.api.response.KeycloakTokenResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(
    contextId = "auth",
    configuration = {FeignConfig.class},
    name = "AuthenticationClient",
    url = "${api.authentication.service.base.url}",
    fallbackFactory = AuthenticationClientFallback.class)
@Component
public interface AuthenticationClient {

  @PostMapping("/user/auth/login")
  ResponseEntity<KeycloakTokenResponse> login(PasswordLoginKeycloakUserRequest request);
}
