package com.seera.lumi.core.fleet.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ESDeepSearchRequestDTO extends SortRequestDTO {

  List<Object> searchAfter = new ArrayList<>();

  @Override
  public String toString() {
    final StringBuilder sb =
        new StringBuilder(super.toString())
            .append("DeepSearchRequestDTO{")
            .append("searchAfter='")
            .append(searchAfter)
            .append('\'')
            .append('}')
            .append(super.toString());
    return sb.toString();
  }
}
