package com.seera.lumi.core.fleet.service.impl;

import com.lumi.rental.core.fleet.entity.MaintenanceType;
import com.lumi.rental.core.fleet.repository.MaintenanceTypeRepository;
import com.seera.lumi.core.fleet.dto.MaintenanceTypeResponse;
import com.seera.lumi.core.fleet.mapper.VehicleMaintenanceLogMapper;
import com.seera.lumi.core.fleet.service.MaintenanceTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

@Service
@RequiredArgsConstructor
public class MaintenanceTypeServiceImpl implements MaintenanceTypeService {

  private final MaintenanceTypeRepository repository;
  private final VehicleMaintenanceLogMapper vehicleMaintenanceLogMapper;

  @Override
  public Map<String, MaintenanceType> maintenanceTypeOnServiceType() {
    return repository.findAll().stream()
        .collect(toMap(type -> type.getNameEn().trim(), identity(), (n, o) -> n));
  }
}
