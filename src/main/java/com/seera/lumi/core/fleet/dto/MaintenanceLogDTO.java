package com.seera.lumi.core.fleet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MaintenanceLogDTO implements Serializable {

  @Serial private static final long serialVersionUID = 42L;

  private Long id;
  private String plateNo;
  private LocalDateTime date;
  private String remarks;
  private Integer km;
  private Integer orderId;
  private Set<MaintenanceLogTypeDTO> maintenanceLogTypes;
}
