environment: dev
service:
  type: ClusterIP
  scheme: HTTP
  port: 80


#Kong Configs
kongService:
  enabled: true
  type: ExternalName
  origin: istio-ingressgateway.istio-system.svc.cluster.local

apiKongIngress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/preserve-host: "false"
    konghq.com/https-redirect-status-code: "301"
  hosts:
    api-dev.lumirental.com:
      - /core-fleet-service/
    lumi-api:
      - /core-fleet-service/

hpa:
  enabled: true
  targetCPUUtilizationPercentage: 80
  TargetAvgMemoryUtilization: 80
replicaCount: 1
maxReplicas: 2

resources:
  requests:
    cpu: 300m
    memory: 1000Mi
  limits:
    cpu: 500m
    memory: 1500Mi

environment_variable:
  enabled: true
  data:
    APP_NAME: "lumi-core-fleet"
    ACTIVE_PROFILE: "dev"
    SERVER_PORT: "80"
    SWAGGER_HOST: "api-dev.lumirental.com/core-fleet-service"
    SPRING_REDIS_HOST: "lumi-core-redis"
    SPRING_REDIS_PORT: "6379"
    KEYCLOAK_URL: "https://keycloak.dev.lumirental.com"
    LEASE_KEYCLOAK_CONFIG_PATH: "/app/keycloak-lease.json"
    LEASE_KEYCLOAK_POLICY_ENFORCER_CONFIG_PATH: "/app/policy-enforcer-lease.json"
    LUMI_KEYCLOAK_CONFIG_PATH: "/app/keycloak-lumi.json"
    LUMI_KEYCLOAK_POLICY_ENFORCER_CONFIG_PATH: "/app/policy-enforcer-lumi.json"
    SPRING_DATASOURCE_URL: "**************************************************************************************************************************************************************"
    SPRING_DATASOURCE_USERNAME: "lumi-core-fleet-service"
    DB_TIMEOUT: "20000"
    DB_MIN_IDLE: "10"
    DB_IDLE_TIME: "10000"
    DB_MAX_LIFE: "30000"
    SWAGGER_CONTEXT: "/core-fleet-service"
    OPENSEARCH_HOST: "opensearch-nonprod.lumirental.io:443"
    KAFKA_HOST: "lumi-lease-kafka-cluster:9092"
    DEFAULT_KAFKA_HOST: "lumi-kafka-cluster:9092"
    DLT_AUTO_START: "true"
    KAFKA_LISTENER_CONCURRENCY: "1"
    KAFKA_LISTENER_SINGLE_CONCURRENCY: "1"
    KAFKA_PARTITION: "1"
    KAFKA_REPLICATION: "1"
    S3_FLEET_DOCUMENTS_BUCKET: "dev-fleet-documents"
    AWS_CDN_CONTENT_URL: "https://cdn-dev.lumirental.com/"
    AWS_PEM_KEY_PATH: "/app/signing-certificate.pem"
    S3_REGION: "me-south-1"
    CAR_PRO_APP_BASE_URL: "http://**********"
    CONTENT_SERVICE: "http://lumi-ultra-content-service.backend.svc.cluster.local"
    AUTHENTICATION_SERVICE_BASEURL: "http://lumi-core-auth-service.core.svc.cluster.local"
    AUTHENTICATION_SERVICE_USER_USERNAME: "lumi-fleet-service"
    CORE_AGREEMENT_SERVICE: "http://lumi-core-agreement-service.core.svc.cluster.local"
    BRANCH_SERVICE: "http://lumi-ultra-branch-service.backend.svc.cluster.local"
    USER_SERVICE: "http://lumi-core-user-service.core.svc.cluster.local"
    NEW_RELIC_AGENT_ENABLED: "true"
    NEW_RELIC_APP_NAME: "Core-Fleet-Service (Dev)"
    NEW_RELIC_LICENSE_KEY: "eu01xx474c26cd7aff0239160a530e7cFFFFNRAL"
    NEW_RELIC_DISTRIBUTED_TRACING_ENABLED: "false"

log_alerts:
  enabled: true
  rules: |
    groups:
      - name: FLEET_UPSTREAM_5XX
        rules:
          - alert: FLEET_UPSTREAM_5XX
            expr: |
              sum by (app,namespace,log,pod) (rate({service="lumi-core-fleet-service"} | json | message_logtype = `UPSTREAM_REQRESP` | message_eventtype = `ResponseMessage` | message_status =~ `5.*` | regexp "(?P<log>.*)"  [5m]))
            for: 0m
            labels:
              severity: critical
              category: logs
            annotations:
              summary: "5XX UPSTREAM ERROR"
              description: "Unable to serve request:Fleet-Service"
              dashboard: "https://grafana.dev.lumirental.io/explore?schemaVersion=1&panes=%7B%22UI3%22:%7B%22datasource%22:%22Jx6g8ACnz%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bservice%3D%5C%22lumi-core-fleet-service%5C%22%7D%20%7C%20json%20%7C%20message_logtype%20%3D%20%60UPSTREAM_REQRESP%60%20%7C%20message_eventtype%20%3D%20%60ResponseMessage%60%20%7C%20message_status%20%3D~%20%605.%2A%60%22,%22queryType%22:%22range%22,%22datasource%22:%7B%22type%22:%22loki%22,%22uid%22:%22Jx6g8ACnz%22%7D,%22editorMode%22:%22code%22%7D%5D,%22range%22:%7B%22from%22:%22now-15m%22,%22to%22:%22now%22%7D%7D%7D&orgId=1"
      - name: FLEET_DOWNSTREAM_HIGH_LATENCY
        rules:
          - alert: FLEET_DOWNSTREAM_HIGH_LATENCY
            expr: |
              sum by (app,namespace,log,pod) (rate({service="lumi-core-fleet-service"} | json | message_logtype = `DOWNSTREAM_REQRESP` | message_eventtype = `ResponseMessage` | message_totalduration > 2000 | regexp "(?P<log>.*)"  [5m]))
            for: 0m
            labels:
              severity: critical
              category: logs
            annotations:
              summary: "Increased Latency in Fleet-Downstream API Responses"
              description: "The average response time for Fleet Downstream APIs has exceeded than 2s"
              dashboard: "https://grafana.dev.lumirental.io/explore?schemaVersion=1&panes=%7B%22UI3%22:%7B%22datasource%22:%22Jx6g8ACnz%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bservice%3D%5C%22lumi-core-fleet-service%5C%22%7D%20%7C%20json%20%7C%20message_logtype%20%3D%20%60DOWNSTREAM_REQRESP%60%20%7C%20message_eventtype%20%3D%20%60ResponseMessage%60%20%7C%20message_totalduration%20%3E%202000%22,%22queryType%22:%22range%22,%22datasource%22:%7B%22type%22:%22loki%22,%22uid%22:%22Jx6g8ACnz%22%7D,%22editorMode%22:%22builder%22%7D%5D,%22range%22:%7B%22from%22:%22now-6h%22,%22to%22:%22now%22%7D%7D%7D&orgId=1"
      - name: FLEET_DOWNSTREAM_5XX
        rules:
          - alert: FLEET_DOWNSTREAM_5XX
            expr: |
              sum by (app,namespace,log,pod) (rate({service="lumi-core-fleet-service"} | json | message_logtype = `DOWNSTREAM_REQRESP` | message_eventtype = `ResponseMessage` | message_status =~ `5.*` | regexp "(?P<log>.*)"  [5m]))
            for: 0m
            labels:
              severity: critical
              category: logs
            annotations:
              summary: "5XX DOWNSTREAM ERROR"
              description: "Fleet Downstream service failure"
              dashboard: "https://grafana.dev.lumirental.io/explore?schemaVersion=1&panes=%7B%22UI3%22:%7B%22datasource%22:%22Jx6g8ACnz%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bservice%3D%5C%22lumi-core-fleet-service%5C%22%7D%20%7C%20json%20%7C%20message_logtype%20%3D%20%60UPSTREAM_REQRESP%60%20%7C%20message_eventtype%20%3D%20%60ResponseMessage%60%20%7C%20message_status%20%3D~%20%604.%2A%60%22,%22queryType%22:%22range%22,%22datasource%22:%7B%22type%22:%22loki%22,%22uid%22:%22Jx6g8ACnz%22%7D,%22editorMode%22:%22code%22%7D%5D,%22range%22:%7B%22from%22:%22now-30m%22,%22to%22:%22now%22%7D%7D%7D&orgId=1"
