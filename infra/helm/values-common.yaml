Component: lumi-core
SubComponent: fleet-service

readinessProbePath: /actuator/health
livenessProbePath: /actuator/health

livenessProbe:
  initialDelaySeconds: 120
  periodSeconds: 30
  timeoutSeconds: 5
  failureThreshold: 6
  successThreshold: 1
readinessProbe:
  initialDelaySeconds: 120
  periodSeconds: 30
  timeoutSeconds: 5
  failureThreshold: 6
  successThreshold: 1

secret_variable:
  enabled: true
  data:
    - SPRING_DATASOURCE_PASSWORD
    - AWS_KEY_PAIR
    - AWS_URL_EXPIRY
    - ES_USERNAME
    - ES_PASSWORD
    - AUTHENTICATION_SERVICE_USER_PASSWORD

istio:
  enabled: true
  virtualService:
    enabled: true
    destinationRule:
      trafficPolicy:
        tls:
          mode: DISABLE

  gateway:
    enabled: true
    host: "*"
    name: core-api-gateway
    match:
      - uri: /core-fleet-service/
        rewrite: /
  customHeaders:
    enabled: true
    headers:
      X-Forwarded-Prefix: /core-fleet-service/

deployment_custom_annotations:
  enabled: true
  annotations:
    prometheus.io/path: "/actuator/prometheus"
    prometheus.io/port: "80"
    prometheus.io/scrape: "true"


volumeMounts:
  - name: fleet-service-lumi-keycloak-secret
    mountPath: /app/keycloak-lumi.json
    subPath: keycloak-lumi.json
  - name: fleet-service-lumi-policy-enforcer
    mountPath: /app/policy-enforcer-lumi.json
    subPath: policy-enforcer-lumi.json
  - name: fleet-service-lease-keycloak-secret
    mountPath: /app/keycloak-lease.json
    subPath: keycloak-lease.json
  - name: fleet-service-lease-policy-enforcer
    mountPath: /app/policy-enforcer-lease.json
    subPath: policy-enforcer-lease.json
  - name: lumi-core-fleet-service-signing-certificate
    mountPath: /app/signing-certificate.pem
    subPath: signing-certificate.pem

volumes:
  - name: fleet-service-lumi-keycloak-secret
    secret:
      secretName: common-keycloak-secret-lumi
  - name: fleet-service-lumi-policy-enforcer
    configMap:
      name: fleet-service-lumi-policy-enforcer-cm
  - name: fleet-service-lease-keycloak-secret
    secret:
      secretName: common-keycloak-secret-lease
  - name: fleet-service-lease-policy-enforcer
    configMap:
      name: fleet-service-lease-policy-enforcer-cm
  - name: lumi-core-fleet-service-signing-certificate
    secret:
      secretName: lumi-core-fleet-service-signing-certificate


environment_variable:
  enabled: true
  data:
    CAR_PRO_SERVICE: "http://lumi-ultra-carpro-service.backend.svc.cluster.local"
    CORE_CAR_PRO_SERVICE: "http://lumi-core-carpro-service.core.svc.cluster.local"
    AI_SERVICE: "http://lumi-core-yaqeen-business.core.svc.cluster.local"

