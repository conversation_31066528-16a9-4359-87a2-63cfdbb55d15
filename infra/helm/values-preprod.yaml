environment: preprod
service:
  type: ClusterIP
  scheme: HTTP
  port: 80

#Kong Configs
kongService:
  enabled: true
  type: ExternalName
  origin: istio-ingressgateway.istio-system.svc.cluster.local

apiKongIngress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/preserve-host: "false"
    konghq.com/https-redirect-status-code: "301"
  hosts:
    api-preprod.lumirental.com:
      - /core-fleet-service/
    lumi-api:
      - /core-fleet-service/

hpa:
  enabled: true
  targetCPUUtilizationPercentage: 80
  TargetAvgMemoryUtilization: 80
replicaCount: 1
maxReplicas: 2

resources:
  requests:
    cpu: 300m
    memory: 1000Mi
  limits:
    cpu: 500m
    memory: 1500Mi

environment_variable:
  enabled: true
  data:
    APP_NAME: "lumi-core-fleet"
    ACTIVE_PROFILE: "preprod"
    SERVER_PORT: "80"
    SWAGGER_HOST: "api-preprod.lumirental.com/core-fleet-service"
    SPRING_REDIS_HOST: "lumi-core-redis"
    SPRING_REDIS_PORT: "6379"
    KEYCLOAK_URL: "https://keycloak.preprod.lumirental.com"
    LEASE_KEYCLOAK_CONFIG_PATH: "/app/keycloak-lease.json"
    LEASE_KEYCLOAK_POLICY_ENFORCER_CONFIG_PATH: "/app/policy-enforcer-lease.json"
    LUMI_KEYCLOAK_CONFIG_PATH: "/app/keycloak-lumi.json"
    LUMI_KEYCLOAK_POLICY_ENFORCER_CONFIG_PATH: "/app/policy-enforcer-lumi.json"
    SPRING_DATASOURCE_URL: "**************************************************************************************************************************************************************"
    SPRING_DATASOURCE_USERNAME: "lumi-core-fleet-service"
    DB_TIMEOUT: "20000"
    DB_MIN_IDLE: "10"
    DB_IDLE_TIME: "10000"
    DB_MAX_LIFE: "30000"
    SWAGGER_CONTEXT: "/core-fleet-service"
    OPENSEARCH_HOST: "opensearch-nonprod.lumirental.io:443"
    KAFKA_HOST: "lumi-lease-kafka-cluster:9092"
    DEFAULT_KAFKA_HOST: "lumi-kafka-cluster:9092"
    DLT_AUTO_START: "true"
    KAFKA_LISTENER_CONCURRENCY: "1"
    KAFKA_LISTENER_SINGLE_CONCURRENCY: "1"
    KAFKA_PARTITION: "1"
    KAFKA_REPLICATION: "1"
    S3_FLEET_DOCUMENTS_BUCKET: "staging-fleet-documents"
    AWS_CDN_CONTENT_URL: "https://cdn-staging.lumirental.com/"
    AWS_PEM_KEY_PATH: "/app/signing-certificate.pem"
    S3_REGION: "me-south-1"
    CAR_PRO_APP_BASE_URL: "http://carpro-staging.lumirental.com"
    CONTENT_SERVICE: "http://lumi-ultra-content-service.backend.svc.cluster.local"
    AUTHENTICATION_SERVICE_BASEURL: "http://lumi-core-auth-service.core.svc.cluster.local"
    AUTHENTICATION_SERVICE_USER_USERNAME: "lumi-fleet-service"
    CORE_AGREEMENT_SERVICE: "http://lumi-core-agreement-service.core.svc.cluster.local"
    BRANCH_SERVICE: "http://lumi-ultra-branch-service.backend.svc.cluster.local"
    USER_SERVICE: "http://lumi-core-user-service.core.svc.cluster.local"
    NEW_RELIC_AGENT_ENABLED: "false"
    NEW_RELIC_APP_NAME: "Core-Fleet-Service (PreProd)"
    NEW_RELIC_LICENSE_KEY: "2e14cd0e8b8b5c696a38296083e3da45FFFFNRAL"
    NEW_RELIC_DISTRIBUTED_TRACING_ENABLED: "false"