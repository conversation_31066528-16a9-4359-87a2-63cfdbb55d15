# Lumi Core Fleet Service

[Home page]()

Build Job : 
[*Dev*](https://jenkins.lumirental.io/job/core/job/dev/job/lumi-core-fleet-service/)  | 
[*Staging*](https://jenkins.lumirental.io/job/core/job/staging/job/lumi-core-fleet-service/) |
[*Preprod*](https://jenkins.lumirental.io/job/core/job/preprod/job/lumi-core-fleet-service/) |
[*Prod*](https://jenkins.lumirental.io/job/core/job/prod/job/lumi-core-fleet-service/)


## Table of Contents
* [About](#about)
* [API Documentation](#api-docs)
* [Server and Environments](#environments)
* [Develop](#develop)
* [Release](#release)
* [Monitor](#monitor)

## About<a id="about"/>

Lumi Core Fleet Service is a backend service built with Spring Boot that manages vehicle fleet operations for a car rental platform. It provides APIs for vehicle availability, reservation management, maintenance tracking, and fleet operations.

The service acts as the central system for managing the entire vehicle lifecycle, from onboarding to maintenance and retirement. It enables real-time tracking of vehicle status, location, and availability across multiple branches and locations.

The Fleet Service integrates with various other Lumi systems including reservation, payment, and customer services to provide a seamless rental experience. It maintains comprehensive vehicle information including specifications, features, maintenance history, and operational status.

Key responsibilities include:
- Managing real-time vehicle availability across branches
- Processing vehicle reservations and bookings
- Tracking vehicle location and movements
- Managing vehicle maintenance schedules and history
- Handling vehicle inspection reports
- Providing vehicle metadata for frontend applications
- Supporting non-revenue movements between branches
- Tracking vehicle operational status changes

The service utilizes a MySQL database for persistent storage, Redis for caching availability data, Kafka for event messaging, and ElasticSearch for advanced search capabilities.

### Features

- Vehicle availability management across branches
- Vehicle reservation and booking system
- Vehicle maintenance and inspection tracking
- Non-revenue movement (NRM) management
- Out-of-service vehicle tracking
- Vehicle metadata management (models, makes, groups, features)
- Document management for vehicles

## API Documentation<a id="api-docs"/>
[Api Details]() <br>
[Troubleshooting]()

## Servers and Environments <a id="environments"/>
For Server and Environment Details, please check [here]()

### Service Info
| Env | Endpoint                                                    |
|-----|-------------------------------------------------------------|
| Dev | https://api-dev.lumirental.com/core-fleet-service/actuator/info          |
| Staging | https://api-staging.lumirental.com/core-fleet-service/actuator/info  |
| PreProd | https://api-preprod.lumirental.com/core-fleet-service/actuator/info  |
| Prod | https://api.lumirental.com/core-fleet-service/actuator/info             |

### Service Health
| Env | Endpoint                                                            |
|-----|---------------------------------------------------------------------|
| Dev | https://api-dev.lumirental.com/core-fleet-service/actuator/health   |
| Staging | https://api-staging.lumirental.com/core-fleet-service/actuator/health |
| Preprod | https://api-preprod.lumirental.com/core-fleet-service/actuator/health |
| Prod | https://api.lumirental.com/core-fleet-service/actuator/health            |


## Develop<a id="develop"/> 
### How to build?
```
mvn clean install
```

### How to run service locally?
```
mvn spring-boot:run
```

### How to confirm that the service is running locally?

Visit: http://localhost:8099/actuator/health

### How to run service in debug mode?
```
java -Xdebug -Xrunjdwp:server=y,transport=dt_socket,address=9999,suspend=n -jar target/fleet-service-1.0.0.jar
```

### Tech Stack

- Java 21
- Spring Boot 3.4
- Spring Data JPA
- MySQL Database
- Redis for caching
- Kafka for messaging
- ElasticSearch/OpenSearch for search capabilities
- Keycloak for authentication and authorization

## Release<a id="release"/>
### Dev 
https://argo.dev.lumirental.io/applications/lumi-core-fleet-service

### Staging
https://argo.staging.lumirental.io/applications/lumi-core-fleet-service

### Preprod
https://argo.preprod.lumirental.io/applications/lumi-core-fleet-service

### Prod
https://argo.lumirental.io/applications/lumi-core-fleet-service


## Monitor<a id="monitor">

### Loki
[*Dev*](https://grafana.dev.lumirental.io/explore?schemaVersion=1&panes=%7B%22UI3%22:%7B%22datasource%22:%22Jx6g8ACnz%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bservice%3D%5C%22lumi-core-fleet-service%5C%22%7D%22,%22queryType%22:%22range%22,%22datasource%22:%7B%22type%22:%22loki%22,%22uid%22:%22Jx6g8ACnz%22%7D,%22editorMode%22:%22builder%22%7D%5D,%22range%22:%7B%22from%22:%22now-30m%22,%22to%22:%22now%22%7D%7D%7D&orgId=1) |
[*Staging*](https://grafana-staging.lumirental.io/explore?schemaVersion=1&panes=%7B%22UI3%22:%7B%22datasource%22:%22Jx6g8ACnz%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bservice%3D%5C%22lumi-core-fleet-service%5C%22%7D%22,%22queryType%22:%22range%22,%22datasource%22:%7B%22type%22:%22loki%22,%22uid%22:%22Jx6g8ACnz%22%7D,%22editorMode%22:%22builder%22%7D%5D,%22range%22:%7B%22from%22:%22now-30m%22,%22to%22:%22now%22%7D%7D%7D&orgId=1) |
[*Prod*](https://grafana.lumirental.io/explore?schemaVersion=1&panes=%7B%22cCL%22:%7B%22datasource%22:%22djVed8Cnk%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bservice%3D%5C%22lumi-core-fleet-service%5C%22%7D%22,%22queryType%22:%22range%22,%22datasource%22:%7B%22type%22:%22loki%22,%22uid%22:%22djVed8Cnk%22%7D,%22editorMode%22:%22builder%22%7D%5D,%22range%22:%7B%22from%22:%22now-15m%22,%22to%22:%22now%22%7D%7D%7D&orgId=1)


### Service Dashboard
#### Dev
[*Infra*](https://grafana.dev.lumirental.io/d/_gLbd5_izasass/deployments?orgId=1&var-job=k8s-containers-metrics&var-namespace=core&var-deployment=lumi-core-fleet-service&var-pod=All&var-Node=All&var-interval=5m) |
[*Business*](https://grafana.dev.lumirental.io/d/LJ_uJAvmg/backend-service-dashboard?orgId=1&refresh=5m&var-datasource=default&var-service=lumi-core-fleet-service.core.svc.cluster.local&var-qrep=destination&var-srccluster=All&var-srcns=All&var-srcwl=All&var-dstcluster=All&var-dstns=All&var-dstwl=All) |
[*JVM*](https://grafana.dev.lumirental.io/d/Go50wSTZk/jvm-micrometer?orgId=1&refresh=30s&var-apps=lumi-core-fleet-service&var-kubernetes_pod_name=lumi-core-fleet-service-deployment-7d97668bb8-qvdb4&var-jvm_memory_pool_heap=All&var-jvm_memory_pool_nonheap=All) |
[*Micrometer*](https://grafana.dev.lumirental.io/d/vwgu1P7Mz/micrometer?orgId=1&var-apps=lumi-core-fleet-service&var-kubernetes_pod_name=lumi-core-fleet-service-deployment-7d97668bb8-hf6n7&var-hikaricp=HikariPool-1&var-memory_pool_heap=All&var-memory_pool_nonheap=All)
#### Prod
[*Infra*](https://grafana.lumirental.io/d/bFv9Mtm7k/deployments?orgId=1&var-job=k8s-containers-metrics&var-namespace=core&var-deployment=lumi-core-fleet-service&var-pod=All&var-Node=All&var-interval=5m&from=now-1h&to=now) |
[*Business*](https://grafana.lumirental.io/d/LJ_uJAvmk/backend-service-dashboard?orgId=1&refresh=5m&var-datasource=default&var-service=lumi-core-fleet-service.core.svc.cluster.local&var-qrep=destination&var-srccluster=All&var-srcns=All&var-srcwl=All&var-dstcluster=All&var-dstns=All&var-dstwl=All) |
[*JVM*](https://grafana.lumirental.io/d/LDdwKpinz/jvm-micrometer?orgId=1&refresh=30s&var-apps=lumi-core-fleet-service&var-kubernetes_pod_name=lumi-core-fleet-service-deployment-5ddb65f54d-4kvs9&var-jvm_memory_pool_heap=All&var-jvm_memory_pool_nonheap=All) |
[*Micrometer*](https://grafana.lumirental.io/d/GtolFtink/micrometer?orgId=1&var-apps=lumi-core-fleet-service&var-kubernetes_pod_name=lumi-core-fleet-service-deployment-5ddb65f54d-4kvs9&var-hikaricp=HikariPool-1&var-memory_pool_heap=All&var-memory_pool_nonheap=All)


### Dependencies Dashboard
#### Redis
[*Dev*](https://grafana.dev.lumirental.io/d/aXPBKto6ik/redis?orgId=1&refresh=30s) |
[*Staging*]() |
[*Prod*](https://grafana.lumirental.io/d/3VBG5tink/redis?orgId=1)
#### MySql
[*Dev*](https://grafana.dev.lumirental.io/d/MQWgroiiz/mysql?orgId=1&refresh=10s) |
[*Staging*]() |
[*Prod*](https://grafana.lumirental.io/d/V-FW5tink/mysql?orgId=1&refresh=10s&var-host=mysql-lumi-ultra-prod-apps&var-interval=$__auto_interval_interval)
#### Kafka
[*Dev*](https://grafana.dev.lumirental.io/d/jwPKIsniz/kafka?orgId=1&refresh=5s) |
[*Staging*]() |
[*Prod*](https://grafana.lumirental.io/d/ceZScpinz/kafka?orgId=1&refresh=5s)